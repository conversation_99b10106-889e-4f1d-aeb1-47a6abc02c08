# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
venv/
ENV/
env/
.env

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Environment variables
.env
.env.*

# Data files (if sensitive)
data/
*.csv
*.txt
*.xlsx
*.xls

# Output files
output/
reports/
*.pdf
*.png
*.jpg
*.jpeg

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*~

# Backup files
*.bak
*.backup

# Trading specific
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.ini
settings.ini
credentials.json
api_keys.txt
