# JPM Collar Strategy - Advanced Options Trading System

## Overview

The JPM Collar Strategy is a sophisticated options trading system based on the analysis of JPMorgan's Hedged Equity Fund positioning and its impact on S&P 500 price discovery. This systematic approach leverages institutional flow patterns, advanced Greeks analysis, and market microstructure insights to generate alpha through collar spread strategies.

## Key Features

### Advanced Greeks Analysis
- **Vanna**: Sensitivity of delta to volatility changes
- **Charm**: Rate of change of delta with respect to time
- **Vomma**: Sensitivity of vega to volatility changes  
- **Zomma**: Sensitivity of gamma to volatility changes
- **Integrated Analysis**: Convergence and divergence pattern detection

### Institutional Flow Detection
- Three-phase distribution pattern recognition
- Smart Money Fragility analysis
- Gamma wall detection and pressure point identification
- Volatility regime classification

### Systematic Collar Implementation
- Dynamic collar width adjustment based on market conditions
- Quarterly expiration targeting aligned with institutional flows
- Risk-adjusted position sizing
- Comprehensive exit management

## Project Structure

```
jpm_collar_strategy/
├── src/                           # Source code modules
│   ├── config.py                 # Configuration management
│   ├── constants.py              # Trading constants and parameters
│   ├── dataloader.py             # Options data loading and preprocessing
│   ├── emini_overnight_analyzer.py # E-mini overnight drift analysis
│   └── greeks_analytics_engine.py # Advanced Greeks calculations and signal generation
├── data/                         # Data directory
│   └── SPX_COMPLETE_COMBINED.csv # Combined SPX options data
├── reports/                      # Output reports directory
├── optionshistory/               # Historical options data (quarterly files)
├── cluster_strategy_refactored.py # Main cluster strategy implementation
├── generate_performance_report.py # Performance reporting and analysis
├── requirements.txt              # Python dependencies
└── README.md                     # This file
```

## Installation

1. **Extract the project files**
   ```bash
   unzip jpm_collar_strategy.zip
   cd jpm_collar_strategy
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure parameters (optional)**
   Edit the `.env` file to adjust strategy parameters:
   ```
   INITIAL_CAPITAL=1000000
   COLLAR_WIDTH_PERCENT=0.05
   VANNA_THRESHOLD=60000
   GAMMA_THRESHOLD=80000
   VOMMA_THRESHOLD=100000000
   ```

## Usage

### Run Main Cluster Strategy
```bash
# Run the main cluster strategy backtest
python cluster_strategy_refactored.py

# This will:
# - Load options data from optionshistory/ directory
# - Calculate Greeks and identify option walls
# - Generate trading signals using E-mini overnight drift
# - Execute trades with proper risk management
# - Save results to reports/ directory
```

### Generate Performance Report
```bash
# Generate comprehensive performance report
python generate_performance_report.py

# This will:
# - Analyze the last 15 trades
# - Generate tomorrow's trading signal
# - Create performance charts and PDF report
# - Save detailed analysis to reports/ directory
```

### Custom Strategy Implementation
```python
from src.config import Config
from src.dataloader import OptionsDataLoader
from cluster_strategy_refactored import ClusterStrategy

# Initialize components
config = Config()
loader = OptionsDataLoader(config.data_path)
strategy = ClusterStrategy(start_year=2023)

# Load options data
options_data = loader.load_and_preprocess()

# Run custom backtest
results = strategy.run_backtest(options_data)
print(f"Total Return: {results['total_return']:.2f}%")
print(f"Win Rate: {results['win_rate']:.1f}%")
```

## Strategy Logic

### Cluster Strategy Overview
The cluster strategy identifies options trading opportunities by analyzing:

1. **Options Walls Detection**: Identifies significant call and put option concentrations using volume and open interest
2. **Greeks Analysis**: Calculates delta, gamma, vanna, charm, and vomma to weight option significance
3. **E-mini Overnight Drift**: Uses overnight E-mini futures price action to determine market sentiment
4. **Dynamic Position Sizing**: Adjusts position size based on signal strength and risk management

### Signal Generation
The system generates trading signals based on:

1. **Greeks Convergence Events**: When multiple extreme Greeks conditions align
2. **Distribution Phase Detection**: Three-phase institutional distribution patterns
3. **Gamma Wall Analysis**: Support/resistance levels from market maker hedging
4. **Volatility Regime Changes**: Vomma-facilitated distribution patterns

### Position Management
- **Entry**: Systematic collar spreads during distribution phases
- **Sizing**: Dynamic position sizing based on signal strength and confidence
- **Exit**: Profit targets (50%), stop losses (-200%), time decay (7 DTE)

### Risk Management
- Maximum 10% of capital per position
- Maximum 5 concurrent positions
- Commission and slippage modeling
- Comprehensive drawdown monitoring

## Performance Targets

The strategy is designed to achieve:
- **Profit Factor**: > 3.0
- **Trading Frequency**: Every 5-7 days
- **Annual Return**: > 30%
- **Sharpe Ratio**: > 1.5
- **Maximum Drawdown**: < 15%

## Output Files

### Automated Reports
- **Excel Export**: Detailed performance metrics, trade log, and analysis
- **PDF Report**: Comprehensive strategy report with current signals
- **Performance Charts**: Portfolio value, returns, drawdown, and position analysis

### Report Sections
1. **Executive Summary**: Strategy overview and key results
2. **Performance Analysis**: Return, risk, and trading metrics
3. **Current Trading Signals**: Latest Greeks analysis and recommendations
4. **Risk Analysis**: Comprehensive risk management framework
5. **Trade Analysis**: Detailed trade-by-trade breakdown
6. **Strategy Insights**: Optimization recommendations and market regime analysis

## Configuration Parameters

### Trading Parameters
- `INITIAL_CAPITAL`: Starting capital amount
- `COLLAR_WIDTH_PERCENT`: Base collar width (default: 5%)
- `MAX_POSITION_SIZE`: Maximum position size as % of capital
- `COMMISSION_PER_CONTRACT`: Commission cost per options contract
- `SLIPPAGE_BPS`: Slippage in basis points

### Signal Thresholds
- `VANNA_THRESHOLD`: Vanna exposure threshold for signal generation
- `GAMMA_THRESHOLD`: Gamma exposure threshold for signal generation  
- `VOMMA_THRESHOLD`: Vomma exposure threshold for signal generation

### Risk Management
- `RISK_FREE_RATE`: Risk-free rate for Sharpe ratio calculation
- `REBALANCE_FREQUENCY`: Strategy rebalancing frequency

## Data Requirements

### Input Data Format
The system expects SPX options data with the following columns:
- `date`: Trading date
- `strike`: Option strike price
- `expiration`: Option expiration date
- `option_type`: 'c' for calls, 'p' for puts
- `price`: Option price
- `bid`: Bid price
- `ask`: Ask price
- `volume`: Trading volume
- `open_interest`: Open interest
- `delta`: Delta Greek
- `gamma`: Gamma Greek
- `theta`: Theta Greek
- `vega`: Vega Greek
- `rho`: Rho Greek

### Data Sources
- Professional options data vendors (Bloomberg, Refinitiv)
- Broker APIs with options Greeks
- Historical options databases

## Advanced Features

### Greeks Calculation
The system calculates higher-order Greeks:
```python
# Vanna: sensitivity of delta to volatility
vanna = delta * vega / 100

# Charm: rate of change of delta with time
charm = theta * delta

# Vomma: sensitivity of vega to volatility
vomma = vega * vega / 100

# Zomma: sensitivity of gamma to volatility
zomma = gamma * vega / 100
```

### Distribution Phase Detection
```python
# Phase 1: Volume explosion (400-700% increase)
# Phase 2: Peak distribution (extreme Greeks convergence)
# Phase 3: Volume collapse (80-95% decrease)
```

### Gamma Wall Detection
```python
# Identify strikes with high Gamma × Open Interest
# Find nearest walls to current price
# Calculate wall strength and distance
```

## Troubleshooting

### Common Issues

1. **No trades generated**: Check signal thresholds in `.env` file
2. **Data loading errors**: Verify data file path and format
3. **Memory issues**: Reduce date range for large datasets
4. **Permission errors**: Use `pip install --user` for dependencies

### Debug Mode
Enable detailed logging by setting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Disclaimer

This trading system is for educational and research purposes only. Options trading involves substantial risk and is not suitable for all investors. Past performance does not guarantee future results. Please consult with a qualified financial advisor before implementing any trading strategy.

## Support

For technical support or questions about the implementation, please refer to the comprehensive documentation in the generated PDF reports or review the inline code comments for detailed explanations of the algorithms and methodologies.

## License

This project is provided as-is for educational purposes. Commercial use requires appropriate licensing and risk disclosures.

