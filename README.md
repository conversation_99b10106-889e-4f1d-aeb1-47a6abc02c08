# JPM Call Spread Strategy

Professional-grade quantitative call spread options trading strategy using real SPX market data with balanced risk management and enhanced position sizing.

## 🚀 **STRATEGY OVERVIEW**

### **Call Spread Strategy** (Two-Leg Options)
- **Performance**: +1,276% return, 60.7% win rate, 24.9% max drawdown
- **Strategy**: Sell closer-to-money calls, buy further OTM calls for net credit
- **Risk**: Defined risk with maximum loss = spread width - credit received
- **Trades**: 84 trades, 15.5 avg contracts, $25,844 avg win
- **File**: `call_spread_strategy.py`

## 📊 **KEY FEATURES**

### **Risk Management Improvements**
- **Drawdown Reduction**: Cut maximum drawdown from 48.8% to 24.9% (50% improvement)
- **Balanced Strike Selection**: Moderate distances from money for optimal risk/reward
- **Conservative Position Sizing**: 8-20 contracts based on confidence levels
- **Tighter Spreads**: Maximum 75-point spread width for better risk control

### **Position Tracking Excellence**
- **Real Market Data**: 100% real exit prices from actual SPX options data
- **Proper Position Lifecycle**: Entry → Track → Advance → Revalue
- **No Artificial Clustering**: All exit prices from genuine market data
- **Enhanced Revaluation**: Multiple approaches for finding exit prices

### **Strategy Mechanics**
- **Entry**: Find actual call options in market data on signal date
- **Short Leg**: Sell closer-to-money call (2% ITM to 4% OTM)
- **Long Leg**: Buy further OTM call (1% to 5% OTM)
- **Exit**: Revalue both legs using real market data after 1-day holding period
- **Credit Requirement**: Minimum 20% of spread width for good risk/reward

## 🎯 **PERFORMANCE METRICS**

| Metric | Value | Achievement |
|--------|-------|-------------|
| **Total Return** | **1,276%** | 12.8x growth |
| **Win Rate** | **60.7%** | Excellent for spreads |
| **Max Drawdown** | **24.9%** | 50% improvement |
| **Total Trades** | **84** | High-quality selection |
| **Profit Factor** | **30.99** | Outstanding |
| **Avg Spread Width** | **75 points** | Balanced risk |
| **Avg Net Credit** | **$48.62** | Good premium collection |

## 🔧 **TECHNICAL IMPLEMENTATION**

### **Data Sources**
- **SPX Options Data**: 385,063 real market records from 63 quarterly files
- **VIX Data**: 538 daily observations for volatility regime analysis
- **Date Range**: 2023-05-01 to 2025-07-14 (auto-updated)
- **No Forward-Looking Bias**: All calculations use historical data only

### **Signal Generation**
- **VRP Analysis**: Volatility Risk Premium from real SPX options data
- **VIX Regimes**: Multiple volatility environments for signal quality
- **Technical Filters**: RSI and moving average confirmations
- **Quality Control**: 190 signals generated, 84 executed successfully

### **Position Management**
- **Entry Logic**: Find suitable call spreads meeting all criteria
- **Strike Selection**: Balanced approach for optimal risk/reward
- **Position Sizing**: Confidence-based allocation (8-20 contracts)
- **Exit Logic**: Real market data revaluation after holding period

## 🚀 **QUICK START**

### **Prerequisites**
```bash
# Install required packages
pip install pandas numpy matplotlib seaborn reportlab openai

# Set up environment variables
cp .env.example .env
# Edit .env with your OpenAI API key for PDF narratives
```

### **Run Strategy**
```bash
# Execute automated strategy
./run_strategies.sh

# Or run directly
python3 call_spread_strategy.py
```

### **Generated Outputs**
- **Trade Data**: `trades/call_spread_trades.csv`
- **PDF Report**: `reports/Call_Spread_Strategy_Report_[timestamp].pdf`
- **Charts**: Equity curves and performance analysis

## 📁 **FILE STRUCTURE**

```
jpm_collar_strategy/
├── call_spread_strategy.py          # Main strategy implementation
├── call_spread_pdf_generator.py     # PDF report generation
├── run_strategies.sh                # Automated execution script
├── requirements.txt                 # Python dependencies
├── .env                            # Environment variables
├── trades/                         # Generated trade data
│   └── call_spread_trades.csv
├── reports/                        # Generated PDF reports
└── optionshistory/                 # SPX options data (quarterly files)
```

## 🎯 **STRATEGY ADVANTAGES**

### **Risk Management**
- **Defined Risk**: Maximum loss is known at entry
- **Credit Collection**: Receive premium upfront
- **Balanced Approach**: Not too conservative, not too aggressive
- **Drawdown Control**: Significant improvement in risk metrics

### **Market Data Integrity**
- **Real Prices**: All exit prices from actual market data
- **No Simulation**: Uses genuine SPX options historical data
- **Proper Tracking**: Position lifecycle management like real trading
- **Data Validation**: Comprehensive checks for data quality

### **Professional Implementation**
- **Institutional Sizing**: 8-20 contracts per trade
- **Quality Selection**: High-probability setups only
- **Automated Execution**: Complete end-to-end automation
- **Comprehensive Reporting**: Detailed PDF reports with narratives

## 📊 **RISK DISCLOSURE**

This strategy is for educational and research purposes. Past performance does not guarantee future results. Options trading involves substantial risk and may not be suitable for all investors. The maximum loss for call spreads is the spread width minus the credit received. Always consult with a qualified financial advisor before implementing any trading strategy.

## 🔧 **SUPPORT**

For questions or issues:
1. Check the generated PDF reports for detailed analysis
2. Review trade data in CSV format for transparency
3. Examine the strategy code for implementation details
4. Ensure all data files are properly loaded

---

**Professional Options Trading Strategy - Real Market Data Implementation**
