#!/usr/bin/env python3
"""
Analyze 2024 drawdown period to identify patterns and suggest improvements
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt

def analyze_2024_drawdown():
    """Analyze the 2024 drawdown period"""
    
    print("🔍 ANALYZING 2024 DRAWDOWN PERIOD")
    print("=" * 60)
    
    # Load trades data
    trades_df = pd.read_csv('trades/trades_analysis.csv')
    trades_df['signal_date'] = pd.to_datetime(trades_df['signal_date'])
    trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
    trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
    
    # Filter for 2024 trades
    trades_2024 = trades_df[trades_df['signal_date'].dt.year == 2024].copy()
    
    print(f"📊 Total 2024 trades: {len(trades_2024)}")
    
    # Analyze win/loss patterns
    wins_2024 = trades_2024[trades_2024['win_loss_flag'] == True]
    losses_2024 = trades_2024[trades_2024['win_loss_flag'] == False]
    
    print(f"✅ Wins: {len(wins_2024)} ({len(wins_2024)/len(trades_2024)*100:.1f}%)")
    print(f"❌ Losses: {len(losses_2024)} ({len(losses_2024)/len(trades_2024)*100:.1f}%)")
    
    # Analyze by month
    print("\n📅 MONTHLY BREAKDOWN:")
    monthly_stats = trades_2024.groupby(trades_2024['signal_date'].dt.month).agg({
        'net_pnl': ['sum', 'count'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    for month in range(1, 13):
        month_trades = trades_2024[trades_2024['signal_date'].dt.month == month]
        if len(month_trades) > 0:
            month_pnl = month_trades['net_pnl'].sum()
            month_wins = month_trades['win_loss_flag'].mean() * 100
            print(f"   {month:2d}/2024: {len(month_trades):3d} trades, ${month_pnl:8,.0f} P&L, {month_wins:4.1f}% wins")
    
    # Analyze by signal source
    print("\n🎯 BY SIGNAL SOURCE:")
    source_stats = trades_2024.groupby('signal_source').agg({
        'net_pnl': ['sum', 'count'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    for source in trades_2024['signal_source'].unique():
        source_trades = trades_2024[trades_2024['signal_source'] == source]
        source_pnl = source_trades['net_pnl'].sum()
        source_wins = source_trades['win_loss_flag'].mean() * 100
        print(f"   {source}: {len(source_trades):3d} trades, ${source_pnl:8,.0f} P&L, {source_wins:4.1f}% wins")
    
    # Analyze by VIX level
    print("\n📈 BY VIX LEVEL:")
    trades_2024['vix_bucket'] = pd.cut(trades_2024['vix_level'], 
                                      bins=[0, 15, 20, 25, 100], 
                                      labels=['Low (<15)', 'Normal (15-20)', 'High (20-25)', 'Very High (>25)'])
    
    for bucket in trades_2024['vix_bucket'].cat.categories:
        bucket_trades = trades_2024[trades_2024['vix_bucket'] == bucket]
        if len(bucket_trades) > 0:
            bucket_pnl = bucket_trades['net_pnl'].sum()
            bucket_wins = bucket_trades['win_loss_flag'].mean() * 100
            print(f"   {bucket}: {len(bucket_trades):3d} trades, ${bucket_pnl:8,.0f} P&L, {bucket_wins:4.1f}% wins")
    
    # Analyze by option type
    print("\n📊 BY OPTION TYPE:")
    for opt_type in trades_2024['option_type'].unique():
        type_trades = trades_2024[trades_2024['option_type'] == opt_type]
        type_pnl = type_trades['net_pnl'].sum()
        type_wins = type_trades['win_loss_flag'].mean() * 100
        print(f"   {opt_type}: {len(type_trades):3d} trades, ${type_pnl:8,.0f} P&L, {type_wins:4.1f}% wins")
    
    # Identify worst losing streaks
    print("\n💔 WORST LOSING PERIODS:")
    trades_2024_sorted = trades_2024.sort_values('signal_date')
    
    # Calculate cumulative P&L
    trades_2024_sorted['cumulative_pnl'] = trades_2024_sorted['net_pnl'].cumsum()
    
    # Find drawdown periods
    trades_2024_sorted['running_max'] = trades_2024_sorted['cumulative_pnl'].expanding().max()
    trades_2024_sorted['drawdown'] = trades_2024_sorted['cumulative_pnl'] - trades_2024_sorted['running_max']
    
    # Find worst drawdown
    worst_dd_idx = trades_2024_sorted['drawdown'].idxmin()
    worst_dd_value = trades_2024_sorted.loc[worst_dd_idx, 'drawdown']
    worst_dd_date = trades_2024_sorted.loc[worst_dd_idx, 'signal_date']
    
    print(f"   Worst drawdown: ${worst_dd_value:,.0f} on {worst_dd_date.strftime('%Y-%m-%d')}")
    
    # Analyze losing streaks
    trades_2024_sorted['loss_streak'] = 0
    current_streak = 0
    
    for idx, row in trades_2024_sorted.iterrows():
        if not row['win_loss_flag']:
            current_streak += 1
        else:
            current_streak = 0
        trades_2024_sorted.loc[idx, 'loss_streak'] = current_streak
    
    max_loss_streak = trades_2024_sorted['loss_streak'].max()
    print(f"   Longest losing streak: {max_loss_streak} trades")
    
    # Analyze strike selection issues
    print("\n🎯 STRIKE ANALYSIS:")
    print(f"   Average losing trade strike: {losses_2024['strike_price'].mean():.0f}")
    print(f"   Average winning trade strike: {wins_2024['strike_price'].mean():.0f}")
    
    return trades_2024

def suggest_improvements(trades_2024):
    """Suggest improvements based on 2024 analysis"""
    
    print("\n🔧 SUGGESTED IMPROVEMENTS:")
    print("=" * 60)
    
    # 1. VIX filtering
    high_vix_losses = trades_2024[(trades_2024['vix_level'] > 18) & (trades_2024['win_loss_flag'] == False)]
    if len(high_vix_losses) > 10:
        print("1. 🚫 VIX FILTERING:")
        print(f"   - Skip trades when VIX > 18 (found {len(high_vix_losses)} losses)")
        print(f"   - Potential savings: ${high_vix_losses['net_pnl'].sum():,.0f}")
    
    # 2. Signal source filtering
    vrp_losses = trades_2024[(trades_2024['signal_source'] == 'VRP_REVERSED') & (trades_2024['win_loss_flag'] == False)]
    orig_losses = trades_2024[(trades_2024['signal_source'] == 'Original') & (trades_2024['win_loss_flag'] == False)]
    
    print("\n2. 📊 SIGNAL SOURCE OPTIMIZATION:")
    if len(vrp_losses) > len(orig_losses):
        print(f"   - VRP_REVERSED has more losses: {len(vrp_losses)} vs {len(orig_losses)}")
        print(f"   - Consider reducing VRP trade frequency")
    
    # 3. Seasonal patterns
    q1_trades = trades_2024[trades_2024['signal_date'].dt.quarter == 1]
    q1_performance = q1_trades['net_pnl'].sum()
    
    if q1_performance < -50000:
        print("\n3. 📅 SEASONAL FILTERING:")
        print(f"   - Q1 2024 performance: ${q1_performance:,.0f}")
        print(f"   - Consider reducing position sizes in Q1")
    
    # 4. Strike optimization
    print("\n4. 🎯 STRIKE OPTIMIZATION:")
    print(f"   - Average losing strike: {trades_2024[trades_2024['win_loss_flag']==False]['strike_price'].mean():.0f}")
    print(f"   - Average winning strike: {trades_2024[trades_2024['win_loss_flag']==True]['strike_price'].mean():.0f}")
    print(f"   - Consider dynamic strike selection based on VIX level")
    
    # 5. Position sizing
    large_losses = trades_2024[trades_2024['net_pnl'] < -2000]
    if len(large_losses) > 5:
        print("\n5. 💰 POSITION SIZING:")
        print(f"   - {len(large_losses)} trades lost >$2,000")
        print(f"   - Consider dynamic position sizing based on recent performance")

if __name__ == "__main__":
    trades_2024 = analyze_2024_drawdown()
    suggest_improvements(trades_2024)
