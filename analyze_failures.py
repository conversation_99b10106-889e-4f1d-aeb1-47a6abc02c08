#!/usr/bin/env python3

import pandas as pd
import sys
from datetime import datetime, timedelta

print("🔍 ANALYZING WHY 188/190 TRADES FAILED")
print("=" * 50)

# Load the strategy
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()

print(f"✅ Loaded options data: {len(strategy.spx_options_data):,} records")

# Let's analyze the failure patterns from the debug output
print("\n📊 FAILURE PATTERN ANALYSIS:")

# Pattern 1: Entry failures - can't find both legs
print("\n1. ENTRY FAILURES (can't find both legs):")
print("   Debug: SPX 4377, target short 4375, long 4525")
print("   Short matches: 0, Long matches: 1")
print("   → SHORT LEG MISSING")

print("\n   Debug: SPX 4472, target short 4475, long 4625") 
print("   Short matches: 1, Long matches: 0")
print("   → LONG LEG MISSING")

# Let's check what strikes are actually available
print("\n🔍 CHECKING ACTUAL STRIKE AVAILABILITY:")

# Check a specific date that failed
test_date = pd.to_datetime('2023-06-28')
day_options = strategy.spx_options_data[
    (strategy.spx_options_data['date'] == test_date) &
    (strategy.spx_options_data['Call/Put'] == 'c')
]

print(f"\n📅 OPTIONS AVAILABLE ON {test_date.date()}:")
print(f"   Total call options: {len(day_options)}")

if len(day_options) > 0:
    # Check expiry filtering
    day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days
    valid_dte = day_options[day_options['days_to_expiry'] >= 25]
    
    print(f"   Options with 25+ DTE: {len(valid_dte)}")
    
    if len(valid_dte) > 0:
        print(f"   Strike range: {valid_dte['Strike'].min():.0f} - {valid_dte['Strike'].max():.0f}")
        
        # Check specific strikes that failed
        strikes_to_check = [4375, 4525, 4475, 4625]
        for strike in strikes_to_check:
            matches = valid_dte[valid_dte['Strike'] == strike]
            print(f"   Strike {strike}: {len(matches)} matches")
            if len(matches) > 0:
                print(f"      Expiries: {[d.strftime('%Y-%m-%d') for d in matches['expiry_date'].unique()]}")
                print(f"      Prices: ${matches['Last Trade Price'].min():.2f} - ${matches['Last Trade Price'].max():.2f}")

# Pattern 2: Exit failures - can't find same option 3 days later
print("\n\n2. EXIT FAILURES (can't find same option 3 days later):")

# Check if the successful trades had their options available on exit dates
trades_df = pd.read_csv('trades/call_spread_trades.csv')
if len(trades_df) > 0:
    for i, trade in trades_df.iterrows():
        print(f"\n   SUCCESSFUL TRADE {i+1}:")
        print(f"   Entry: {trade['entry_date']}, Exit: {trade['exit_date']}")
        print(f"   Short: {trade['short_strike']}, Long: {trade['long_strike']}")
        
        # Check if these exact options exist on exit date
        exit_date = pd.to_datetime(trade['exit_date'])
        short_expiry = pd.to_datetime(trade['short_expiry'])
        long_expiry = pd.to_datetime(trade['long_expiry'])
        
        # Short leg on exit date
        exit_short = strategy.spx_options_data[
            (strategy.spx_options_data['date'] == exit_date) &
            (strategy.spx_options_data['Strike'] == trade['short_strike']) &
            (strategy.spx_options_data['expiry_date'] == short_expiry) &
            (strategy.spx_options_data['Call/Put'] == 'c')
        ]
        
        # Long leg on exit date  
        exit_long = strategy.spx_options_data[
            (strategy.spx_options_data['date'] == exit_date) &
            (strategy.spx_options_data['Strike'] == trade['long_strike']) &
            (strategy.spx_options_data['expiry_date'] == long_expiry) &
            (strategy.spx_options_data['Call/Put'] == 'c')
        ]
        
        print(f"   Exit availability: Short {len(exit_short)}, Long {len(exit_long)}")

# Pattern 3: Check if it's a weekend/holiday issue
print("\n\n3. WEEKEND/HOLIDAY ANALYSIS:")

failed_dates = [
    '2023-06-17', '2023-06-25', '2023-07-16', '2023-07-29',
    '2023-08-13', '2023-08-17', '2023-08-24', '2023-08-25'
]

for date_str in failed_dates[:5]:  # Check first 5
    date = pd.to_datetime(date_str)
    weekday = date.strftime('%A')
    
    # Check if any options exist on this date
    day_options = strategy.spx_options_data[strategy.spx_options_data['date'] == date]
    
    print(f"   {date_str} ({weekday}): {len(day_options)} total options")

print("\n🎯 HYPOTHESIS:")
print("   The failures are likely due to:")
print("   1. Missing strikes in the historical data")
print("   2. Weekend/holiday dates with no trading")
print("   3. Overly restrictive DTE filtering")
print("   4. Data gaps in the options dataset")
print("\n   The strategy logic is correct, but the data coverage")
print("   is incomplete for the specific strikes and dates needed.")
