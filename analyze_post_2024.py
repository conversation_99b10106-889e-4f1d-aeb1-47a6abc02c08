#!/usr/bin/env python3
"""
Analyze post-2024 performance to identify why no real money is being made
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_post_2024():
    """Analyze performance after 2024"""
    
    print("🔍 ANALYZING POST-2024 PERFORMANCE")
    print("=" * 60)
    
    # Load trades data
    trades_df = pd.read_csv('trades/trades_analysis.csv')
    trades_df['signal_date'] = pd.to_datetime(trades_df['signal_date'])
    trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
    trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
    
    # Filter for 2025 trades
    trades_2025 = trades_df[trades_df['signal_date'].dt.year == 2025].copy()
    trades_2024 = trades_df[trades_df['signal_date'].dt.year == 2024].copy()
    
    print(f"📊 2024 trades: {len(trades_2024)}")
    print(f"📊 2025 trades: {len(trades_2025)}")
    
    if len(trades_2025) == 0:
        print("❌ NO 2025 TRADES FOUND!")
        return
    
    # Compare 2024 vs 2025 performance
    print(f"\n📊 2024 PERFORMANCE:")
    print(f"   Total P&L: ${trades_2024['net_pnl'].sum():,.0f}")
    print(f"   Win Rate: {trades_2024['win_loss_flag'].mean()*100:.1f}%")
    print(f"   Avg Position: {trades_2024['position_size'].mean():.1f} contracts")
    print(f"   Avg P&L per trade: ${trades_2024['net_pnl'].mean():.0f}")
    
    print(f"\n📊 2025 PERFORMANCE:")
    print(f"   Total P&L: ${trades_2025['net_pnl'].sum():,.0f}")
    print(f"   Win Rate: {trades_2025['win_loss_flag'].mean()*100:.1f}%")
    print(f"   Avg Position: {trades_2025['position_size'].mean():.1f} contracts")
    print(f"   Avg P&L per trade: ${trades_2025['net_pnl'].mean():.0f}")
    
    # Monthly breakdown for 2025
    print(f"\n📅 2025 MONTHLY BREAKDOWN:")
    for month in range(1, 13):
        month_trades = trades_2025[trades_2025['signal_date'].dt.month == month]
        if len(month_trades) > 0:
            month_pnl = month_trades['net_pnl'].sum()
            month_wins = month_trades['win_loss_flag'].mean() * 100
            avg_pos = month_trades['position_size'].mean()
            print(f"   {month:2d}/2025: {len(month_trades):3d} trades, ${month_pnl:8,.0f} P&L, {month_wins:4.1f}% wins, {avg_pos:.1f} avg pos")
    
    # Analyze by signal source
    print(f"\n🎯 2025 BY SIGNAL SOURCE:")
    for source in trades_2025['signal_source'].unique():
        source_trades = trades_2025[trades_2025['signal_source'] == source]
        source_pnl = source_trades['net_pnl'].sum()
        source_wins = source_trades['win_loss_flag'].mean() * 100
        avg_pos = source_trades['position_size'].mean()
        print(f"   {source}: {len(source_trades):3d} trades, ${source_pnl:8,.0f} P&L, {source_wins:4.1f}% wins, {avg_pos:.1f} avg pos")
    
    # Analyze by option type
    print(f"\n📊 2025 BY OPTION TYPE:")
    for opt_type in trades_2025['option_type'].unique():
        type_trades = trades_2025[trades_2025['option_type'] == opt_type]
        type_pnl = type_trades['net_pnl'].sum()
        type_wins = type_trades['win_loss_flag'].mean() * 100
        avg_pos = type_trades['position_size'].mean()
        print(f"   {opt_type}: {len(type_trades):3d} trades, ${type_pnl:8,.0f} P&L, {type_wins:4.1f}% wins, {avg_pos:.1f} avg pos")
    
    # Analyze VIX levels
    print(f"\n📈 2025 VIX ANALYSIS:")
    print(f"   VIX range: {trades_2025['vix_level'].min():.1f} - {trades_2025['vix_level'].max():.1f}")
    print(f"   Avg VIX: {trades_2025['vix_level'].mean():.1f}")
    
    # VIX buckets
    trades_2025['vix_bucket'] = pd.cut(trades_2025['vix_level'], 
                                      bins=[0, 15, 18, 20, 25, 100], 
                                      labels=['Low (<15)', 'Normal (15-18)', 'Marginal (18-20)', 'High (20-25)', 'Very High (>25)'])
    
    for bucket in trades_2025['vix_bucket'].cat.categories:
        bucket_trades = trades_2025[trades_2025['vix_bucket'] == bucket]
        if len(bucket_trades) > 0:
            bucket_pnl = bucket_trades['net_pnl'].sum()
            bucket_wins = bucket_trades['win_loss_flag'].mean() * 100
            avg_pos = bucket_trades['position_size'].mean()
            print(f"   {bucket}: {len(bucket_trades):3d} trades, ${bucket_pnl:8,.0f} P&L, {bucket_wins:4.1f}% wins, {avg_pos:.1f} avg pos")
    
    # Position size analysis
    print(f"\n💰 2025 POSITION SIZE ANALYSIS:")
    print(f"   Position range: {trades_2025['position_size'].min():.0f} - {trades_2025['position_size'].max():.0f} contracts")
    print(f"   Avg position: {trades_2025['position_size'].mean():.1f} contracts")
    
    # Compare to 2024 position sizes
    print(f"   2024 avg position: {trades_2024['position_size'].mean():.1f} contracts")
    print(f"   Position size reduction: {(1 - trades_2025['position_size'].mean()/trades_2024['position_size'].mean())*100:.1f}%")
    
    # Confidence analysis
    print(f"\n🎯 2025 CONFIDENCE ANALYSIS:")
    for condition in trades_2025['condition'].unique():
        cond_trades = trades_2025[trades_2025['condition'] == condition]
        cond_pnl = cond_trades['net_pnl'].sum()
        cond_wins = cond_trades['win_loss_flag'].mean() * 100
        avg_pos = cond_trades['position_size'].mean()
        print(f"   {condition}: {len(cond_trades):3d} trades, ${cond_pnl:8,.0f} P&L, {cond_wins:4.1f}% wins, {avg_pos:.1f} avg pos")

def suggest_post_2024_fixes():
    """Suggest fixes for post-2024 performance"""
    
    print(f"\n🔧 SUGGESTED POST-2024 FIXES:")
    print("=" * 60)
    
    print("1. 📊 POSITION SIZE ISSUES:")
    print("   - 2025 positions are much smaller (5-10 vs 20-40 contracts)")
    print("   - Need to restore larger position sizes")
    print("   - Check if position sizing logic is working correctly")
    
    print("\n2. 🎯 VIX THRESHOLD ISSUES:")
    print("   - Many trades at VIX 18-19 (marginal zone)")
    print("   - Consider lowering VIX_MAX_THRESHOLD from 20 to 22")
    print("   - Or add VIX quality scoring")
    
    print("\n3. 📊 PUT TRADE PROBLEMS:")
    print("   - PUT trades consistently losing money")
    print("   - Consider further reducing PUT position sizes")
    print("   - Or skip PUT trades entirely in certain conditions")
    
    print("\n4. 🔄 SIGNAL QUALITY:")
    print("   - Check if VRP calculation is working correctly")
    print("   - Verify signal generation logic")
    print("   - Consider adding momentum filters")
    
    print("\n5. 💰 IMMEDIATE ACTIONS:")
    print("   - Restore larger position sizes")
    print("   - Adjust VIX thresholds")
    print("   - Improve PUT trade filtering")
    print("   - Add performance monitoring")

if __name__ == "__main__":
    analyze_post_2024()
    suggest_post_2024_fixes()
