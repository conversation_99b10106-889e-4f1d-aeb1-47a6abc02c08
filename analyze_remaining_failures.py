#!/usr/bin/env python3

import pandas as pd
import sys
from datetime import datetime, timedelta

print("🔍 ANALYZING REMAINING 118 FAILED TRADES")
print("=" * 50)

# Load the strategy
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()

print(f"✅ Loaded options data: {len(strategy.spx_options_data):,} records")

# Analyze the failure patterns from the recent output
print("\n📊 FAILURE PATTERN ANALYSIS:")

# Pattern 1: Entry failures - missing strikes
entry_failures = [
    ("2023-06-17", 4375, 4525, "Short matches: 0, Long matches: 1"),
    ("2023-07-14", 4475, 4625, "Short matches: 1, Long matches: 0"),
    ("2023-08-31", 4425, 4575, "Short matches: 0, Long matches: 1"),
    ("2023-09-05", 4525, 4675, "Short matches: 0, Long matches: 0"),
    ("2024-10-21", 5850, 6000, "target short 5850, no strikes within 25 points"),
]

print("\n1. ENTRY FAILURES - Missing Strikes:")
for date_str, short, long, debug in entry_failures:
    print(f"   {date_str}: Short {short}, Long {long} - {debug}")

# Pattern 2: Exit failures - can't find same option on exit date
exit_failures = [
    ("2023-06-20", 4375, 4525),
    ("2023-07-03", 4375, 4525),
    ("2023-07-17", 4500, 4650),
    ("2023-07-31", 4575, 4725),
]

print("\n2. EXIT FAILURES - Can't find same option on exit:")
for date_str, short, long in exit_failures:
    print(f"   {date_str}: Short {short}, Long {long}")

# Let's check what's actually available for some failed cases
print("\n🔍 DETAILED ANALYSIS OF FAILED CASES:")

# Check a specific failed entry case
failed_date = pd.to_datetime('2023-06-17')
print(f"\n📅 CHECKING {failed_date.date()} (Saturday - should be moved to Monday):")

# Check if weekend handling worked
monday_date = failed_date
while monday_date.weekday() >= 5:
    monday_date += timedelta(days=1)

print(f"   Weekend adjustment: {failed_date.date()} → {monday_date.date()}")

day_options = strategy.spx_options_data[
    (strategy.spx_options_data['date'] == monday_date) &
    (strategy.spx_options_data['Call/Put'] == 'c')
]

print(f"   Options available on {monday_date.date()}: {len(day_options)}")

if len(day_options) > 0:
    # Check DTE filtering
    day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days
    valid_dte = day_options[day_options['days_to_expiry'] >= 25]
    
    print(f"   Options with 25+ DTE: {len(valid_dte)}")
    
    if len(valid_dte) > 0:
        print(f"   Strike range: {valid_dte['Strike'].min():.0f} - {valid_dte['Strike'].max():.0f}")
        
        # Check for strikes around 4375 and 4525
        target_short = 4375
        target_long = 4525
        
        # Check exact strikes
        exact_short = valid_dte[valid_dte['Strike'] == target_short]
        exact_long = valid_dte[valid_dte['Strike'] == target_long]
        
        print(f"   Exact strike {target_short}: {len(exact_short)} matches")
        print(f"   Exact strike {target_long}: {len(exact_long)} matches")
        
        # Check within 25 points
        short_range = valid_dte[
            (valid_dte['Strike'] >= target_short - 25) &
            (valid_dte['Strike'] <= target_short + 25)
        ]
        long_range = valid_dte[
            (valid_dte['Strike'] >= target_long - 25) &
            (valid_dte['Strike'] <= target_long + 25)
        ]
        
        print(f"   Short range ({target_short-25}-{target_short+25}): {len(short_range)} matches")
        if len(short_range) > 0:
            print(f"      Available strikes: {sorted(short_range['Strike'].unique())}")
        
        print(f"   Long range ({target_long-25}-{target_long+25}): {len(long_range)} matches")
        if len(long_range) > 0:
            print(f"      Available strikes: {sorted(long_range['Strike'].unique())}")

# Check a "no strikes within 25 points" case
print(f"\n📅 CHECKING 'NO STRIKES WITHIN 25 POINTS' CASE:")
failed_date2 = pd.to_datetime('2024-10-21')
day_options2 = strategy.spx_options_data[
    (strategy.spx_options_data['date'] == failed_date2) &
    (strategy.spx_options_data['Call/Put'] == 'c')
]

print(f"   Options available on {failed_date2.date()}: {len(day_options2)}")

if len(day_options2) > 0:
    day_options2['days_to_expiry'] = (day_options2['expiry_date'] - day_options2['date']).dt.days
    valid_dte2 = day_options2[day_options2['days_to_expiry'] >= 25]
    
    print(f"   Options with 25+ DTE: {len(valid_dte2)}")
    
    if len(valid_dte2) > 0:
        print(f"   Strike range: {valid_dte2['Strike'].min():.0f} - {valid_dte2['Strike'].max():.0f}")
        
        target_short2 = 5850
        short_range2 = valid_dte2[
            (valid_dte2['Strike'] >= target_short2 - 25) &
            (valid_dte2['Strike'] <= target_short2 + 25)
        ]
        
        print(f"   Short range ({target_short2-25}-{target_short2+25}): {len(short_range2)} matches")
        if len(short_range2) > 0:
            print(f"      Available strikes: {sorted(short_range2['Strike'].unique())}")
        else:
            # Check wider range
            wider_range = valid_dte2[
                (valid_dte2['Strike'] >= target_short2 - 50) &
                (valid_dte2['Strike'] <= target_short2 + 50)
            ]
            print(f"   Wider range ({target_short2-50}-{target_short2+50}): {len(wider_range)} matches")
            if len(wider_range) > 0:
                print(f"      Available strikes: {sorted(wider_range['Strike'].unique())}")

print("\n🎯 POTENTIAL FIXES:")
print("   1. Expand strike search range from ±25 to ±50 points")
print("   2. Improve weekend/holiday handling for exit dates")
print("   3. Allow wider spread widths (100-200 points instead of exactly 150)")
print("   4. Add fallback logic for missing strikes")
print("   5. Check for data gaps and fill with nearby dates")
