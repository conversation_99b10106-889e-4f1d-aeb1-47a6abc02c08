#!/usr/bin/env python3
"""
Analyze VIX filtering to understand why no trades are being generated
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pure_vix_options_strategy import EnhancedVIXOptionsStrategyV3
from constants import *

def analyze_vix_filtering():
    """Analyze VIX filtering during test period"""
    
    print("🔍 ANALYZING VIX FILTERING")
    print("=" * 50)
    
    # Initialize strategy
    strategy = EnhancedVIXOptionsStrategyV3()
    
    # Load data for analysis period
    strategy.options_loader.load_all_available_data(2012, 2012)
    strategy.load_market_data()
    
    # Analyze VIX data for test period
    test_start = datetime(2012, 1, 3)
    test_end = datetime(2012, 6, 30)
    
    print(f"📅 Test Period: {test_start.strftime('%Y-%m-%d')} to {test_end.strftime('%Y-%m-%d')}")
    print(f"📊 VIX Data Points: {len(strategy.vix_data)}")
    
    # Filter VIX data for test period
    test_vix = strategy.vix_data[
        (strategy.vix_data['date'] >= test_start) & 
        (strategy.vix_data['date'] <= test_end)
    ].copy()
    
    if len(test_vix) == 0:
        print("❌ No VIX data found for test period")
        return
    
    print(f"📈 VIX Data in Test Period: {len(test_vix)} observations")
    print(f"📊 VIX Range: {test_vix['VIX'].min():.1f} - {test_vix['VIX'].max():.1f}")
    print(f"📊 VIX Mean: {test_vix['VIX'].mean():.1f}")
    print(f"📊 VIX Std: {test_vix['VIX'].std():.1f}")
    
    # Analyze VIX regimes
    print(f"\n🎯 VIX REGIME ANALYSIS:")
    print(f"Current Thresholds:")
    print(f"  - Low VIX: < {VIX_OPTIMAL_LOW}")
    print(f"  - Optimal VIX: {VIX_OPTIMAL_LOW} - {VIX_OPTIMAL_HIGH}")
    print(f"  - High VIX: > {VIX_OPTIMAL_HIGH}")
    print(f"  - Extreme High: > {VIX_EXTREME_HIGH}")
    
    # Count observations in each regime
    low_vix = test_vix[test_vix['VIX'] < VIX_OPTIMAL_LOW]
    optimal_vix = test_vix[(test_vix['VIX'] >= VIX_OPTIMAL_LOW) & (test_vix['VIX'] <= VIX_OPTIMAL_HIGH)]
    high_vix = test_vix[(test_vix['VIX'] > VIX_OPTIMAL_HIGH) & (test_vix['VIX'] <= VIX_EXTREME_HIGH)]
    extreme_vix = test_vix[test_vix['VIX'] > VIX_EXTREME_HIGH]
    
    print(f"\n📊 VIX REGIME DISTRIBUTION:")
    print(f"  - Low VIX (< {VIX_OPTIMAL_LOW}): {len(low_vix)} days ({len(low_vix)/len(test_vix)*100:.1f}%)")
    print(f"  - Optimal VIX ({VIX_OPTIMAL_LOW}-{VIX_OPTIMAL_HIGH}): {len(optimal_vix)} days ({len(optimal_vix)/len(test_vix)*100:.1f}%)")
    print(f"  - High VIX ({VIX_OPTIMAL_HIGH}-{VIX_EXTREME_HIGH}): {len(high_vix)} days ({len(high_vix)/len(test_vix)*100:.1f}%)")
    print(f"  - Extreme VIX (> {VIX_EXTREME_HIGH}): {len(extreme_vix)} days ({len(extreme_vix)/len(test_vix)*100:.1f}%)")
    
    # Test signal generation and filtering
    print(f"\n🔧 TESTING SIGNAL GENERATION:")
    
    # Generate trading dates
    trading_dates = pd.date_range(test_start, test_end, freq='B')  # Business days
    
    signals_generated = 0
    signals_approved = 0
    signals_rejected = 0
    rejection_reasons = {}
    
    for trade_date in trading_dates[:20]:  # Test first 20 days
        # Generate a test signal
        signal_direction = np.random.choice(['BULLISH', 'BEARISH'])
        signal_strength = np.random.uniform(0.7, 0.95)
        
        # Apply VIX filter
        vix_filter = strategy.apply_enhanced_vix_filter(trade_date, signal_strength, signal_direction)
        
        signals_generated += 1
        
        if vix_filter['approved']:
            signals_approved += 1
        else:
            signals_rejected += 1
            reason = vix_filter.get('filter_reason', 'unknown')
            rejection_reasons[reason] = rejection_reasons.get(reason, 0) + 1
    
    print(f"📊 Signal Testing Results (first 20 days):")
    print(f"  - Signals Generated: {signals_generated}")
    print(f"  - Signals Approved: {signals_approved} ({signals_approved/signals_generated*100:.1f}%)")
    print(f"  - Signals Rejected: {signals_rejected} ({signals_rejected/signals_generated*100:.1f}%)")
    
    if rejection_reasons:
        print(f"\n❌ REJECTION REASONS:")
        for reason, count in rejection_reasons.items():
            print(f"  - {reason}: {count} times")
    
    # Suggest optimized thresholds
    print(f"\n💡 SUGGESTED OPTIMIZATIONS:")
    
    vix_25th = test_vix['VIX'].quantile(0.25)
    vix_75th = test_vix['VIX'].quantile(0.75)
    vix_90th = test_vix['VIX'].quantile(0.90)
    
    print(f"Based on test period VIX distribution:")
    print(f"  - 25th percentile: {vix_25th:.1f}")
    print(f"  - 75th percentile: {vix_75th:.1f}")
    print(f"  - 90th percentile: {vix_90th:.1f}")
    
    print(f"\nRecommended thresholds for better trade generation:")
    print(f"  - VIX_OPTIMAL_LOW: {vix_25th:.1f} (was {VIX_OPTIMAL_LOW})")
    print(f"  - VIX_OPTIMAL_HIGH: {vix_75th:.1f} (was {VIX_OPTIMAL_HIGH})")
    print(f"  - VIX_EXTREME_HIGH: {vix_90th:.1f} (was {VIX_EXTREME_HIGH})")

def test_refined_filters():
    """Test with refined VIX filters"""
    
    print(f"\n🔧 TESTING REFINED VIX FILTERS")
    print("=" * 50)
    
    # Test with more permissive thresholds
    test_thresholds = [
        {'low': 12.0, 'high': 20.0, 'extreme': 25.0, 'name': 'Permissive'},
        {'low': 10.0, 'high': 18.0, 'extreme': 22.0, 'name': 'Very Permissive'},
        {'low': 14.0, 'high': 22.0, 'extreme': 28.0, 'name': 'Moderate'},
    ]
    
    for thresholds in test_thresholds:
        print(f"\n📊 Testing {thresholds['name']} Thresholds:")
        print(f"   Low: < {thresholds['low']}, Optimal: {thresholds['low']}-{thresholds['high']}, Extreme: > {thresholds['extreme']}")
        
        # This would require modifying the constants temporarily
        # For now, just show what the distribution would be
        
        # Load test VIX data
        strategy = EnhancedVIXOptionsStrategyV3()
        strategy.options_loader.load_all_available_data(2012, 2012)
        strategy.load_market_data()
        
        test_start = datetime(2012, 1, 3)
        test_end = datetime(2012, 6, 30)
        
        test_vix = strategy.vix_data[
            (strategy.vix_data['date'] >= test_start) & 
            (strategy.vix_data['date'] <= test_end)
        ].copy()
        
        if len(test_vix) > 0:
            low_count = len(test_vix[test_vix['VIX'] < thresholds['low']])
            optimal_count = len(test_vix[(test_vix['VIX'] >= thresholds['low']) & (test_vix['VIX'] <= thresholds['high'])])
            high_count = len(test_vix[(test_vix['VIX'] > thresholds['high']) & (test_vix['VIX'] <= thresholds['extreme'])])
            extreme_count = len(test_vix[test_vix['VIX'] > thresholds['extreme']])
            
            total = len(test_vix)
            print(f"   Low: {low_count} days ({low_count/total*100:.1f}%)")
            print(f"   Optimal: {optimal_count} days ({optimal_count/total*100:.1f}%)")
            print(f"   High: {high_count} days ({high_count/total*100:.1f}%)")
            print(f"   Extreme: {extreme_count} days ({extreme_count/total*100:.1f}%)")
            
            # Estimate trade approval rate
            approval_rate = (low_count + optimal_count + high_count) / total * 100
            print(f"   Estimated Approval Rate: {approval_rate:.1f}%")

if __name__ == "__main__":
    analyze_vix_filtering()
    test_refined_filters()
