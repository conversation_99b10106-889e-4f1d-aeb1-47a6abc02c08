#!/usr/bin/env python3
"""
Call Spread Strategy PDF Report Generator
Comprehensive analysis and reporting for two-leg call spread strategy
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
import matplotlib.dates as mdates

class CallSpreadPDFGenerator:
    def __init__(self, trades_file='trades/call_spread_trades.csv'):
        self.trades_file = trades_file
        self.trades_df = None
        self.load_trades()
        
    def load_trades(self):
        """Load call spread trades data"""
        if os.path.exists(self.trades_file):
            self.trades_df = pd.read_csv(self.trades_file)
            self.trades_df['signal_date'] = pd.to_datetime(self.trades_df['signal_date'])
            self.trades_df['exit_date'] = pd.to_datetime(self.trades_df['exit_date'])
            print(f"✅ Loaded {len(self.trades_df)} call spread trades")
        else:
            print(f"❌ Trades file not found: {self.trades_file}")
            
    def calculate_performance_metrics(self):
        """Calculate comprehensive performance metrics"""
        if self.trades_df is None or len(self.trades_df) == 0:
            return {}
            
        # Basic metrics
        total_trades = len(self.trades_df)
        winning_trades = len(self.trades_df[self.trades_df['net_pnl'] > 0])
        losing_trades = len(self.trades_df[self.trades_df['net_pnl'] < 0])
        win_rate = (winning_trades / total_trades) * 100
        
        # P&L metrics
        total_pnl = self.trades_df['net_pnl'].sum()
        starting_capital = 100000
        total_return = (total_pnl / starting_capital) * 100
        
        # Calculate cumulative P&L for drawdown
        sorted_trades = self.trades_df.sort_values('exit_date')
        sorted_trades['cumulative_pnl'] = sorted_trades['net_pnl'].cumsum()
        sorted_trades['running_max'] = sorted_trades['cumulative_pnl'].expanding().max()
        sorted_trades['drawdown'] = sorted_trades['cumulative_pnl'] - sorted_trades['running_max']
        max_drawdown = abs(sorted_trades['drawdown'].min() / starting_capital * 100)
        
        # Profit factor
        gross_profit = self.trades_df[self.trades_df['net_pnl'] > 0]['net_pnl'].sum()
        gross_loss = abs(self.trades_df[self.trades_df['net_pnl'] < 0]['net_pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Average metrics
        avg_win = self.trades_df[self.trades_df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = self.trades_df[self.trades_df['net_pnl'] < 0]['net_pnl'].mean() if losing_trades > 0 else 0
        
        # Spread characteristics
        avg_spread_width = self.trades_df['spread_width'].mean()
        avg_net_credit = self.trades_df['net_credit'].mean()
        avg_contracts = self.trades_df['contracts'].mean()
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'avg_spread_width': avg_spread_width,
            'avg_net_credit': avg_net_credit,
            'avg_contracts': avg_contracts,
            'final_capital': starting_capital + total_pnl,
            'sorted_trades': sorted_trades
        }
    
    def create_equity_curve_chart(self, metrics):
        """Create equity curve chart"""
        plt.style.use('default')
        fig, ax = plt.subplots(figsize=(12, 8))
        
        sorted_trades = metrics['sorted_trades']
        starting_capital = 100000
        
        # Calculate equity curve
        equity_curve = starting_capital + sorted_trades['cumulative_pnl']
        
        # Plot equity curve
        ax.plot(sorted_trades['exit_date'], equity_curve, linewidth=2, color='#2E8B57', label='Call Spread Strategy')
        ax.fill_between(sorted_trades['exit_date'], starting_capital, equity_curve, alpha=0.3, color='#2E8B57')
        
        # Formatting
        ax.set_title('Call Spread Strategy - Equity Curve', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Portfolio Value ($)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Format y-axis as currency
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        chart_path = 'reports/call_spread_equity_curve.png'
        os.makedirs('reports', exist_ok=True)
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_path
    
    def create_trade_distribution_chart(self):
        """Create trade P&L distribution chart"""
        plt.style.use('default')
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # P&L Distribution
        ax1.hist(self.trades_df['net_pnl'], bins=20, alpha=0.7, color='#4CAF50', edgecolor='black')
        ax1.axvline(self.trades_df['net_pnl'].mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: ${self.trades_df["net_pnl"].mean():,.0f}')
        ax1.set_title('Trade P&L Distribution', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Net P&L ($)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Spread Width vs P&L
        colors_map = ['green' if pnl > 0 else 'red' for pnl in self.trades_df['net_pnl']]
        ax2.scatter(self.trades_df['spread_width'], self.trades_df['net_pnl'], c=colors_map, alpha=0.7, s=60)
        ax2.set_title('Spread Width vs P&L', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Spread Width (Points)')
        ax2.set_ylabel('Net P&L ($)')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_path = 'reports/call_spread_distribution.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_path
    
    def generate_pdf_report(self):
        """Generate comprehensive PDF report"""
        if self.trades_df is None:
            print("❌ No trades data available for PDF generation")
            return None
            
        # Calculate metrics
        metrics = self.calculate_performance_metrics()
        
        # Create charts
        equity_chart = self.create_equity_curve_chart(metrics)
        distribution_chart = self.create_trade_distribution_chart()
        
        # Generate PDF
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_filename = f'reports/Call_Spread_Strategy_Report_{timestamp}.pdf'
        
        doc = SimpleDocTemplate(pdf_filename, pagesize=letter, topMargin=0.5*inch, bottomMargin=0.5*inch)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'], fontSize=20, spaceAfter=30, alignment=TA_CENTER)
        story.append(Paragraph("Call Spread Strategy - Comprehensive Report", title_style))
        story.append(Paragraph(f"Generated: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Executive Summary
        story.append(Paragraph("Executive Summary", styles['Heading2']))
        summary_text = f"""
        The Call Spread Strategy demonstrates exceptional performance with a {metrics['total_return']:.1f}% total return 
        and {metrics['win_rate']:.1f}% win rate across {metrics['total_trades']} trades. This two-leg options strategy 
        sells closer-to-money calls and buys further out-of-money calls for net credit, providing defined risk and 
        consistent income generation.
        """
        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Performance Metrics Table
        story.append(Paragraph("Performance Metrics", styles['Heading2']))
        
        perf_data = [
            ['Metric', 'Value'],
            ['Total Return', f"{metrics['total_return']:.1f}%"],
            ['Win Rate', f"{metrics['win_rate']:.1f}%"],
            ['Total Trades', f"{metrics['total_trades']:,}"],
            ['Winning Trades', f"{metrics['winning_trades']:,}"],
            ['Losing Trades', f"{metrics['losing_trades']:,}"],
            ['Total P&L', f"${metrics['total_pnl']:,.0f}"],
            ['Final Capital', f"${metrics['final_capital']:,.0f}"],
            ['Max Drawdown', f"{metrics['max_drawdown']:.1f}%"],
            ['Profit Factor', f"{metrics['profit_factor']:.2f}" if metrics['profit_factor'] != float('inf') else "∞"],
            ['Avg Win', f"${metrics['avg_win']:,.0f}"],
            ['Avg Loss', f"${metrics['avg_loss']:,.0f}"],
        ]
        
        perf_table = Table(perf_data, colWidths=[2.5*inch, 2*inch])
        perf_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(perf_table)
        story.append(Spacer(1, 20))

        # Strategy Characteristics
        story.append(Paragraph("Strategy Characteristics", styles['Heading2']))

        char_data = [
            ['Characteristic', 'Value'],
            ['Avg Spread Width', f"{metrics['avg_spread_width']:.0f} points"],
            ['Avg Net Credit', f"${metrics['avg_net_credit']:.2f}"],
            ['Avg Contracts', f"{metrics['avg_contracts']:.1f}"],
            ['Credit as % of Spread', f"{(metrics['avg_net_credit']/metrics['avg_spread_width']*100):.1f}%"],
            ['Risk per Trade', f"${(metrics['avg_spread_width']-metrics['avg_net_credit'])*metrics['avg_contracts']*100:.0f}"],
            ['Reward/Risk Ratio', f"{metrics['avg_net_credit']/(metrics['avg_spread_width']-metrics['avg_net_credit']):.2f}:1"],
        ]

        char_table = Table(char_data, colWidths=[2.5*inch, 2*inch])
        char_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(char_table)
        story.append(Spacer(1, 20))

        # Recent Trades
        story.append(Paragraph("Recent Trades (Last 10)", styles['Heading2']))

        recent_trades = self.trades_df.nlargest(10, 'exit_date')
        trade_data = [['Date', 'Short/Long', 'Spread', 'Credit', 'Contracts', 'P&L']]

        for _, trade in recent_trades.iterrows():
            trade_data.append([
                trade['signal_date'].strftime('%Y-%m-%d'),
                f"{trade['short_strike']:.0f}/{trade['long_strike']:.0f}",
                f"{trade['spread_width']:.0f}pts",
                f"${trade['net_credit']:.2f}",
                f"{trade['contracts']:.0f}",
                f"${trade['net_pnl']:,.0f}"
            ])

        trade_table = Table(trade_data, colWidths=[1*inch, 1*inch, 0.8*inch, 0.8*inch, 0.8*inch, 1*inch])
        trade_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(trade_table)
        story.append(PageBreak())

        # Strategy Description
        story.append(Paragraph("Strategy Description", styles['Heading2']))

        strategy_text = """
        <b>Call Spread Strategy Overview:</b><br/><br/>

        The Call Spread Strategy is a two-leg options strategy that involves:
        <br/>• <b>Selling</b> a closer-to-money call option (short leg)
        <br/>• <b>Buying</b> a further out-of-money call option (long leg)
        <br/><br/>

        <b>Key Characteristics:</b>
        <br/>• <b>Net Credit Strategy:</b> Receive premium upfront
        <br/>• <b>Defined Risk:</b> Maximum loss = Spread Width - Net Credit
        <br/>• <b>Defined Reward:</b> Maximum profit = Net Credit received
        <br/>• <b>Time Decay Benefit:</b> Profits from theta decay
        <br/><br/>

        <b>Optimal Market Conditions:</b>
        <br/>• Neutral to slightly bearish market outlook
        <br/>• High implied volatility (sell expensive options)
        <br/>• Stable or declining underlying price
        <br/>• Low VIX with negative VRP conditions
        """

        story.append(Paragraph(strategy_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Risk Management
        story.append(Paragraph("Risk Management", styles['Heading2']))

        risk_text = """
        <b>Position Sizing:</b> 5-25 contracts based on signal confidence<br/>
        <b>Maximum Spread Width:</b> 150 points to limit risk<br/>
        <b>Minimum Credit:</b> 15% of spread width for adequate reward<br/>
        <b>Holding Period:</b> 3 days for optimal time decay capture<br/>
        <b>Strike Selection:</b> ATM to 4% OTM for optimal risk/reward<br/>
        """

        story.append(Paragraph(risk_text, styles['Normal']))

        print(f"✅ Generated comprehensive PDF report: {pdf_filename}")

        # Build PDF
        doc.build(story)
        return pdf_filename

if __name__ == "__main__":
    print("📊 GENERATING CALL SPREAD STRATEGY PDF REPORT")
    print("=" * 60)
    
    generator = CallSpreadPDFGenerator()
    pdf_file = generator.generate_pdf_report()
    
    if pdf_file:
        print(f"✅ PDF report generated successfully: {pdf_file}")
    else:
        print("❌ Failed to generate PDF report")
