#!/usr/bin/env python3
"""
Call Spread Strategy PDF Report Generator
Comprehensive analysis and reporting for two-leg call spread strategy
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
import matplotlib.dates as mdates
import openai
import sys

class CallSpreadPDFGenerator:
    def __init__(self, trades_file='trades/call_spread_trades.csv'):
        self.trades_file = trades_file
        self.trades_df = None
        self.openai_client = None
        self.setup_openai()
        self.load_trades()

    def setup_openai(self):
        """Setup OpenAI client for narrative generation"""
        try:
            # Try to get API key from environment
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.openai_client = openai.OpenAI(api_key=api_key)
                print("✅ OpenAI client initialized")
            else:
                print("⚠️ OpenAI API key not found - will use default narratives")
        except Exception as e:
            print(f"⚠️ OpenAI setup failed: {e} - will use default narratives")

    def generate_narrative(self, prompt, fallback_text):
        """Generate narrative using ChatGPT or fallback to default"""
        if not self.openai_client:
            return fallback_text

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional quantitative analyst writing clear, concise trading strategy documentation."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"⚠️ ChatGPT generation failed: {e} - using fallback")
            return fallback_text

    def load_trades(self):
        """Load call spread trades data"""
        if os.path.exists(self.trades_file):
            self.trades_df = pd.read_csv(self.trades_file)
            self.trades_df['signal_date'] = pd.to_datetime(self.trades_df['signal_date'])
            self.trades_df['exit_date'] = pd.to_datetime(self.trades_df['exit_date'])
            print(f"✅ Loaded {len(self.trades_df)} call spread trades")
        else:
            print(f"❌ Trades file not found: {self.trades_file}")
            
    def calculate_performance_metrics(self):
        """Calculate comprehensive performance metrics"""
        if self.trades_df is None or len(self.trades_df) == 0:
            return {}
            
        # Basic metrics
        total_trades = len(self.trades_df)
        winning_trades = len(self.trades_df[self.trades_df['net_pnl'] > 0])
        losing_trades = len(self.trades_df[self.trades_df['net_pnl'] < 0])
        win_rate = (winning_trades / total_trades) * 100
        
        # P&L metrics
        total_pnl = self.trades_df['net_pnl'].sum()
        starting_capital = 100000
        total_return = (total_pnl / starting_capital) * 100
        
        # Calculate cumulative P&L for drawdown
        sorted_trades = self.trades_df.sort_values('exit_date')
        sorted_trades['cumulative_pnl'] = sorted_trades['net_pnl'].cumsum()
        sorted_trades['running_max'] = sorted_trades['cumulative_pnl'].expanding().max()
        sorted_trades['drawdown'] = sorted_trades['cumulative_pnl'] - sorted_trades['running_max']
        max_drawdown = abs(sorted_trades['drawdown'].min() / starting_capital * 100)
        
        # Profit factor
        gross_profit = self.trades_df[self.trades_df['net_pnl'] > 0]['net_pnl'].sum()
        gross_loss = abs(self.trades_df[self.trades_df['net_pnl'] < 0]['net_pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Average metrics
        avg_win = self.trades_df[self.trades_df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = self.trades_df[self.trades_df['net_pnl'] < 0]['net_pnl'].mean() if losing_trades > 0 else 0
        
        # Spread characteristics
        avg_spread_width = self.trades_df['spread_width'].mean()
        avg_net_credit = self.trades_df['net_credit'].mean()
        avg_contracts = self.trades_df['contracts'].mean()
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'avg_spread_width': avg_spread_width,
            'avg_net_credit': avg_net_credit,
            'avg_contracts': avg_contracts,
            'final_capital': starting_capital + total_pnl,
            'sorted_trades': sorted_trades
        }
    
    def create_comprehensive_charts(self, metrics):
        """Create comprehensive equity and performance charts"""
        plt.style.use('default')

        # Create main equity curve chart
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        sorted_trades = metrics['sorted_trades']
        starting_capital = 100000

        # 1. Equity Curve
        equity_curve = starting_capital + sorted_trades['cumulative_pnl']
        ax1.plot(sorted_trades['exit_date'], equity_curve, linewidth=3, color='#2E8B57', label='Portfolio Value')
        ax1.fill_between(sorted_trades['exit_date'], starting_capital, equity_curve, alpha=0.3, color='#2E8B57')
        ax1.axhline(y=starting_capital, color='red', linestyle='--', alpha=0.7, label='Starting Capital')
        ax1.set_title('Equity Curve - Call Spread Strategy', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Portfolio Value ($)')
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 2. Drawdown Chart
        drawdown_pct = (sorted_trades['drawdown'] / starting_capital) * 100
        ax2.fill_between(sorted_trades['exit_date'], 0, drawdown_pct, color='red', alpha=0.7)
        ax2.set_title('Drawdown Analysis', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Drawdown (%)')
        ax2.set_ylim(min(drawdown_pct.min() * 1.1, -0.1), 0.1)
        ax2.grid(True, alpha=0.3)

        # 3. Monthly Returns
        sorted_trades['month'] = sorted_trades['exit_date'].dt.to_period('M')
        monthly_pnl = sorted_trades.groupby('month')['net_pnl'].sum()
        monthly_returns = (monthly_pnl / starting_capital) * 100

        colors = ['green' if x > 0 else 'red' for x in monthly_returns]
        ax3.bar(range(len(monthly_returns)), monthly_returns, color=colors, alpha=0.7)
        ax3.set_title('Monthly Returns', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Monthly Return (%)')
        ax3.set_xticks(range(len(monthly_returns)))
        ax3.set_xticklabels([str(m) for m in monthly_returns.index], rotation=45)
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 4. Trade P&L Distribution
        ax4.hist(self.trades_df['net_pnl'], bins=15, alpha=0.7, color='#4CAF50', edgecolor='black')
        ax4.axvline(self.trades_df['net_pnl'].mean(), color='red', linestyle='--', linewidth=2,
                   label=f'Mean: ${self.trades_df["net_pnl"].mean():,.0f}')
        ax4.set_title('Trade P&L Distribution', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Net P&L ($)')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        chart_path = 'reports/call_spread_comprehensive_charts.png'
        os.makedirs('reports', exist_ok=True)
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    def get_next_signal_info(self):
        """Get next trading signal information"""
        try:
            # Import the call spread strategy to get current market conditions
            sys.path.append('.')
            from call_spread_strategy import CallSpreadStrategy

            strategy = CallSpreadStrategy()
            market_data = strategy.load_market_data_with_real_vrp()

            if market_data is not None and len(market_data) > 0:
                # Get latest market data
                latest_data = market_data.iloc[-1]
                latest_date = market_data.index[-1]

                # Generate signals for latest data
                signals = strategy.generate_enhanced_signals(market_data.tail(1))

                next_signal_info = {
                    'date': latest_date,
                    'vix': latest_data.get('vix', 'N/A'),
                    'vrp_avg': latest_data.get('vrp_avg', 'N/A'),
                    'signal_available': len(signals) > 0,
                    'signal_direction': signals[0]['signal_direction'] if signals else 'No Signal',
                    'confidence_score': signals[0]['confidence_score'] if signals else 0,
                    'condition': signals[0]['condition'] if signals else 'No qualifying conditions',
                    'holding_period': '3 days',
                    'position_size': f"{max(5, int(signals[0]['confidence_score'] * 25)) if signals else 0} contracts",
                    'strategy_type': 'Call Spread (Sell closer OTM, Buy further OTM)'
                }

                return next_signal_info

        except Exception as e:
            print(f"⚠️ Could not get next signal info: {e}")

        # Fallback signal info
        return {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'vix': 'Monitor',
            'vrp_avg': 'Monitor',
            'signal_available': False,
            'signal_direction': 'Monitor Market',
            'confidence_score': 0,
            'condition': 'Awaiting VRP negative conditions',
            'holding_period': '3 days',
            'position_size': '5-25 contracts',
            'strategy_type': 'Call Spread (Sell closer OTM, Buy further OTM)'
        }

    def create_trade_distribution_chart(self):
        """Create trade P&L distribution chart"""
        plt.style.use('default')
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # P&L Distribution
        ax1.hist(self.trades_df['net_pnl'], bins=20, alpha=0.7, color='#4CAF50', edgecolor='black')
        ax1.axvline(self.trades_df['net_pnl'].mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: ${self.trades_df["net_pnl"].mean():,.0f}')
        ax1.set_title('Trade P&L Distribution', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Net P&L ($)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Spread Width vs P&L
        colors_map = ['green' if pnl > 0 else 'red' for pnl in self.trades_df['net_pnl']]
        ax2.scatter(self.trades_df['spread_width'], self.trades_df['net_pnl'], c=colors_map, alpha=0.7, s=60)
        ax2.set_title('Spread Width vs P&L', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Spread Width (Points)')
        ax2.set_ylabel('Net P&L ($)')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_path = 'reports/call_spread_distribution.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_path
    
    def generate_pdf_report(self):
        """Generate comprehensive PDF report"""
        if self.trades_df is None:
            print("❌ No trades data available for PDF generation")
            return None
            
        # Calculate metrics
        metrics = self.calculate_performance_metrics()

        # Get next signal information
        next_signal = self.get_next_signal_info()

        # Create charts
        comprehensive_chart = self.create_comprehensive_charts(metrics)
        distribution_chart = self.create_trade_distribution_chart()
        
        # Generate PDF
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_filename = f'reports/Call_Spread_Strategy_Report_{timestamp}.pdf'
        
        doc = SimpleDocTemplate(pdf_filename, pagesize=letter, topMargin=0.5*inch, bottomMargin=0.5*inch)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'], fontSize=20, spaceAfter=30, alignment=TA_CENTER)
        story.append(Paragraph("Call Spread Strategy - Comprehensive Report", title_style))
        story.append(Paragraph(f"Generated: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Executive Summary with ChatGPT narrative
        story.append(Paragraph("Executive Summary", styles['Heading2']))

        summary_prompt = f"""
        Write a professional executive summary for a call spread options trading strategy with these results:
        - Total Return: {metrics['total_return']:.1f}%
        - Win Rate: {metrics['win_rate']:.1f}%
        - Total Trades: {metrics['total_trades']}
        - Max Drawdown: {metrics['max_drawdown']:.1f}%
        - Strategy: Sell closer-to-money calls, buy further OTM calls for net credit
        Keep it concise and professional, 3-4 sentences maximum.
        """

        fallback_summary = f"""
        The Call Spread Strategy demonstrates exceptional performance with a {metrics['total_return']:.1f}% total return
        and {metrics['win_rate']:.1f}% win rate across {metrics['total_trades']} trades. This two-leg options strategy
        sells closer-to-money calls and buys further out-of-money calls for net credit, providing defined risk and
        consistent income generation with only {metrics['max_drawdown']:.1f}% maximum drawdown.
        """

        summary_text = self.generate_narrative(summary_prompt, fallback_summary)
        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Next Signal Information
        story.append(Paragraph("Next Trading Signal", styles['Heading2']))

        signal_data = [
            ['Signal Information', 'Value'],
            ['Current Date', next_signal['date']],
            ['Signal Available', 'YES' if next_signal['signal_available'] else 'MONITOR'],
            ['Signal Direction', next_signal['signal_direction']],
            ['Market Condition', next_signal['condition']],
            ['Confidence Score', f"{next_signal['confidence_score']:.1f}" if next_signal['confidence_score'] > 0 else 'N/A'],
            ['Position Size', next_signal['position_size']],
            ['Holding Period', next_signal['holding_period']],
            ['Current VIX', f"{next_signal['vix']:.2f}" if isinstance(next_signal['vix'], (int, float)) else str(next_signal['vix'])],
            ['Current VRP', f"{next_signal['vrp_avg']:.2f}" if isinstance(next_signal['vrp_avg'], (int, float)) else str(next_signal['vrp_avg'])],
        ]

        signal_table = Table(signal_data, colWidths=[2.5*inch, 2*inch])
        signal_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(signal_table)
        story.append(Spacer(1, 20))
        
        # Performance Metrics Table
        story.append(Paragraph("Performance Metrics", styles['Heading2']))
        
        perf_data = [
            ['Metric', 'Value'],
            ['Total Return', f"{metrics['total_return']:.1f}%"],
            ['Win Rate', f"{metrics['win_rate']:.1f}%"],
            ['Total Trades', f"{metrics['total_trades']:,}"],
            ['Winning Trades', f"{metrics['winning_trades']:,}"],
            ['Losing Trades', f"{metrics['losing_trades']:,}"],
            ['Total P&L', f"${metrics['total_pnl']:,.0f}"],
            ['Final Capital', f"${metrics['final_capital']:,.0f}"],
            ['Max Drawdown', f"{metrics['max_drawdown']:.1f}%"],
            ['Profit Factor', f"{metrics['profit_factor']:.2f}" if metrics['profit_factor'] != float('inf') else "∞"],
            ['Avg Win', f"${metrics['avg_win']:,.0f}"],
            ['Avg Loss', f"${metrics['avg_loss']:,.0f}"],
        ]
        
        perf_table = Table(perf_data, colWidths=[2.5*inch, 2*inch])
        perf_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(perf_table)
        story.append(Spacer(1, 20))

        # Strategy Characteristics
        story.append(Paragraph("Strategy Characteristics", styles['Heading2']))

        char_data = [
            ['Characteristic', 'Value'],
            ['Avg Spread Width', f"{metrics['avg_spread_width']:.0f} points"],
            ['Avg Net Credit', f"${metrics['avg_net_credit']:.2f}"],
            ['Avg Contracts', f"{metrics['avg_contracts']:.1f}"],
            ['Credit as % of Spread', f"{(metrics['avg_net_credit']/metrics['avg_spread_width']*100):.1f}%"],
            ['Risk per Trade', f"${(metrics['avg_spread_width']-metrics['avg_net_credit'])*metrics['avg_contracts']*100:.0f}"],
            ['Reward/Risk Ratio', f"{metrics['avg_net_credit']/(metrics['avg_spread_width']-metrics['avg_net_credit']):.2f}:1"],
        ]

        char_table = Table(char_data, colWidths=[2.5*inch, 2*inch])
        char_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(char_table)
        story.append(Spacer(1, 20))

        # Recent Trades
        story.append(Paragraph("Recent Trades (Last 10)", styles['Heading2']))

        recent_trades = self.trades_df.nlargest(10, 'exit_date')
        trade_data = [['Date', 'Short/Long', 'Spread', 'Credit', 'Contracts', 'P&L']]

        for _, trade in recent_trades.iterrows():
            trade_data.append([
                trade['signal_date'].strftime('%Y-%m-%d'),
                f"{trade['short_strike']:.0f}/{trade['long_strike']:.0f}",
                f"{trade['spread_width']:.0f}pts",
                f"${trade['net_credit']:.2f}",
                f"{trade['contracts']:.0f}",
                f"${trade['net_pnl']:,.0f}"
            ])

        trade_table = Table(trade_data, colWidths=[1*inch, 1*inch, 0.8*inch, 0.8*inch, 0.8*inch, 1*inch])
        trade_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(trade_table)
        story.append(PageBreak())

        # Add comprehensive charts
        story.append(Paragraph("Performance Analysis", styles['Heading2']))

        if os.path.exists(comprehensive_chart):
            img = Image(comprehensive_chart, width=7*inch, height=5.25*inch)
            story.append(img)
            story.append(Spacer(1, 10))

        if os.path.exists(distribution_chart):
            img2 = Image(distribution_chart, width=7*inch, height=3*inch)
            story.append(img2)
            story.append(Spacer(1, 20))

        story.append(PageBreak())

        # Strategy Description with ChatGPT narrative
        story.append(Paragraph("Strategy Methodology", styles['Heading2']))

        methodology_prompt = f"""
        Write a detailed but concise explanation of a call spread options trading strategy methodology. Include:
        - How the strategy works (sell closer OTM call, buy further OTM call)
        - Entry conditions (VRP negative, VIX technical signals)
        - Risk management (defined risk, position sizing)
        - Exit strategy (3-day holding period)
        Keep it professional and educational, about 200 words.
        """

        fallback_methodology = """
        <b>Call Spread Strategy - Comprehensive Methodology:</b><br/><br/>

        <b>What is a Call Spread?</b><br/>
        A call spread is a two-leg options strategy that involves simultaneously selling one call option (short leg) and buying another call option (long leg) with the same expiration date but different strike prices. This creates a defined risk/reward profile with limited profit potential and limited loss exposure.<br/><br/>

        <b>How Call Spreads Work:</b><br/>
        • <b>Short Leg (Sell):</b> Sell a call option closer to the current market price (lower strike)<br/>
        • <b>Long Leg (Buy):</b> Buy a call option further from the market price (higher strike)<br/>
        • <b>Net Credit:</b> Receive premium upfront (short premium > long premium)<br/>
        • <b>Maximum Profit:</b> Keep the entire net credit if both options expire worthless<br/>
        • <b>Maximum Loss:</b> Spread width minus net credit received<br/><br/>

        <b>Strike Selection Criteria:</b><br/>
        Our systematic approach uses the following selection criteria:<br/>
        • <b>Short Strike:</b> 1-4% out-of-the-money from current SPX price<br/>
        • <b>Long Strike:</b> 2-7% out-of-the-money from current SPX price<br/>
        • <b>Spread Width:</b> Maximum 150 points to limit risk exposure<br/>
        • <b>Strike Intervals:</b> Only strikes that are multiples of 25 points<br/>
        • <b>Expiration:</b> 25-35 days to expiration for optimal time decay<br/>
        • <b>Minimum Credit:</b> At least 15% of spread width for adequate risk/reward<br/><br/>

        <b>Entry Signal Conditions:</b><br/>
        Trades are initiated when multiple technical conditions align:<br/>
        • <b>VRP Negative:</b> Volatility Risk Premium below -2 (implied vol < realized vol)<br/>
        • <b>VIX Technical:</b> VIX RSI showing oversold conditions (RSI < 30)<br/>
        • <b>Market Environment:</b> VIX levels below 22 for stable conditions<br/>
        • <b>Options Quality:</b> Sufficient bid-ask spreads and volume<br/><br/>

        <b>Position Sizing Logic:</b><br/>
        Position sizes are dynamically calculated based on signal confidence:<br/>
        • <b>Base Range:</b> 20-60 contracts per trade<br/>
        • <b>Confidence Multiplier:</b> Higher confidence = larger position size<br/>
        • <b>Risk Management:</b> Maximum position size capped at 60 contracts<br/>
        • <b>Capital Allocation:</b> Positions sized to optimize risk-adjusted returns<br/><br/>

        <b>Risk Management Framework:</b><br/>
        • <b>Defined Risk:</b> Maximum loss = (Spread Width - Net Credit) × Contracts × 100<br/>
        • <b>Profit Target:</b> Keep entire net credit if market stays below short strike<br/>
        • <b>Time Decay:</b> Benefit from theta decay over 3-day holding period<br/>
        • <b>Early Exit:</b> No intraday management - hold to expiration or 3-day limit<br/>
        • <b>Assignment Risk:</b> Monitor for early assignment on short leg if ITM
        """

        methodology_text = self.generate_narrative(methodology_prompt, fallback_methodology)
        story.append(Paragraph(methodology_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Trading Execution Guide
        story.append(Paragraph("How to Execute Trades", styles['Heading2']))

        execution_prompt = f"""
        Write a step-by-step guide for executing call spread trades based on this strategy. Include:
        - Daily monitoring requirements
        - Signal identification process
        - Order entry procedures for call spreads
        - Position management during holding period
        - Exit procedures after 3 days
        Keep it practical and actionable, about 150 words.
        """

        fallback_execution = """
        <b>Daily Execution Process:</b><br/><br/>

        <b>1. Morning Market Analysis (9:00 AM):</b><br/>
        • Check VIX level and VRP calculation<br/>
        • Review VIX RSI for oversold conditions<br/>
        • Confirm negative VRP environment<br/><br/>

        <b>2. Signal Identification:</b><br/>
        • VRP < -2 (negative VRP required)<br/>
        • VIX RSI < 30 (oversold preferred)<br/>
        • Confidence score determines position size<br/><br/>

        <b>3. Order Entry (if signal present):</b><br/>
        • Sell SPX call 1-4% OTM (short leg)<br/>
        • Buy SPX call 2-7% OTM (long leg)<br/>
        • Target net credit ≥ 15% of spread width<br/>
        • Position size: 5-25 contracts based on confidence<br/><br/>

        <b>4. Position Management:</b><br/>
        • Hold for exactly 3 days<br/>
        • No intraday adjustments<br/>
        • Monitor for early assignment risk<br/><br/>

        <b>5. Exit Execution (Day 3):</b><br/>
        • Close both legs simultaneously<br/>
        • Use market orders for reliable fills<br/>
        • Record trade results for analysis
        """

        execution_text = self.generate_narrative(execution_prompt, fallback_execution)
        story.append(Paragraph(execution_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Call Spread Example
        story.append(Paragraph("Call Spread Trade Example", styles['Heading2']))

        example_prompt = f"""
        Create a detailed example of a call spread trade using real market conditions. Include:
        - SPX price scenario (e.g., SPX at 5000)
        - Strike selection process
        - Premium calculations
        - Risk/reward analysis
        - Profit/loss scenarios at expiration
        Keep it educational and specific with actual numbers.
        """

        fallback_example = """
        <b>Real Trade Example: SPX Call Spread</b><br/><br/>

        <b>Market Setup:</b><br/>
        • SPX Current Price: 5,000<br/>
        • VIX Level: 18.5 (low volatility environment)<br/>
        • VRP: -3.2 (negative, favorable for selling premium)<br/>
        • Signal Confidence: 0.8 (high confidence)<br/><br/>

        <b>Strike Selection:</b><br/>
        • Short Strike: 5,100 (2% OTM) - Sell this call<br/>
        • Long Strike: 5,250 (5% OTM) - Buy this call<br/>
        • Spread Width: 150 points<br/>
        • Days to Expiration: 30 days<br/><br/>

        <b>Premium Analysis:</b><br/>
        • Short Call Premium: $85.00 (receive)<br/>
        • Long Call Premium: $15.00 (pay)<br/>
        • Net Credit: $70.00 per spread<br/>
        • Credit as % of Spread: 46.7% (excellent)<br/><br/>

        <b>Position Sizing:</b><br/>
        • Confidence Score: 0.8<br/>
        • Contracts: 20 + (0.8 × 40) = 52 contracts<br/>
        • Total Credit Received: $70 × 52 × 100 = $364,000<br/><br/>

        <b>Risk/Reward Profile:</b><br/>
        • Maximum Profit: $364,000 (if SPX stays below 5,100)<br/>
        • Maximum Loss: (150 - 70) × 52 × 100 = $416,000<br/>
        • Breakeven Point: 5,100 + 70 = 5,170<br/>
        • Profit Probability: ~75% (based on historical data)<br/><br/>

        <b>Expiration Scenarios:</b><br/>
        • SPX below 5,100: Keep full $364,000 profit<br/>
        • SPX at 5,170: Breakeven (no profit/loss)<br/>
        • SPX at 5,200: Loss of $156,000<br/>
        • SPX above 5,250: Maximum loss of $416,000<br/><br/>

        <b>Why This Trade Works:</b><br/>
        • High probability of profit (market needs to stay below 5,170)<br/>
        • Excellent risk-adjusted return (87% max profit vs max loss)<br/>
        • Benefits from time decay over 30 days<br/>
        • Defined risk with known maximum loss
        """

        example_text = self.generate_narrative(example_prompt, fallback_example)
        story.append(Paragraph(example_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Market Outlook and Recommendations
        story.append(Paragraph("Current Market Outlook", styles['Heading2']))

        outlook_prompt = f"""
        Based on the call spread strategy performance ({metrics['total_return']:.1f}% return, {metrics['win_rate']:.1f}% win rate),
        write a brief market outlook and recommendations for continuing this strategy. Include:
        - Current market environment assessment
        - Strategy suitability going forward
        - Risk considerations
        - Recommended position sizing
        Keep it professional and forward-looking, about 100 words.
        """

        fallback_outlook = f"""
        <b>Market Assessment:</b> The exceptional {metrics['total_return']:.1f}% return with {metrics['win_rate']:.1f}% win rate demonstrates the strategy's effectiveness in current market conditions. The low {metrics['max_drawdown']:.1f}% maximum drawdown indicates robust risk management.<br/><br/>

        <b>Going Forward:</b> Continue monitoring VRP conditions and VIX technical signals for optimal entry points. The strategy's defined risk profile makes it suitable for various market environments, particularly during periods of elevated implied volatility.<br/><br/>

        <b>Recommendations:</b> Maintain position sizes of 5-25 contracts based on signal confidence. Focus on trades with minimum 15% credit-to-spread ratios. Consider reducing position sizes if market volatility increases significantly above historical norms.
        """

        outlook_text = self.generate_narrative(outlook_prompt, fallback_outlook)
        story.append(Paragraph(outlook_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Risk Management
        story.append(Paragraph("Risk Management", styles['Heading2']))

        risk_text = """
        <b>Position Sizing:</b> 5-25 contracts based on signal confidence<br/>
        <b>Maximum Spread Width:</b> 150 points to limit risk<br/>
        <b>Minimum Credit:</b> 15% of spread width for adequate reward<br/>
        <b>Holding Period:</b> 3 days for optimal time decay capture<br/>
        <b>Strike Selection:</b> ATM to 4% OTM for optimal risk/reward<br/>
        """

        story.append(Paragraph(risk_text, styles['Normal']))

        print(f"✅ Generated comprehensive PDF report: {pdf_filename}")

        # Build PDF
        doc.build(story)
        return pdf_filename

if __name__ == "__main__":
    print("📊 GENERATING CALL SPREAD STRATEGY PDF REPORT")
    print("=" * 60)
    
    generator = CallSpreadPDFGenerator()
    pdf_file = generator.generate_pdf_report()
    
    if pdf_file:
        print(f"✅ PDF report generated successfully: {pdf_file}")
    else:
        print("❌ Failed to generate PDF report")
