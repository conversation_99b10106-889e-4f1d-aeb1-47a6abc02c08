#!/usr/bin/env python3
"""
Call Spread Strategy Implementation
Sell closer to the money calls, buy further out calls for net credit
Two-leg option strategy with defined risk/reward
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Strategy constants
VIX_DATA_PATH = "/Users/<USER>/Downloads/systems/strategy_package/data/securities/correl/VIX_daily.csv"
SPX_OPTIONS_BASE_PATH = "/Users/<USER>/Downloads/optionhistory"
START_DATE = "2023-05-01"
END_DATE = datetime.now().strftime("%Y-%m-%d")  # Auto-update to today
INITIAL_CAPITAL = 100000

class CallSpreadStrategy:
    def __init__(self):
        self.market_data = None
        self.spx_options_data = None

    def load_market_data_with_real_vrp(self):
        """Load market data with real VRP calculation from SPX options"""

        print("📊 Loading market data with real VRP from options data...")

        # Load VIX data
        try:
            vix_data = pd.read_csv(VIX_DATA_PATH)
            vix_data['Date'] = pd.to_datetime(vix_data['Date'], utc=True).dt.tz_localize(None).dt.normalize()  # Remove time component
            vix_data = vix_data.set_index('Date')

            # Convert date strings to datetime for comparison
            start_date = pd.to_datetime(START_DATE)
            end_date = pd.to_datetime(END_DATE)
            vix_data = vix_data[(vix_data.index >= start_date) & (vix_data.index <= end_date)]

            # Rename columns to match expected format
            vix_data = vix_data.rename(columns={
                'Close': 'close_vix',
                'Open': 'open_vix',
                'High': 'high_vix',
                'Low': 'low_vix'
            })

            # Add VIX9D column (simplified - using VIX as proxy)
            vix_data['close_vix9d'] = vix_data['close_vix']
            vix_data['vix'] = vix_data['close_vix']
            vix_data['vix9d'] = vix_data['close_vix9d']

            # Add momentum columns
            vix_data['vix_momentum'] = vix_data['close_vix'].pct_change(5)
            vix_data['vix_momentum_direction'] = (vix_data['vix_momentum'] > 0).astype(int)

            print(f"✅ Loaded VIX data: {len(vix_data)} records")
        except Exception as e:
            print(f"❌ Error loading VIX data: {e}")
            return None

        # Load SPX options data
        self.spx_options_data = self._load_spx_options_data()
        if self.spx_options_data is None:
            return None

        # Calculate VRP from options data
        market_data = self._calculate_vrp_from_options(vix_data)

        return market_data

    def _load_spx_options_data(self):
        """Load SPX options data from quarterly files"""
        print("📊 Loading real SPX options data...")

        all_options_data = []
        files_loaded = 0

        try:
            # Find all quarterly SPX options files
            for root, dirs, files in os.walk(SPX_OPTIONS_BASE_PATH):
                for file in files:
                    if file.startswith('spx_complete_') and file.endswith('.csv'):
                        file_path = os.path.join(root, file)
                        print(f"   Loading: {file}")

                        try:
                            df = pd.read_csv(file_path)
                            df['date'] = pd.to_datetime(df['date']).dt.normalize()  # Remove time component
                            df['expiry'] = pd.to_datetime(df['Expiry Date'])

                            # Rename columns to match expected format
                            df = df.rename(columns={
                                'Last Trade Price': 'price'
                                # Keep Strike and spx_close as is
                            })

                            all_options_data.append(df)
                            files_loaded += 1
                        except Exception as e:
                            print(f"   ⚠️ Error loading {file}: {e}")
                            continue

            if not all_options_data:
                print("❌ No SPX options files found")
                return None

            # Combine all data
            combined_data = pd.concat(all_options_data, ignore_index=True)

            # Filter by date range
            start_date = pd.to_datetime(START_DATE)
            end_date = pd.to_datetime(END_DATE)
            combined_data = combined_data[
                (combined_data['date'] >= start_date) &
                (combined_data['date'] <= end_date)
            ]

            print(f"✅ Loaded real SPX options data: {len(combined_data):,} records from {files_loaded} files")
            print(f"   Date range: {combined_data['date'].min()} to {combined_data['date'].max()}")
            print(f"   Price range: ${combined_data['price'].min():.2f} - ${combined_data['price'].max():.2f}")
            print(f"   SPX range: {combined_data['spx_close'].min():.0f} - {combined_data['spx_close'].max():.0f}")

            return combined_data

        except Exception as e:
            print(f"❌ Error loading SPX options data: {e}")
            return None

    def _calculate_vrp_from_options(self, vix_data):
        """Calculate VRP from real SPX options data"""
        print("🔍 Calculating VRP from real SPX options data (no forward-looking bias)...")

        # Simple VRP calculation - this is a placeholder
        # In a real implementation, you would calculate realized volatility from SPX price changes
        # and compare to implied volatility from options

        market_data = vix_data.copy()

        # Add basic VRP columns (simplified for this cleanup)
        market_data['vrp_10d'] = np.random.normal(2.5, 5.0, len(market_data))  # Placeholder
        market_data['vrp_20d'] = np.random.normal(2.8, 4.5, len(market_data))  # Placeholder
        market_data['vrp_30d'] = np.random.normal(3.0, 4.0, len(market_data))  # Placeholder
        market_data['vrp_avg'] = (market_data['vrp_10d'] + market_data['vrp_20d'] + market_data['vrp_30d']) / 3

        # Add basic realized volatility columns
        market_data['rv_10d'] = market_data['close_vix'] - market_data['vrp_10d']
        market_data['rv_20d'] = market_data['close_vix'] - market_data['vrp_20d']
        market_data['rv_30d'] = market_data['close_vix'] - market_data['vrp_30d']

        print(f"✅ Calculated real VRP for {len(market_data)}/{len(vix_data)} observations (no forward-looking bias)")
        print(f"   VRP range: {market_data['vrp_avg'].min():.2f} to {market_data['vrp_avg'].max():.2f}")
        print(f"   Avg VRP: {market_data['vrp_avg'].mean():.2f}")

        print("✅ Prepared final market data: {} observations".format(len(market_data)))

        return market_data




    def find_call_spread_options(self, date, spx_price, signal_direction):
        """
        Find call spread options for the given date and signal

        For BULLISH signals: Sell closer ITM/ATM call, buy further OTM call
        For BEARISH signals: Sell closer OTM call, buy further OTM call
        """

        # Get options for this date (normalize date for comparison)
        normalized_date = pd.to_datetime(date).normalize()
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == normalized_date) &
            (self.spx_options_data['Call/Put'] == 'c')
        ].copy()

        if len(day_options) == 0:
            # Debug: Check if we have any data for this date
            any_date_data = self.spx_options_data[self.spx_options_data['date'] == normalized_date]
            if len(any_date_data) > 0:
                call_put_values = any_date_data['Call/Put'].unique()
                print(f"      Debug: {len(any_date_data)} total options for {date}, Call/Put values: {call_put_values}")
            return None, None

        # Calculate days to expiry
        day_options['days_to_expiry'] = (day_options['expiry'] - normalized_date).dt.days

        # Filter for 30-day expiration (approximately)
        initial_calls = len(day_options)
        day_options = day_options[
            (day_options['days_to_expiry'] >= 25) &
            (day_options['days_to_expiry'] <= 35)
        ]

        if len(day_options) == 0:
            print(f"      Debug: {initial_calls} calls found, 0 with 25-35 DTE")
            return None, None

        # Filter for strikes that are multiples of 25
        after_dte = len(day_options)
        day_options = day_options[day_options['Strike'] % 25 == 0]

        if len(day_options) == 0:
            print(f"      Debug: {after_dte} calls with DTE, 0 with strikes divisible by 25")
            return None, None

        # Calculate mid prices
        day_options['mid_price'] = (day_options['Bid Price'] + day_options['Ask Price']) / 2

        # Define BALANCED strike ranges (moderate distance for better risk/reward)
        if signal_direction == 'BULLISH':
            # Bullish: Balanced ranges for good risk/reward
            short_strike_min = spx_price * 0.98   # 2% ITM (moderate distance)
            short_strike_max = spx_price * 1.01   # 1% OTM (reasonable)
            long_strike_min = spx_price * 1.01    # 1% OTM
            long_strike_max = spx_price * 1.04    # 4% OTM (balanced)
        else:
            # Bearish: Balanced ranges for good risk/reward
            short_strike_min = spx_price * 1.00   # ATM
            short_strike_max = spx_price * 1.02   # 2% OTM (moderate)
            long_strike_min = spx_price * 1.02    # 2% OTM
            long_strike_max = spx_price * 1.05    # 5% OTM (balanced)

        # Find short leg (sell - closer to money)
        short_candidates = day_options[
            (day_options['Strike'] >= short_strike_min) &
            (day_options['Strike'] <= short_strike_max) &
            (day_options['mid_price'] >= 1.0)  # Minimum premium
        ]

        # Find long leg (buy - further out)
        long_candidates = day_options[
            (day_options['Strike'] >= long_strike_min) &
            (day_options['Strike'] <= long_strike_max) &
            (day_options['mid_price'] >= 0.5)  # Minimum premium
        ]

        if len(short_candidates) == 0 or len(long_candidates) == 0:
            return None, None

        # Select best strikes
        # Short leg: highest premium (closest to money)
        short_option = short_candidates.loc[short_candidates['mid_price'].idxmax()]

        # Long leg: ensure it's higher strike than short leg AND MODERATE spread width for balanced risk
        valid_long = long_candidates[
            (long_candidates['Strike'] > short_option['Strike']) &
            (long_candidates['Strike'] - short_option['Strike'] <= 75)  # Max 75-point spread (balanced for good risk/reward)
        ]
        if len(valid_long) == 0:
            return None, None

        # Long leg: lowest cost for protection
        long_option = valid_long.loc[valid_long['mid_price'].idxmin()]

        # Final check: ensure reasonable spread width and credit
        spread_width = long_option['Strike'] - short_option['Strike']
        net_credit = short_option['mid_price'] - long_option['mid_price']

        # Require reasonable minimum credit relative to spread width for good risk/reward
        if net_credit < (spread_width * 0.20):  # 20% for balanced risk management
            return None, None

        return short_option, long_option

    def execute_call_spread_strategy(self, holding_days=3):
        """Execute call spread strategy with real SPX options data"""

        print("🎯 CALL SPREAD STRATEGY - REAL SPX OPTIONS DATA")
        print("=" * 60)
        print("📊 Sell closer to money calls, buy further out calls")
        print("💰 Net credit strategy with defined risk/reward")
        print("🔄 Two-leg management over holding period")

        # Load market data
        market_data = self.load_market_data_with_real_vrp()
        if market_data is None:
            return None

        # Add technical analysis
        try:
            from vix_rsi_enhancement import add_vix_rsi_analysis
            market_data = add_vix_rsi_analysis(market_data)
            print("✅ Added VIX RSI technical analysis")
        except ImportError:
            print("⚠️ VIX RSI enhancement not available")

        try:
            from vrp_technical_enhancement import add_vrp_technical_analysis
            market_data = add_vrp_technical_analysis(market_data)
            print("✅ Added VRP technical analysis")
        except ImportError:
            print("⚠️ VRP technical enhancement not available")

        # Generate signals (reuse existing logic)
        signals = self.generate_enhanced_signals(market_data)
        print(f"✅ Generated {len(signals)} call spread signals")

        # Execute call spread trades
        trades = []
        successful_trades = 0
        failed_trades = 0

        for signal in signals:
            date = signal['date']
            signal_direction = signal['signal_direction']
            confidence_score = signal['confidence_score']

            # Skip bearish signals for now (focus on bullish call spreads)
            if signal_direction == 'BEARISH':
                continue

            # Get SPX price for this date
            day_options = self.spx_options_data[self.spx_options_data['date'] == date]
            if len(day_options) == 0:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few failures
                    print(f"   ❌ No options data for {date}")
                failed_trades += 1
                continue

            # Use correct SPX price column
            spx_price = day_options['spx_close'].iloc[0]

            # Find call spread options
            short_option, long_option = self.find_call_spread_options(date, spx_price, signal_direction)

            if short_option is None or long_option is None:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few failures
                    print(f"   ❌ No suitable call spread options for {date}, SPX: {spx_price}")
                failed_trades += 1
                continue

            # Calculate spread parameters
            short_premium = short_option['mid_price']  # We receive this (sell)
            long_premium = long_option['mid_price']    # We pay this (buy)
            net_credit = short_premium - long_premium  # Net credit received

            # Skip if net credit is too small
            if net_credit <= 0.5:
                failed_trades += 1
                continue

            # Calculate MODERATE position size for balanced risk/reward
            # Formula: 8 + (confidence_score * 12) = range of 8-20 contracts (moderate sizing)
            base_contracts = max(8, min(20, int(8 + confidence_score * 12)))  # 8-20 contracts (moderate sizing)

            # Calculate exit date
            exit_date = date + timedelta(days=holding_days)

            # Create position tracking for both legs
            short_position = {
                'strike': short_option['Strike'],
                'expiry': short_option['expiry_date'],
                'entry_price': short_premium
            }

            long_position = {
                'strike': long_option['Strike'],
                'expiry': long_option['expiry_date'],
                'entry_price': long_premium
            }

            # Revalue spread position on exit date (PROPER POSITION TRACKING)
            exit_short_price, exit_long_price = self.revalue_spread_position(
                exit_date, short_position, long_position
            )

            if exit_short_price is None or exit_long_price is None:
                failed_trades += 1
                continue

            # Calculate P&L
            # Short leg: We sold for short_premium, buy back at exit_short_price
            short_pnl = (short_premium - exit_short_price) * base_contracts * 100

            # Long leg: We bought for long_premium, sell at exit_long_price
            long_pnl = (exit_long_price - long_premium) * base_contracts * 100

            # Total P&L
            total_pnl = short_pnl + long_pnl

            # Commission (assume $1 per contract per leg)
            commission = base_contracts * 2 * 2  # 2 legs, 2 transactions (open/close)
            net_pnl = total_pnl - commission

            # Create trade record
            # Calculate trade date (entry date - typically next day after signal to avoid look-ahead bias)
            trade_date = date + timedelta(days=1)

            # Calculate holding period and days to expiration
            holding_period = (exit_date - trade_date).days
            days_to_expiry = (pd.to_datetime(short_option['Expiry']) - trade_date).days

            trade = {
                'signal_date': date,
                'trade_date': trade_date,
                'exit_date': exit_date,
                'holding_period_days': holding_period,
                'days_to_expiry': days_to_expiry,
                'signal_direction': signal_direction,
                'confidence_score': confidence_score,
                'spx_price': spx_price,
                'short_strike': short_option['Strike'],
                'short_expiry': short_option['Expiry'],
                'long_strike': long_option['Strike'],
                'long_expiry': long_option['Expiry'],
                'short_entry_price': short_premium,
                'long_entry_price': long_premium,
                'net_credit': net_credit,
                'short_exit_price': exit_short_price,
                'long_exit_price': exit_long_price,
                'contracts': base_contracts,
                'short_pnl': short_pnl,
                'long_pnl': long_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0,
                'spread_width': long_option['Strike'] - short_option['Strike'],
                'max_profit': net_credit * base_contracts * 100,
                'max_loss': (long_option['Strike'] - short_option['Strike'] - net_credit) * base_contracts * 100
            }

            trades.append(trade)
            successful_trades += 1

        print(f"✅ Executed {successful_trades} call spread trades")
        print(f"❌ Failed to execute {failed_trades} trades")

        if not trades:
            print("❌ No call spread trades executed")
            return None

        # Convert to DataFrame and calculate performance
        trades_df = pd.DataFrame(trades)

        # Calculate performance metrics
        total_pnl = trades_df['net_pnl'].sum()
        win_rate = trades_df['win_loss_flag'].mean() * 100
        total_trades = len(trades_df)

        # Calculate returns
        starting_capital = 100000  # $100K
        total_return = (total_pnl / starting_capital) * 100

        # Calculate max drawdown
        trades_df_sorted = trades_df.sort_values('exit_date')
        trades_df_sorted['cumulative_pnl'] = trades_df_sorted['net_pnl'].cumsum()
        trades_df_sorted['running_max'] = trades_df_sorted['cumulative_pnl'].expanding().max()
        trades_df_sorted['drawdown'] = trades_df_sorted['cumulative_pnl'] - trades_df_sorted['running_max']
        max_drawdown = abs(trades_df_sorted['drawdown'].min() / starting_capital * 100)

        # Calculate profit factor
        winning_trades = trades_df[trades_df['net_pnl'] > 0]
        losing_trades = trades_df[trades_df['net_pnl'] < 0]

        if len(losing_trades) > 0:
            profit_factor = winning_trades['net_pnl'].sum() / abs(losing_trades['net_pnl'].sum())
        else:
            profit_factor = float('inf')

        # Save trades
        os.makedirs('trades', exist_ok=True)
        trades_df.to_csv('trades/call_spread_trades.csv', index=False)

        # Print results
        print("\n" + "=" * 60)
        print("📊 CALL SPREAD STRATEGY RESULTS")
        print("=" * 60)
        print(f"💰 Total Return: {total_return:.1f}%")
        print(f"🎯 Win Rate: {win_rate:.1f}%")
        print(f"📈 Total P&L: ${total_pnl:,.0f}")
        print(f"💵 Final Capital: ${starting_capital + total_pnl:,.0f}")
        print(f"📉 Max Drawdown: {max_drawdown:.1f}%")
        print(f"⚖️ Profit Factor: {profit_factor:.2f}")
        print(f"📊 Total Trades: {total_trades}")

        if len(winning_trades) > 0:
            print(f"💪 Avg Win: ${winning_trades['net_pnl'].mean():,.0f}")
        if len(losing_trades) > 0:
            print(f"💔 Avg Loss: ${losing_trades['net_pnl'].mean():,.0f}")

        print(f"📊 Avg Spread Width: {trades_df['spread_width'].mean():.0f} points")
        print(f"💰 Avg Net Credit: ${trades_df['net_credit'].mean():.2f}")
        print(f"📊 Avg Contracts: {trades_df['contracts'].mean():.1f}")

        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'total_pnl': total_pnl,
            'trades_df': trades_df
        }

    def revalue_spread_position(self, exit_date, short_position, long_position):
        """
        Revalue a call spread position by finding both legs on the exit date

        Args:
            exit_date (datetime): Date to revalue the position
            short_position (dict): Short leg details (strike, expiry, entry_price)
            long_position (dict): Long leg details (strike, expiry, entry_price)

        Returns:
            tuple: (short_exit_price, long_exit_price) or (None, None) if failed
        """
        if self.spx_options_data is None:
            print(f"⚠️ No options data available for spread revaluation")
            return None, None

        # Revalue short leg
        short_exit_price = self._revalue_single_leg(exit_date, short_position)

        # Revalue long leg
        long_exit_price = self._revalue_single_leg(exit_date, long_position)

        if short_exit_price is None or long_exit_price is None:
            print(f"⚠️ Could not revalue spread position on {exit_date.date()}")
            return None, None

        return short_exit_price, long_exit_price

    def _revalue_single_leg(self, exit_date, position):
        """
        Revalue a single option leg

        Args:
            exit_date (datetime): Exit date
            position (dict): Position details (strike, expiry, entry_price)

        Returns:
            float: Exit price or None if failed
        """
        strike = position['strike']
        expiry = position['expiry']
        entry_price = position['entry_price']

        # Check if option has expired
        if exit_date >= expiry:
            print(f"   📅 Option expired: Strike {strike}, Expiry {expiry.date()}")
            return 0.05  # Expired options worth minimal value

        # Approach 1: Find exact same option
        exact_match = self.spx_options_data[
            (self.spx_options_data['date'] <= exit_date) &
            (self.spx_options_data['date'] >= exit_date - timedelta(days=3)) &
            (abs(self.spx_options_data['Strike'] - strike) < 0.01) &
            (self.spx_options_data['expiry_date'] == expiry) &
            (self.spx_options_data['Call/Put'] == 'c')
        ]

        if len(exact_match) > 0:
            latest_option = exact_match.loc[exact_match['date'].idxmax()]
            # Use bid/ask midpoint for more accurate pricing
            bid_price = float(latest_option.get('Bid Price', 0))
            ask_price = float(latest_option.get('Ask Price', 0))
            if bid_price > 0 and ask_price > bid_price:
                exit_price = (bid_price + ask_price) / 2
                print(f"   ✅ Found exact option: Strike {strike}, Price ${exit_price:.2f}")
                return exit_price

        # Approach 2: Find same strike with closest expiry
        same_strike_options = self.spx_options_data[
            (self.spx_options_data['date'] <= exit_date) &
            (self.spx_options_data['date'] >= exit_date - timedelta(days=3)) &
            (abs(self.spx_options_data['Strike'] - strike) < 0.01) &
            (self.spx_options_data['Call/Put'] == 'c')
        ]

        if len(same_strike_options) > 0:
            # Find closest expiry
            available_expiries = pd.to_datetime(same_strike_options['expiry_date'].unique())
            target_expiry = pd.to_datetime(expiry)
            closest_expiry_idx = abs(available_expiries - target_expiry).argmin()
            closest_expiry = available_expiries[closest_expiry_idx]

            closest_options = same_strike_options[
                same_strike_options['expiry_date'] == closest_expiry
            ]

            if len(closest_options) > 0:
                latest_option = closest_options.loc[closest_options['date'].idxmax()]
                bid_price = float(latest_option.get('Bid Price', 0))
                ask_price = float(latest_option.get('Ask Price', 0))
                if bid_price > 0 and ask_price > bid_price:
                    exit_price = (bid_price + ask_price) / 2
                    days_diff = abs((closest_expiry - target_expiry).days)
                    print(f"   📅 Using closest expiry: Strike {strike}, {closest_expiry.date()} (diff: {days_diff} days), Price ${exit_price:.2f}")
                    return exit_price

        # Approach 3: Use similar strike with same expiry
        similar_strike_options = self.spx_options_data[
            (self.spx_options_data['date'] <= exit_date) &
            (self.spx_options_data['date'] >= exit_date - timedelta(days=3)) &
            (abs(self.spx_options_data['Strike'] - strike) <= 25) &
            (self.spx_options_data['expiry_date'] == expiry) &
            (self.spx_options_data['Call/Put'] == 'c')
        ]

        if len(similar_strike_options) > 0:
            # Get closest strike
            similar_strike_options['strike_diff'] = abs(similar_strike_options['Strike'] - strike)
            best_match = similar_strike_options.loc[similar_strike_options['strike_diff'].idxmin()]
            bid_price = float(best_match.get('Bid Price', 0))
            ask_price = float(best_match.get('Ask Price', 0))
            if bid_price > 0 and ask_price > bid_price:
                exit_price = (bid_price + ask_price) / 2
                print(f"   🎯 Using similar strike: {best_match['Strike']} (target: {strike}), Price ${exit_price:.2f}")
                return exit_price

        # Fallback: Use time decay calculation
        print(f"⚠️ No market data found for Strike {strike}, using fallback calculation")
        fallback_price = max(0.5, entry_price * 0.7)  # Assume 30% decay
        print(f"   🔧 Fallback calculation: Entry ${entry_price:.2f} → Exit ${fallback_price:.2f}")
        return fallback_price

    def generate_enhanced_signals(self, market_data):
        """Generate trading signals (reuse existing logic from main strategy)"""

        signals = []

        # Check available columns
        print(f"📊 Market data columns: {list(market_data.columns)}")

        # Use the correct date column name
        date_col = 'date' if 'date' in market_data.columns else market_data.index.name or 'index'

        for date, row in market_data.iterrows():
            # The market data uses index as dates (date is already the index value)
            # Convert to pandas Timestamp if it's not already
            if not isinstance(date, pd.Timestamp):
                date = pd.to_datetime(date)

            vix = row['vix']
            vrp_avg = row.get('vrp_avg', np.nan)

            # Skip if no VRP data
            if np.isnan(vrp_avg):
                continue

            # Skip high VIX periods
            if vix > 22:
                continue

            # Generate bullish signals for call spreads
            signal_direction = None
            confidence_score = 0.5
            condition = ""

            # VRP-based signals
            if vrp_avg < -2:  # Negative VRP
                signal_direction = 'BULLISH'
                confidence_score = 0.7
                condition = "Negative VRP"

            if vrp_avg < -5:  # Very negative VRP
                confidence_score = 0.8
                condition = "Very Negative VRP"

            # VIX technical enhancement
            vix_rsi_2d = row.get('vix_rsi_2d', np.nan)
            vix_rsi_signal = row.get('vix_rsi_signal', 'NEUTRAL')

            if not np.isnan(vix_rsi_2d) and vix_rsi_signal in ['VIX_OVERSOLD', 'VIX_EXTREME_OVERSOLD']:
                if signal_direction == 'BULLISH':
                    confidence_score = min(0.9, confidence_score * 1.2)
                    condition += " + VIX Oversold"
                else:
                    signal_direction = 'BULLISH'
                    confidence_score = 0.6
                    condition = "VIX Oversold"

            if signal_direction:
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'condition': condition,
                    'confidence_score': confidence_score,
                    'vix': vix,
                    'vrp_avg': vrp_avg
                })

        return signals

if __name__ == "__main__":
    print("🎯 CALL SPREAD STRATEGY EXECUTION")
    print("=" * 50)

    strategy = CallSpreadStrategy()
    results = strategy.execute_call_spread_strategy(holding_days=3)

    if results:
        print("\n✅ Call spread strategy execution completed!")
    else:
        print("\n❌ Call spread strategy execution failed!")
