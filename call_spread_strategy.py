#!/usr/bin/env python3
"""
Call Spread Strategy Implementation
Sell closer to the money calls, buy further out calls for net credit
Two-leg option strategy with defined risk/reward
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from final_strategy_constants import *

class CallSpreadStrategy:
    def __init__(self):
        self.market_data = None
        self.spx_options_data = None
        
    def load_market_data_with_real_vrp(self):
        """Load market data with real VRP calculation from SPX options"""

        print("📊 Loading market data with real VRP from options data...")

        # Import and use existing data loading logic
        try:
            from final_strategy_clean import FinalRealDataStrategy
            base_strategy = FinalRealDataStrategy()
            market_data = base_strategy.load_market_data_with_real_vrp()

            if market_data is None:
                print("❌ Failed to load market data from main strategy")
                return None

            print(f"✅ Loaded market data: {len(market_data)} records")

            # Also load the SPX options data for spread trading
            self.spx_options_data = base_strategy.spx_options_data
            print(f"✅ Loaded SPX options data: {len(self.spx_options_data):,} records")

            return market_data

        except Exception as e:
            print(f"❌ Error loading market data: {e}")
            return None
        

    

    
    def find_call_spread_options(self, date, spx_price, signal_direction):
        """
        Find call spread options for the given date and signal
        
        For BULLISH signals: Sell closer ITM/ATM call, buy further OTM call
        For BEARISH signals: Sell closer OTM call, buy further OTM call
        """
        
        # Get options for this date
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == date) &
            (self.spx_options_data['Call/Put'] == 'c')
        ].copy()

        if len(day_options) == 0:
            # Debug: Check if we have any data for this date
            any_date_data = self.spx_options_data[self.spx_options_data['date'] == date]
            if len(any_date_data) > 0:
                call_put_values = any_date_data['Call/Put'].unique()
                print(f"      Debug: {len(any_date_data)} total options for {date}, Call/Put values: {call_put_values}")
            return None, None

        # Calculate days to expiry
        day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days

        # Filter for 30-day expiration (approximately)
        day_options = day_options[
            (day_options['days_to_expiry'] >= 25) &
            (day_options['days_to_expiry'] <= 35)
        ]

        if len(day_options) == 0:
            return None, None

        # Filter for strikes that are multiples of 5 (SPX trades in 5-point increments)
        after_dte = len(day_options)
        day_options = day_options[day_options['Strike'] % 5 == 0]

        if len(day_options) == 0:
            print(f"      Debug: {after_dte} calls with DTE, 0 with strikes divisible by 5")
            return None, None

        # Use Last Trade Price as the actual option price
        day_options['option_price'] = day_options['Last Trade Price']
        
        # Simple strike selection: 25 points below SPX, 150 points apart
        # Round SPX price to nearest 25 for strike selection
        spx_rounded = round(spx_price / 25) * 25

        # Target strikes
        short_strike = spx_rounded - 25  # 25 points below current price
        long_strike = short_strike + 150  # 150 points above short strike

        # Find exact strikes
        short_option = day_options[day_options['Strike'] == short_strike]
        long_option = day_options[day_options['Strike'] == long_strike]

        if len(short_option) == 0 or len(long_option) == 0:
            print(f"      Debug: SPX {spx_price:.0f}, target short {short_strike}, long {long_strike}")
            print(f"      Short matches: {len(short_option)}, Long matches: {len(long_option)}")
            return None, None

        # Take first match for each
        short_option = short_option.iloc[0]
        long_option = long_option.iloc[0]

        # Simple credit check - just ensure we get some credit
        net_credit = short_option['option_price'] - long_option['option_price']
        if net_credit <= 0:
            return None, None
        
        return short_option, long_option

    def execute_call_spread_strategy(self, holding_days=3):
        """Execute call spread strategy with real SPX options data"""

        print("🎯 CALL SPREAD STRATEGY - REAL SPX OPTIONS DATA")
        print("=" * 60)
        print("📊 Sell closer to money calls, buy further out calls")
        print("💰 Net credit strategy with defined risk/reward")
        print("🔄 Two-leg management over holding period")

        # Load market data
        market_data = self.load_market_data_with_real_vrp()
        if market_data is None:
            return None

        # Add technical analysis
        try:
            from vix_rsi_enhancement import add_vix_rsi_analysis
            market_data = add_vix_rsi_analysis(market_data)
            print("✅ Added VIX RSI technical analysis")
        except ImportError:
            print("⚠️ VIX RSI enhancement not available")

        try:
            from vrp_technical_enhancement import add_vrp_technical_analysis
            market_data = add_vrp_technical_analysis(market_data)
            print("✅ Added VRP technical analysis")
        except ImportError:
            print("⚠️ VRP technical enhancement not available")

        # Generate signals (reuse existing logic)
        signals = self.generate_enhanced_signals(market_data)
        print(f"✅ Generated {len(signals)} call spread signals")

        # Execute call spread trades
        trades = []
        successful_trades = 0
        failed_trades = 0

        for signal in signals:
            date = signal['date']
            signal_direction = signal['signal_direction']
            confidence_score = signal['confidence_score']

            # Skip bearish signals for now (focus on bullish call spreads)
            if signal_direction == 'BEARISH':
                continue

            # Get SPX price for this date
            day_options = self.spx_options_data[self.spx_options_data['date'] == date]
            if len(day_options) == 0:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few failures
                    print(f"   ❌ No options data for {date}")
                failed_trades += 1
                continue

            # Use correct SPX price column
            spx_price = day_options['spx_close'].iloc[0]

            # Find call spread options
            short_option, long_option = self.find_call_spread_options(date, spx_price, signal_direction)

            if short_option is None or long_option is None:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few failures
                    print(f"   ❌ No suitable call spread options for {date}, SPX: {spx_price}")
                failed_trades += 1
                continue

            # Calculate spread parameters
            short_premium = short_option['option_price']  # We receive this (sell)
            long_premium = long_option['option_price']    # We pay this (buy)
            net_credit = short_premium - long_premium  # Net credit received

            # Skip if net credit is too small
            if net_credit <= 0.5:
                failed_trades += 1
                continue

            # Calculate ENHANCED position size based on confidence (INSTITUTIONAL SIZES)
            # Formula: 20 + (confidence_score * 40) = range of 20-60 contracts
            base_contracts = max(20, min(60, int(20 + confidence_score * 40)))  # 20-60 contracts (institutional sizes)

            # Calculate exit date
            exit_date = date + timedelta(days=holding_days)

            # Create position tracking for both legs
            short_position = {
                'strike': short_option['Strike'],
                'expiry': short_option['expiry_date'],
                'entry_price': short_premium
            }

            long_position = {
                'strike': long_option['Strike'],
                'expiry': long_option['expiry_date'],
                'entry_price': long_premium
            }

            # Revalue spread position on exit date (PROPER POSITION TRACKING)
            exit_short_price, exit_long_price = self.revalue_spread_position(
                exit_date, short_position, long_position
            )

            if exit_short_price is None or exit_long_price is None:
                failed_trades += 1
                continue

            # Calculate P&L
            # Short leg: We sold for short_premium, buy back at exit_short_price
            short_pnl = (short_premium - exit_short_price) * base_contracts * 100

            # Long leg: We bought for long_premium, sell at exit_long_price
            long_pnl = (exit_long_price - long_premium) * base_contracts * 100

            # Total P&L
            total_pnl = short_pnl + long_pnl

            # Commission (assume $1 per contract per leg)
            commission = base_contracts * 2 * 2  # 2 legs, 2 transactions (open/close)
            net_pnl = total_pnl - commission

            # Create trade record
            # Calculate entry date (typically next day after signal to avoid look-ahead bias)
            entry_date = date + timedelta(days=1)

            trade = {
                'signal_date': date,
                'entry_date': entry_date,
                'exit_date': exit_date,
                'signal_direction': signal_direction,
                'confidence_score': confidence_score,
                'spx_price': spx_price,
                'short_strike': short_option['Strike'],
                'long_strike': long_option['Strike'],
                'short_entry_price': short_premium,
                'long_entry_price': long_premium,
                'net_credit': net_credit,
                'short_exit_price': exit_short_price,
                'long_exit_price': exit_long_price,
                'contracts': base_contracts,
                'short_pnl': short_pnl,
                'long_pnl': long_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0,
                'spread_width': long_option['Strike'] - short_option['Strike'],
                'max_profit': net_credit * base_contracts * 100,
                'max_loss': (long_option['Strike'] - short_option['Strike'] - net_credit) * base_contracts * 100
            }

            trades.append(trade)
            successful_trades += 1

        print(f"✅ Executed {successful_trades} call spread trades")
        print(f"❌ Failed to execute {failed_trades} trades")

        if not trades:
            print("❌ No call spread trades executed")
            return None

        # Convert to DataFrame and calculate performance
        trades_df = pd.DataFrame(trades)

        # Calculate performance metrics
        total_pnl = trades_df['net_pnl'].sum()
        win_rate = trades_df['win_loss_flag'].mean() * 100
        total_trades = len(trades_df)

        # Calculate returns
        starting_capital = 100000  # $100K
        total_return = (total_pnl / starting_capital) * 100

        # Calculate max drawdown
        trades_df_sorted = trades_df.sort_values('exit_date')
        trades_df_sorted['cumulative_pnl'] = trades_df_sorted['net_pnl'].cumsum()
        trades_df_sorted['running_max'] = trades_df_sorted['cumulative_pnl'].expanding().max()
        trades_df_sorted['drawdown'] = trades_df_sorted['cumulative_pnl'] - trades_df_sorted['running_max']
        max_drawdown = abs(trades_df_sorted['drawdown'].min() / starting_capital * 100)

        # Calculate profit factor
        winning_trades = trades_df[trades_df['net_pnl'] > 0]
        losing_trades = trades_df[trades_df['net_pnl'] < 0]

        if len(losing_trades) > 0:
            profit_factor = winning_trades['net_pnl'].sum() / abs(losing_trades['net_pnl'].sum())
        else:
            profit_factor = float('inf')

        # Save trades
        os.makedirs('trades', exist_ok=True)
        trades_df.to_csv('trades/call_spread_trades.csv', index=False)

        # Print results
        print("\n" + "=" * 60)
        print("📊 CALL SPREAD STRATEGY RESULTS")
        print("=" * 60)
        print(f"💰 Total Return: {total_return:.1f}%")
        print(f"🎯 Win Rate: {win_rate:.1f}%")
        print(f"📈 Total P&L: ${total_pnl:,.0f}")
        print(f"💵 Final Capital: ${starting_capital + total_pnl:,.0f}")
        print(f"📉 Max Drawdown: {max_drawdown:.1f}%")
        print(f"⚖️ Profit Factor: {profit_factor:.2f}")
        print(f"📊 Total Trades: {total_trades}")

        if len(winning_trades) > 0:
            print(f"💪 Avg Win: ${winning_trades['net_pnl'].mean():,.0f}")
        if len(losing_trades) > 0:
            print(f"💔 Avg Loss: ${losing_trades['net_pnl'].mean():,.0f}")

        print(f"📊 Avg Spread Width: {trades_df['spread_width'].mean():.0f} points")
        print(f"💰 Avg Net Credit: ${trades_df['net_credit'].mean():.2f}")
        print(f"📊 Avg Contracts: {trades_df['contracts'].mean():.1f}")

        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'total_pnl': total_pnl,
            'trades_df': trades_df
        }

    def revalue_spread_position(self, exit_date, short_position, long_position):
        """
        Revalue a call spread position by finding both legs on the exit date

        Args:
            exit_date (datetime): Date to revalue the position
            short_position (dict): Short leg details (strike, expiry, entry_price)
            long_position (dict): Long leg details (strike, expiry, entry_price)

        Returns:
            tuple: (short_exit_price, long_exit_price) or (None, None) if failed
        """
        if self.spx_options_data is None:
            print(f"⚠️ No options data available for spread revaluation")
            return None, None

        # Revalue short leg
        short_exit_price = self._revalue_single_leg(exit_date, short_position)

        # Revalue long leg
        long_exit_price = self._revalue_single_leg(exit_date, long_position)

        if short_exit_price is None or long_exit_price is None:
            print(f"⚠️ Could not revalue spread position on {exit_date.date()}")
            return None, None

        return short_exit_price, long_exit_price

    def _revalue_single_leg(self, exit_date, position):
        """
        Revalue a single option leg

        Args:
            exit_date (datetime): Exit date
            position (dict): Position details (strike, expiry, entry_price)

        Returns:
            float: Exit price or None if failed
        """
        strike = position['strike']
        expiry = position['expiry']
        entry_price = position['entry_price']

        # Check if option has expired
        if exit_date >= expiry:
            print(f"   📅 Option expired: Strike {strike}, Expiry {expiry.date()}")
            return 0.05  # Expired options worth minimal value

        # Find exact same option on exact exit date only
        exact_match = self.spx_options_data[
            (self.spx_options_data['date'] == exit_date) &
            (self.spx_options_data['Strike'] == strike) &
            (self.spx_options_data['expiry_date'] == expiry) &
            (self.spx_options_data['Call/Put'] == 'c')
        ]

        if len(exact_match) > 0:
            option = exact_match.iloc[0]
            # Use Last Trade Price
            exit_price = float(option.get('Last Trade Price', 0))
            if exit_price > 0:
                print(f"   ✅ Found exact option: Strike {strike}, Price ${exit_price:.2f}")
                return exit_price

        # If exact match not found, return None to indicate no real market data available
        print(f"   ❌ No exact option found for Strike {strike} on {exit_date.date()}")
        return None

    def generate_enhanced_signals(self, market_data):
        """Generate trading signals (reuse existing logic from main strategy)"""

        signals = []

        # Check available columns
        print(f"📊 Market data columns: {list(market_data.columns)}")

        # Process each trading day

        for date, row in market_data.iterrows():
            # The market data uses index as dates (date is already the index value)
            # Convert to pandas Timestamp if it's not already
            if not isinstance(date, pd.Timestamp):
                date = pd.to_datetime(date)

            vix = row['vix']
            vrp_avg = row.get('vrp_avg', np.nan)

            # Skip if no VRP data
            if np.isnan(vrp_avg):
                continue

            # Skip high VIX periods
            if vix > 22:
                continue

            # Generate bullish signals for call spreads
            signal_direction = None
            confidence_score = 0.5
            condition = ""

            # VRP-based signals
            if vrp_avg < -2:  # Negative VRP
                signal_direction = 'BULLISH'
                confidence_score = 0.7
                condition = "Negative VRP"

            if vrp_avg < -5:  # Very negative VRP
                confidence_score = 0.8
                condition = "Very Negative VRP"

            # VIX technical enhancement
            vix_rsi_2d = row.get('vix_rsi_2d', np.nan)
            vix_rsi_signal = row.get('vix_rsi_signal', 'NEUTRAL')

            if not np.isnan(vix_rsi_2d) and vix_rsi_signal in ['VIX_OVERSOLD', 'VIX_EXTREME_OVERSOLD']:
                if signal_direction == 'BULLISH':
                    confidence_score = min(0.9, confidence_score * 1.2)
                    condition += " + VIX Oversold"
                else:
                    signal_direction = 'BULLISH'
                    confidence_score = 0.6
                    condition = "VIX Oversold"

            if signal_direction:
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'condition': condition,
                    'confidence_score': confidence_score,
                    'vix': vix,
                    'vrp_avg': vrp_avg
                })

        return signals

if __name__ == "__main__":
    print("🎯 CALL SPREAD STRATEGY EXECUTION")
    print("=" * 50)

    strategy = CallSpreadStrategy()
    results = strategy.execute_call_spread_strategy(holding_days=3)

    if results:
        print("\n✅ Call spread strategy execution completed!")
    else:
        print("\n❌ Call spread strategy execution failed!")
