#!/usr/bin/env python3

import pandas as pd
import sys

print("🔍 CHECKING ACTUAL OPTIONS DATA")
print("=" * 40)

# Load the strategy
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()

print(f"✅ Loaded options data: {len(strategy.spx_options_data):,} records")
print(f"Date range: {strategy.spx_options_data['date'].min()} to {strategy.spx_options_data['date'].max()}")

# Check what dates we actually have data for
target_date = pd.to_datetime('2024-08-13')
print(f"\n🔍 CHECKING DATA FOR {target_date.date()}:")

# Check if we have any data for this date
date_data = strategy.spx_options_data[strategy.spx_options_data['date'] == target_date]
print(f"   Total options for this date: {len(date_data)}")

if len(date_data) > 0:
    print(f"   Call options: {len(date_data[date_data['Call/Put'] == 'c'])}")
    print(f"   Put options: {len(date_data[date_data['Call/Put'] == 'p'])}")
    
    # Check strike range
    calls = date_data[date_data['Call/Put'] == 'c']
    if len(calls) > 0:
        print(f"   Strike range: {calls['Strike'].min():.0f} - {calls['Strike'].max():.0f}")
        print(f"   Price range: ${calls['Last Trade Price'].min():.2f} - ${calls['Last Trade Price'].max():.2f}")
        
        # Check DTE
        calls['days_to_expiry'] = (calls['expiry_date'] - calls['date']).dt.days
        print(f"   DTE range: {calls['days_to_expiry'].min()} - {calls['days_to_expiry'].max()}")
        
        # Check 25-35 DTE options
        dte_filtered = calls[(calls['days_to_expiry'] >= 25) & (calls['days_to_expiry'] <= 35)]
        print(f"   25-35 DTE options: {len(dte_filtered)}")
        
        if len(dte_filtered) > 0:
            print(f"   25-35 DTE strikes: {sorted(dte_filtered['Strike'].unique())[:10]}")
else:
    print(f"   ❌ NO DATA FOUND for {target_date.date()}")
    
    # Check nearby dates
    print(f"\n🔍 CHECKING NEARBY DATES:")
    for days_offset in [-3, -2, -1, 1, 2, 3]:
        check_date = target_date + pd.Timedelta(days=days_offset)
        nearby_data = strategy.spx_options_data[strategy.spx_options_data['date'] == check_date]
        print(f"   {check_date.date()}: {len(nearby_data)} options")

# Check what dates we actually have in the data
print(f"\n📊 AVAILABLE DATES SAMPLE:")
unique_dates = sorted(strategy.spx_options_data['date'].unique())
print(f"   Total unique dates: {len(unique_dates)}")
print(f"   First 10 dates: {[d.strftime('%Y-%m-%d') for d in unique_dates[:10]]}")
print(f"   Last 10 dates: {[d.strftime('%Y-%m-%d') for d in unique_dates[-10:]]}")

# Check if 2024-08-13 is a weekend
import datetime
check_weekday = target_date.weekday()
weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
print(f"\n📅 DATE INFO:")
print(f"   {target_date.date()} is a {weekday_names[check_weekday]}")
if check_weekday >= 5:
    print(f"   ⚠️ This is a weekend - no trading data expected")

print(f"\n🎯 CONCLUSION:")
if len(date_data) == 0:
    print(f"   ❌ No options data for target date")
    print(f"   This explains why the strategy finds no options")
    print(f"   Need to check if date is valid trading day or data issue")
else:
    print(f"   ✅ Options data exists for target date")
    print(f"   Strategy should be able to find options")
    print(f"   Bug is likely in the strategy's filtering logic")
