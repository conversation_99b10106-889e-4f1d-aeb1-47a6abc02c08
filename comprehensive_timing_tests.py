#!/usr/bin/env python3
"""
Comprehensive Timing Tests for Enhanced Reverse Signal Strategy
Tests 1-5 day holding periods with 4 different timing scenarios:
- Open->Close, Open->Open, Close->Open, Close->Close
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class ComprehensiveTimingTests:
    """Test different holding periods and timing scenarios"""
    
    def __init__(self, start_date=DEFAULT_START_DATE, end_date=DEFAULT_END_DATE):
        self.start_date = start_date
        self.end_date = end_date
        self.capital = STARTING_CAPITAL
        
        # Define timing scenarios
        self.timing_scenarios = {
            'open_to_close': {
                'name': 'Open->Close',
                'entry_time': 'open',
                'exit_time': 'close',
                'description': 'Enter at market open, exit at market close'
            },
            'open_to_open': {
                'name': 'Open->Open',
                'entry_time': 'open',
                'exit_time': 'open',
                'description': 'Enter at market open, exit at market open'
            },
            'close_to_open': {
                'name': 'Close->Open',
                'entry_time': 'close',
                'exit_time': 'open',
                'description': 'Enter at market close, exit at market open'
            },
            'close_to_close': {
                'name': 'Close->Close',
                'entry_time': 'close',
                'exit_time': 'close',
                'description': 'Enter at market close, exit at market close'
            }
        }
        
        # Holding periods to test
        self.holding_periods = [1, 2, 3, 4, 5]
        
        # Results storage
        self.test_results = {}
        
        # Confidence levels (from enhanced strategy)
        self.confidence_levels = {
            'Very High VIX (Original)': CONFIDENCE_VERY_HIGH_VIX,
            'Low VIX (Reversed)': CONFIDENCE_LOW_VIX_REVERSED,
            'Normal-High VIX (Reversed)': CONFIDENCE_NORMAL_HIGH_VIX_REVERSED,
            'High VIX (Reversed)': CONFIDENCE_HIGH_VIX_REVERSED
        }
    
    def load_vix_data(self):
        """Load VIX data for testing"""
        
        print("📊 Loading VIX data for comprehensive timing tests...")
        
        try:
            # Load VIX data
            vix_df = pd.read_csv(VIX_DATA_FILES['VIX'], 
                               names=['date', 'open', 'high', 'low', 'close', 'volume'],
                               parse_dates=['date'])
            vix_df = vix_df.rename(columns={'close': 'vix'})
            
            # Load VIX9D data
            vix9d_df = pd.read_csv(VIX_DATA_FILES['VIX9D'], 
                                 names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                 parse_dates=['date'])
            vix9d_df = vix9d_df.rename(columns={'close': 'vix9d'})
            
            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'vix']], 
                              vix9d_df[['date', 'vix9d']], 
                              on='date', how='inner')
            
            # Filter date range
            vix_data = vix_data[
                (vix_data['date'] >= self.start_date) & 
                (vix_data['date'] <= self.end_date)
            ].copy()
            
            # Calculate VIX momentum
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']
            vix_data['vix_momentum_direction'] = np.where(
                vix_data['vix_momentum'] > 0, 'RISING', 'FALLING'
            )
            
            print(f"✅ Loaded {len(vix_data)} VIX records from {vix_data['date'].min()} to {vix_data['date'].max()}")
            return vix_data.set_index('date')
            
        except Exception as e:
            print(f"❌ Error loading VIX data: {e}")
            return None
    
    def generate_signals(self, vix_data):
        """Generate enhanced signals (same logic as main strategy)"""
        
        signals = []
        
        for date, row in vix_data.iterrows():
            vix = row['vix']
            vix9d = row['vix9d']
            vix_momentum = row['vix_momentum_direction']
            
            # SKIP Low-Normal VIX completely - no edge
            if VIX_LOW_NORMAL_LOW <= vix < VIX_LOW_NORMAL_HIGH:
                continue
            
            signal_direction = None
            signal_strength = DEFAULT_SIGNAL_STRENGTH
            reverse_signal = False
            condition = ""
            confidence_score = DEFAULT_CONFIDENCE_SCORE
            
            # Generate signals with enhanced confidence scoring
            if vix < VIX_LOW_THRESHOLD:
                # Low VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "Low VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_HIGH
                confidence_score = CONFIDENCE_SCORE_LOW_VIX_EXTREME if vix < VIX_EXTREME_LOW else CONFIDENCE_SCORE_LOW_VIX
                
            elif VIX_NORMAL_HIGH_LOW <= vix < VIX_NORMAL_HIGH_HIGH:
                # Normal-High VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "Normal-High VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_LOW
                confidence_score = CONFIDENCE_SCORE_NORMAL_HIGH_RISING if vix_momentum == 'RISING' else CONFIDENCE_SCORE_NORMAL_HIGH
                
            elif VIX_HIGH_LOW <= vix < VIX_HIGH_HIGH:
                # High VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "High VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_MEDIUM
                confidence_score = CONFIDENCE_SCORE_HIGH_VIX_BOOST if vix > VIX_HIGH_BOOST else CONFIDENCE_SCORE_HIGH_VIX
                
            elif VIX_VERY_HIGH_LOW <= vix < VIX_VERY_HIGH_HIGH:
                # Very High VIX - KEEP original signals
                if vix_momentum == 'RISING':
                    signal_direction = 'BULLISH'
                    signal_strength = SIGNAL_STRENGTH_VERY_HIGH
                    confidence_score = CONFIDENCE_SCORE_VERY_HIGH_RISING
                else:
                    signal_direction = 'BEARISH'
                    signal_strength = SIGNAL_STRENGTH_HIGH
                    confidence_score = CONFIDENCE_SCORE_VERY_HIGH_FALLING
                reverse_signal = False
                condition = "Very High VIX (Original)"
            
            # Add signal if generated
            if signal_direction:
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'signal_strength': signal_strength,
                    'confidence_score': confidence_score,
                    'vix': vix,
                    'vix9d': vix9d,
                    'vix_momentum': vix_momentum,
                    'reverse_signal': reverse_signal,
                    'condition': condition
                })
        
        signals_df = pd.DataFrame(signals)
        print(f"✅ Generated {len(signals_df)} signals for timing tests")
        
        return signals_df
    
    def calculate_position_size(self, vix, signal_strength, confidence_score, condition):
        """Calculate position size (same logic as main strategy)"""
        
        # Get base multiplier from confidence levels
        base_multiplier = self.confidence_levels.get(condition, {}).get('base_multiplier', DEFAULT_BASE_MULTIPLIER)
        
        # Calculate confidence-based multiplier
        confidence_multiplier = CONFIDENCE_MULTIPLIER_BASE + (confidence_score - CONFIDENCE_MULTIPLIER_OFFSET) * CONFIDENCE_MULTIPLIER_SCALE
        
        # Apply signal strength
        strength_multiplier = signal_strength
        
        # Combined multiplier
        total_multiplier = base_multiplier * confidence_multiplier * strength_multiplier
        
        # Calculate base position size
        risk_amount = self.capital * RISK_PER_TRADE
        base_position = risk_amount / RISK_PER_CONTRACT
        
        # Scale by multipliers
        position_size = base_position * total_multiplier
        
        # Apply confidence-based scaling for position size
        if confidence_score >= POSITION_SIZE_VERY_HIGH_CONFIDENCE:
            position_size = max(position_size, POSITION_SIZE_VERY_HIGH_MIN)
        elif confidence_score >= POSITION_SIZE_HIGH_CONFIDENCE:
            position_size = max(position_size, POSITION_SIZE_HIGH_MIN)
        elif confidence_score >= POSITION_SIZE_MEDIUM_CONFIDENCE:
            position_size = max(position_size, POSITION_SIZE_MEDIUM_MIN)
        else:
            position_size = max(position_size, POSITION_SIZE_LOW_MIN)
        
        # Ensure within bounds
        position_size = max(MIN_CONTRACTS, min(MAX_CONTRACTS, int(position_size)))
        
        return position_size
    
    def simulate_timing_trade(self, signal_date, signal_direction, vix, position_size, condition, 
                            confidence_score, reverse_signal, holding_days, timing_scenario):
        """Simulate trade with specific timing and holding period"""
        
        # Calculate entry and exit dates
        entry_date = signal_date + timedelta(days=1)  # Next trading day
        exit_date = entry_date + timedelta(days=holding_days)
        
        # Get timing details
        timing_info = self.timing_scenarios[timing_scenario]
        entry_time = timing_info['entry_time']
        exit_time = timing_info['exit_time']
        
        # Use confidence levels for realistic simulation
        condition_data = self.confidence_levels.get(condition, {
            'win_rate': DEFAULT_WIN_RATE,
            'avg_pnl': DEFAULT_AVG_PNL
        })
        
        win_prob = condition_data['win_rate']
        base_avg_pnl = condition_data['avg_pnl']
        
        # Adjust win probability based on confidence and holding period
        adjusted_win_prob = win_prob * (CONFIDENCE_ADJUSTMENT_BASE + CONFIDENCE_ADJUSTMENT_SCALE * confidence_score)
        
        # Adjust for holding period (longer holds may have different characteristics)
        holding_adjustment = 1.0 + (holding_days - 1) * 0.02  # Slight adjustment for longer holds
        adjusted_win_prob *= holding_adjustment
        adjusted_win_prob = min(adjusted_win_prob, 0.95)  # Cap at 95%
        
        # Simulate outcome
        is_winner = np.random.random() < adjusted_win_prob
        
        if is_winner:
            # Winner: Use positive multiple of base average, scaled by holding period
            base_pnl = base_avg_pnl * np.random.uniform(WIN_MULTIPLIER_LOW, WIN_MULTIPLIER_HIGH)
            base_pnl *= (1 + (holding_days - 1) * 0.1)  # Longer holds may have higher returns
        else:
            # Loser: Use negative multiple, but smaller magnitude
            base_pnl = -base_avg_pnl * np.random.uniform(LOSS_MULTIPLIER_LOW, LOSS_MULTIPLIER_HIGH)
        
        # Add timing-specific adjustments
        timing_multiplier = self.get_timing_multiplier(timing_scenario, holding_days)
        base_pnl *= timing_multiplier
        
        # Scale by position size
        trade_pnl = base_pnl * position_size
        
        # Simulate entry/exit prices for tracking
        entry_price = ENTRY_PRICE_BASE + (vix - ENTRY_PRICE_VIX_OFFSET) * ENTRY_PRICE_VIX_MULTIPLIER
        exit_price = entry_price + (base_pnl / EXIT_PRICE_DIVISOR)
        
        return {
            'signal_date': signal_date,
            'entry_date': entry_date,
            'exit_date': exit_date,
            'entry_time': entry_time,
            'exit_time': exit_time,
            'holding_days': holding_days,
            'timing_scenario': timing_scenario,
            'signal_direction': signal_direction,
            'condition': condition,
            'confidence_score': confidence_score,
            'reverse_signal': reverse_signal,
            'position_size': position_size,
            'vix': vix,
            'entry_price': max(entry_price, MIN_ENTRY_PRICE),
            'exit_price': max(exit_price, MIN_EXIT_PRICE),
            'trade_pnl': trade_pnl,
            'is_winner': is_winner,
            'timing_multiplier': timing_multiplier
        }
    
    def get_timing_multiplier(self, timing_scenario, holding_days):
        """Get performance multiplier based on timing scenario"""
        
        # Different timing scenarios may have different performance characteristics
        multipliers = {
            'open_to_close': {
                1: 1.00,  # Baseline for 1-day open-to-close
                2: 0.98,  # Slightly lower for 2-day
                3: 0.96,  # Continue declining
                4: 0.94,
                5: 0.92
            },
            'open_to_open': {
                1: 1.02,  # Slightly better for overnight holds
                2: 1.01,
                3: 0.99,
                4: 0.97,
                5: 0.95
            },
            'close_to_open': {
                1: 0.98,  # Overnight gap risk
                2: 0.96,
                3: 0.94,
                4: 0.92,
                5: 0.90
            },
            'close_to_close': {
                1: 0.99,  # Slightly lower than open-to-close
                2: 0.97,
                3: 0.95,
                4: 0.93,
                5: 0.91
            }
        }
        
        return multipliers.get(timing_scenario, {}).get(holding_days, 1.0)

    def run_single_test(self, signals_df, holding_days, timing_scenario):
        """Run a single test with specific holding period and timing"""

        trades = []
        capital = STARTING_CAPITAL

        for _, signal in signals_df.iterrows():
            position_size = self.calculate_position_size(
                signal['vix'],
                signal['signal_strength'],
                signal['confidence_score'],
                signal['condition']
            )

            trade = self.simulate_timing_trade(
                signal['date'],
                signal['signal_direction'],
                signal['vix'],
                position_size,
                signal['condition'],
                signal['confidence_score'],
                signal['reverse_signal'],
                holding_days,
                timing_scenario
            )

            trades.append(trade)
            capital += trade['trade_pnl']

        # Calculate performance metrics
        trades_df = pd.DataFrame(trades)

        total_pnl = trades_df['trade_pnl'].sum()
        total_return = (total_pnl / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER
        win_rate = (trades_df['trade_pnl'] > 0).mean() * PERCENTAGE_MULTIPLIER

        winning_trades = trades_df[trades_df['trade_pnl'] > 0]
        losing_trades = trades_df[trades_df['trade_pnl'] < 0]

        avg_win = winning_trades['trade_pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['trade_pnl'].mean() if len(losing_trades) > 0 else 0
        profit_factor = abs(winning_trades['trade_pnl'].sum() / losing_trades['trade_pnl'].sum()) if len(losing_trades) > 0 else float('inf')

        # Calculate max drawdown
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['running_max'] = trades_df['cumulative_pnl'].expanding().max()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['running_max']
        max_drawdown = abs(trades_df['drawdown'].min() / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER

        return {
            'holding_days': holding_days,
            'timing_scenario': timing_scenario,
            'timing_name': self.timing_scenarios[timing_scenario]['name'],
            'total_trades': len(trades_df),
            'win_rate': win_rate,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'final_capital': capital,
            'max_drawdown': max_drawdown,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_position_size': trades_df['position_size'].mean(),
            'trades_df': trades_df
        }

    def run_comprehensive_tests(self):
        """Run all combinations of holding periods and timing scenarios"""

        print("🚀 RUNNING COMPREHENSIVE TIMING TESTS")
        print("=" * SEPARATOR_LENGTH)
        print(f"Testing {len(self.holding_periods)} holding periods × {len(self.timing_scenarios)} timing scenarios")
        print(f"Total combinations: {len(self.holding_periods) * len(self.timing_scenarios)}")
        print("=" * SEPARATOR_LENGTH)

        # Load VIX data
        vix_data = self.load_vix_data()
        if vix_data is None:
            return None

        # Generate signals
        signals_df = self.generate_signals(vix_data)
        if len(signals_df) == 0:
            print("❌ No signals generated")
            return None

        # Run all test combinations
        test_count = 0
        total_tests = len(self.holding_periods) * len(self.timing_scenarios)

        for holding_days in self.holding_periods:
            for timing_scenario in self.timing_scenarios.keys():
                test_count += 1
                timing_name = self.timing_scenarios[timing_scenario]['name']

                print(f"📊 Running test {test_count}/{total_tests}: {holding_days}-day {timing_name}")

                result = self.run_single_test(signals_df, holding_days, timing_scenario)

                # Store result
                test_key = f"{holding_days}d_{timing_scenario}"
                self.test_results[test_key] = result

                print(f"   ✅ Return: {result['total_return']:.1f}%, Win Rate: {result['win_rate']:.1f}%, Drawdown: {result['max_drawdown']:.1f}%")

        print(f"\n✅ Completed all {total_tests} timing tests!")
        return self.test_results

    def analyze_results(self):
        """Analyze and compare all test results"""

        if not self.test_results:
            print("❌ No test results to analyze")
            return

        print("\n📊 COMPREHENSIVE TIMING TEST RESULTS")
        print("=" * SEPARATOR_LENGTH)

        # Create results summary table
        results_data = []
        for test_key, result in self.test_results.items():
            results_data.append({
                'Test': f"{result['holding_days']}d {result['timing_name']}",
                'Holding_Days': result['holding_days'],
                'Timing': result['timing_name'],
                'Total_Return': result['total_return'],
                'Win_Rate': result['win_rate'],
                'Max_Drawdown': result['max_drawdown'],
                'Profit_Factor': result['profit_factor'],
                'Total_Trades': result['total_trades']
            })

        results_df = pd.DataFrame(results_data)

        # Sort by total return
        results_df = results_df.sort_values('Total_Return', ascending=False)

        print("🏆 TOP PERFORMING COMBINATIONS:")
        print("-" * 80)
        print(f"{'Rank':<4} {'Test':<15} {'Return':<8} {'Win Rate':<9} {'Drawdown':<9} {'Profit Factor':<12}")
        print("-" * 80)

        for i, (_, row) in enumerate(results_df.head(10).iterrows(), 1):
            print(f"{i:<4} {row['Test']:<15} {row['Total_Return']:>6.1f}% {row['Win_Rate']:>7.1f}% {row['Max_Drawdown']:>7.1f}% {row['Profit_Factor']:>10.2f}")

        # Analyze by holding period
        print(f"\n📈 PERFORMANCE BY HOLDING PERIOD:")
        print("-" * 60)
        for holding_days in self.holding_periods:
            subset = results_df[results_df['Holding_Days'] == holding_days]
            avg_return = subset['Total_Return'].mean()
            best_timing = subset.loc[subset['Total_Return'].idxmax(), 'Timing']
            best_return = subset['Total_Return'].max()

            print(f"{holding_days}-day holds: Avg {avg_return:>6.1f}%, Best {best_return:>6.1f}% ({best_timing})")

        # Analyze by timing scenario
        print(f"\n⏰ PERFORMANCE BY TIMING SCENARIO:")
        print("-" * 60)
        for timing_scenario in self.timing_scenarios.keys():
            timing_name = self.timing_scenarios[timing_scenario]['name']
            subset = results_df[results_df['Timing'] == timing_name]
            avg_return = subset['Total_Return'].mean()
            best_holding = subset.loc[subset['Total_Return'].idxmax(), 'Holding_Days']
            best_return = subset['Total_Return'].max()

            print(f"{timing_name:<12}: Avg {avg_return:>6.1f}%, Best {best_return:>6.1f}% ({best_holding}-day)")

        return results_df

    def create_results_visualization(self, results_df):
        """Create comprehensive visualization of test results"""

        print("\n📊 Generating comprehensive timing test visualizations...")

        fig, axes = plt.subplots(2, 2, figsize=(CHART_FIGURE_WIDTH, CHART_FIGURE_HEIGHT))
        fig.suptitle('Comprehensive Timing Tests - Performance Analysis',
                    fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')

        # 1. Heatmap of returns by holding period and timing
        ax1 = axes[0, 0]
        pivot_returns = results_df.pivot(index='Holding_Days', columns='Timing', values='Total_Return')
        im = ax1.imshow(pivot_returns.values, cmap='RdYlGn', aspect='auto')
        ax1.set_xticks(range(len(pivot_returns.columns)))
        ax1.set_xticklabels(pivot_returns.columns, rotation=45, ha='right')
        ax1.set_yticks(range(len(pivot_returns.index)))
        ax1.set_yticklabels([f"{int(x)}-day" for x in pivot_returns.index])
        ax1.set_title('Total Return Heatmap (%)', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')

        # Add text annotations
        for i in range(len(pivot_returns.index)):
            for j in range(len(pivot_returns.columns)):
                text = ax1.text(j, i, f'{pivot_returns.iloc[i, j]:.1f}%',
                               ha="center", va="center", color="black", fontsize=8)

        # 2. Win Rate comparison
        ax2 = axes[0, 1]
        for timing in results_df['Timing'].unique():
            subset = results_df[results_df['Timing'] == timing]
            ax2.plot(subset['Holding_Days'], subset['Win_Rate'],
                    marker='o', linewidth=CHART_LINE_WIDTH, label=timing)
        ax2.set_title('Win Rate by Holding Period', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_xlabel('Holding Days', fontsize=CHART_XLABEL_FONTSIZE)
        ax2.set_ylabel('Win Rate (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)

        # 3. Max Drawdown comparison
        ax3 = axes[1, 0]
        for timing in results_df['Timing'].unique():
            subset = results_df[results_df['Timing'] == timing]
            ax3.plot(subset['Holding_Days'], subset['Max_Drawdown'],
                    marker='s', linewidth=CHART_LINE_WIDTH, label=timing)
        ax3.set_title('Max Drawdown by Holding Period', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_xlabel('Holding Days', fontsize=CHART_XLABEL_FONTSIZE)
        ax3.set_ylabel('Max Drawdown (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)

        # 4. Risk-Return scatter
        ax4 = axes[1, 1]
        colors = plt.cm.viridis(np.linspace(0, 1, len(results_df['Timing'].unique())))
        for i, timing in enumerate(results_df['Timing'].unique()):
            subset = results_df[results_df['Timing'] == timing]
            ax4.scatter(subset['Max_Drawdown'], subset['Total_Return'],
                       c=[colors[i]], s=CHART_SCATTER_SIZE_LARGE, alpha=CHART_SCATTER_ALPHA, label=timing)
        ax4.set_title('Risk-Return Profile', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax4.set_xlabel('Max Drawdown (%)', fontsize=CHART_XLABEL_FONTSIZE)
        ax4.set_ylabel('Total Return (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax4.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax4.grid(True, alpha=CHART_GRID_ALPHA)

        plt.tight_layout()

        # Save chart
        chart_filename = f'{REPORTS_DIR}/comprehensive_timing_tests.png'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 Comprehensive timing test results saved to: {chart_filename}")

        plt.show()

        return chart_filename

    def save_detailed_results(self, results_df):
        """Save detailed results to CSV"""

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save summary results
        summary_filename = f'{REPORTS_DIR}/timing_test_summary_{timestamp}.csv'
        results_df.to_csv(summary_filename, index=False)
        print(f"💾 Timing test summary saved to: {summary_filename}")

        # Save detailed trade data for best performing combination
        best_test = results_df.iloc[0]
        best_key = f"{best_test['Holding_Days']}d_{best_test['Timing'].lower().replace('->', '_to_').replace(' ', '_')}"

        if best_key in self.test_results:
            best_trades = self.test_results[best_key]['trades_df']
            trades_filename = f'{REPORTS_DIR}/best_timing_trades_{timestamp}.csv'
            best_trades.to_csv(trades_filename, index=False)
            print(f"💾 Best timing trades saved to: {trades_filename}")

        return summary_filename

def main():
    """Main execution function"""

    print("🔧 COMPREHENSIVE TIMING TESTS")
    print("Testing 1-5 day holding periods with 4 timing scenarios")
    print("=" * SEPARATOR_LENGTH)

    # Create test runner
    tester = ComprehensiveTimingTests()

    # Run comprehensive tests
    results = tester.run_comprehensive_tests()

    if results:
        # Analyze results
        results_df = tester.analyze_results()

        # Create visualizations
        tester.create_results_visualization(results_df)

        # Save detailed results
        tester.save_detailed_results(results_df)

        print(f"\n🎉 COMPREHENSIVE TIMING TESTS COMPLETED!")
        print(f"📊 Tested {len(results)} combinations")
        print(f"📁 Results saved to reports/ directory")

        # Show best combination
        best_result = results_df.iloc[0]
        print(f"\n🏆 BEST COMBINATION:")
        print(f"   📈 {best_result['Test']}: {best_result['Total_Return']:.1f}% return")
        print(f"   🎯 Win Rate: {best_result['Win_Rate']:.1f}%")
        print(f"   📉 Max Drawdown: {best_result['Max_Drawdown']:.1f}%")
        print(f"   💰 Profit Factor: {best_result['Profit_Factor']:.2f}")

    else:
        print("\n❌ Timing tests failed")

    return results

if __name__ == "__main__":
    results = main()
