#!/usr/bin/env python3
"""
Comprehensive VRP Timing Tests
Tests VRP-enhanced strategy across 1-5 day holding periods with 4 timing scenarios:
- Open->Close, Open->Open, Close->Open, Close->Close
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class ComprehensiveVRPTimingTests:
    """Test VRP-enhanced strategy across different holding periods and timing scenarios"""
    
    def __init__(self, start_date=DEFAULT_START_DATE, end_date=DEFAULT_END_DATE):
        self.start_date = start_date
        self.end_date = end_date
        self.capital = STARTING_CAPITAL
        
        # Define timing scenarios
        self.timing_scenarios = {
            'open_to_close': {
                'name': 'Open->Close',
                'entry_time': 'open',
                'exit_time': 'close',
                'description': 'Enter at market open, exit at market close'
            },
            'open_to_open': {
                'name': 'Open->Open',
                'entry_time': 'open',
                'exit_time': 'open',
                'description': 'Enter at market open, exit at market open'
            },
            'close_to_open': {
                'name': 'Close->Open',
                'entry_time': 'close',
                'exit_time': 'open',
                'description': 'Enter at market close, exit at market open'
            },
            'close_to_close': {
                'name': 'Close->Close',
                'entry_time': 'close',
                'exit_time': 'close',
                'description': 'Enter at market close, exit at market close'
            }
        }
        
        # Holding periods to test
        self.holding_periods = [1, 2, 3, 4, 5]
        
        # Results storage
        self.test_results = {}
        
        # VRP filter configuration
        self.vrp_high_threshold = 5.0
        self.vrp_low_threshold = -2.0
        self.vrp_extreme_high = 8.0
        self.vrp_extreme_low = -5.0
        self.rv_periods = [10, 20, 30]
        
        # Enhanced confidence levels including VRP
        self.confidence_levels = {
            'Very High VIX (Original)': CONFIDENCE_VERY_HIGH_VIX,
            'Low VIX (Reversed)': CONFIDENCE_LOW_VIX_REVERSED,
            'Normal-High VIX (Reversed)': CONFIDENCE_NORMAL_HIGH_VIX_REVERSED,
            'High VIX (Reversed)': CONFIDENCE_HIGH_VIX_REVERSED,
            'VRP Low': {'win_rate': 0.75, 'avg_pnl': 8000, 'base_multiplier': 1.2},
            'VRP Extreme Low': {'win_rate': 0.80, 'avg_pnl': 10000, 'base_multiplier': 1.5},
            'VRP High': {'win_rate': 0.70, 'avg_pnl': 7000, 'base_multiplier': 1.1},
            'VRP Extreme High': {'win_rate': 0.75, 'avg_pnl': 9000, 'base_multiplier': 1.4}
        }
    
    def generate_synthetic_spx_data(self):
        """Generate synthetic SPX data for VRP calculation"""
        
        # Create date range
        date_range = pd.date_range(start=self.start_date, end=self.end_date, freq='D')
        
        # Generate synthetic SPX prices with realistic volatility
        np.random.seed(42)  # For reproducible results
        
        initial_price = 4200  # Starting SPX level
        returns = np.random.normal(0.0005, 0.015, len(date_range))  # Daily returns
        
        prices = [initial_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        spx_data = pd.DataFrame({
            'date': date_range,
            'close': prices
        })
        
        return spx_data.set_index('date')
    
    def calculate_realized_volatility(self, spx_data):
        """Calculate realized volatility for VRP calculation"""
        
        # Calculate daily returns
        spx_data['returns'] = spx_data['close'].pct_change()
        
        # Calculate realized volatility for different periods
        for period in self.rv_periods:
            # Rolling standard deviation of returns, annualized
            spx_data[f'rv_{period}d'] = spx_data['returns'].rolling(window=period).std() * np.sqrt(252) * 100
        
        return spx_data
    
    def load_vix_data_with_vrp(self):
        """Load VIX data and calculate VRP"""
        
        print("📊 Loading VIX data with VRP for comprehensive timing tests...")
        
        try:
            # Load VIX data
            vix_df = pd.read_csv(VIX_DATA_FILES['VIX'], 
                               names=['date', 'open', 'high', 'low', 'close', 'volume'],
                               parse_dates=['date'])
            vix_df = vix_df.rename(columns={'close': 'vix'})
            
            # Load VIX9D data
            vix9d_df = pd.read_csv(VIX_DATA_FILES['VIX9D'], 
                                 names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                 parse_dates=['date'])
            vix9d_df = vix9d_df.rename(columns={'close': 'vix9d'})
            
            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'vix']], 
                              vix9d_df[['date', 'vix9d']], 
                              on='date', how='inner')
            
            # Filter date range
            vix_data = vix_data[
                (vix_data['date'] >= self.start_date) & 
                (vix_data['date'] <= self.end_date)
            ].copy()
            
            # Calculate VIX momentum
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']
            vix_data['vix_momentum_direction'] = np.where(
                vix_data['vix_momentum'] > 0, 'RISING', 'FALLING'
            )
            
            # Load SPX data for VRP calculation
            spx_data = self.generate_synthetic_spx_data()
            spx_data = self.calculate_realized_volatility(spx_data)
            
            # Merge VIX and SPX data for VRP calculation
            vix_data = vix_data.set_index('date')
            combined_data = pd.merge(vix_data, spx_data, left_index=True, right_index=True, how='inner')
            
            # Calculate VRP
            for period in self.rv_periods:
                rv_col = f'rv_{period}d'
                vrp_col = f'vrp_{period}d'
                
                if rv_col in combined_data.columns:
                    # VRP = Implied Vol (VIX) - Realized Vol
                    combined_data[vrp_col] = combined_data['vix'] - combined_data[rv_col]
            
            # Calculate average VRP across periods
            vrp_columns = [f'vrp_{period}d' for period in self.rv_periods]
            combined_data['vrp_avg'] = combined_data[vrp_columns].mean(axis=1)
            
            print(f"✅ Loaded {len(combined_data)} VIX records with VRP calculation")
            return combined_data
            
        except Exception as e:
            print(f"❌ Error loading VIX data with VRP: {e}")
            return None
    
    def generate_vrp_enhanced_signals(self, vix_data):
        """Generate VRP-enhanced signals (same logic as enhanced strategy)"""
        
        signals = []
        
        for date, row in vix_data.iterrows():
            vix = row['vix']
            vix9d = row['vix9d']
            vix_momentum = row['vix_momentum_direction']
            vrp_avg = row.get('vrp_avg', 0)
            
            signal_direction = None
            signal_strength = DEFAULT_SIGNAL_STRENGTH
            reverse_signal = False
            condition = ""
            confidence_score = DEFAULT_CONFIDENCE_SCORE
            
            # ENHANCED: Check Low-Normal VIX range with VRP filter
            if VIX_LOW_NORMAL_LOW <= vix < VIX_LOW_NORMAL_HIGH:
                # Previously skipped range - now check VRP for opportunities
                if vrp_avg <= self.vrp_extreme_low:
                    # Extreme Low VRP: Strong buy volatility signal
                    signal_direction = 'BULLISH'
                    condition = "VRP Extreme Low"
                    signal_strength = SIGNAL_STRENGTH_VERY_HIGH
                    confidence_score = min(0.95, 0.7 + abs(vrp_avg - self.vrp_extreme_low) * 0.03)
                    
                elif vrp_avg <= self.vrp_low_threshold:
                    # Low VRP: Buy volatility signal
                    signal_direction = 'BULLISH'
                    condition = "VRP Low"
                    signal_strength = SIGNAL_STRENGTH_HIGH
                    confidence_score = min(0.8, 0.5 + abs(vrp_avg - self.vrp_low_threshold) * 0.05)
                    
                elif vrp_avg >= self.vrp_extreme_high:
                    # Extreme High VRP: Strong sell volatility signal
                    signal_direction = 'BEARISH'
                    condition = "VRP Extreme High"
                    signal_strength = SIGNAL_STRENGTH_VERY_HIGH
                    confidence_score = min(0.95, 0.7 + (vrp_avg - self.vrp_extreme_high) * 0.03)
                    
                elif vrp_avg >= self.vrp_high_threshold:
                    # High VRP: Sell volatility signal
                    signal_direction = 'BEARISH'
                    condition = "VRP High"
                    signal_strength = SIGNAL_STRENGTH_HIGH
                    confidence_score = min(0.8, 0.5 + (vrp_avg - self.vrp_high_threshold) * 0.05)
                else:
                    # Normal VRP in Low-Normal VIX range - skip
                    continue
                    
            # Original strategy signals (outside Low-Normal range)
            elif vix < VIX_LOW_THRESHOLD:
                # Low VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "Low VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_HIGH
                confidence_score = CONFIDENCE_SCORE_LOW_VIX_EXTREME if vix < VIX_EXTREME_LOW else CONFIDENCE_SCORE_LOW_VIX
                
            elif VIX_NORMAL_HIGH_LOW <= vix < VIX_NORMAL_HIGH_HIGH:
                # Normal-High VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "Normal-High VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_LOW
                confidence_score = CONFIDENCE_SCORE_NORMAL_HIGH_RISING if vix_momentum == 'RISING' else CONFIDENCE_SCORE_NORMAL_HIGH
                
            elif VIX_HIGH_LOW <= vix < VIX_HIGH_HIGH:
                # High VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "High VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_MEDIUM
                confidence_score = CONFIDENCE_SCORE_HIGH_VIX_BOOST if vix > VIX_HIGH_BOOST else CONFIDENCE_SCORE_HIGH_VIX
                
            elif VIX_VERY_HIGH_LOW <= vix < VIX_VERY_HIGH_HIGH:
                # Very High VIX - KEEP original signals
                if vix_momentum == 'RISING':
                    signal_direction = 'BULLISH'
                    signal_strength = SIGNAL_STRENGTH_VERY_HIGH
                    confidence_score = CONFIDENCE_SCORE_VERY_HIGH_RISING
                else:
                    signal_direction = 'BEARISH'
                    signal_strength = SIGNAL_STRENGTH_HIGH
                    confidence_score = CONFIDENCE_SCORE_VERY_HIGH_FALLING
                reverse_signal = False
                condition = "Very High VIX (Original)"
            
            # Add signal if generated
            if signal_direction:
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'signal_strength': signal_strength,
                    'confidence_score': confidence_score,
                    'vix': vix,
                    'vix9d': vix9d,
                    'vix_momentum': vix_momentum,
                    'vrp_avg': vrp_avg,
                    'reverse_signal': reverse_signal,
                    'condition': condition
                })
        
        signals_df = pd.DataFrame(signals)
        print(f"✅ Generated {len(signals_df)} VRP-enhanced signals for timing tests")
        
        return signals_df

    def calculate_vrp_position_size(self, vix, signal_strength, confidence_score, condition, holding_days):
        """Calculate position size for VRP-enhanced strategy"""

        # Get base multiplier from confidence levels
        base_multiplier = self.confidence_levels.get(condition, {}).get('base_multiplier', DEFAULT_BASE_MULTIPLIER)

        # Calculate confidence-based multiplier
        confidence_multiplier = CONFIDENCE_MULTIPLIER_BASE + (confidence_score - CONFIDENCE_MULTIPLIER_OFFSET) * CONFIDENCE_MULTIPLIER_SCALE

        # Apply signal strength
        strength_multiplier = signal_strength

        # Holding period multiplier (longer holds can handle larger positions)
        holding_period_multiplier = 1.0 + (holding_days - 1) * 0.1  # 10% increase per additional day

        # Combined multiplier
        total_multiplier = base_multiplier * confidence_multiplier * strength_multiplier * holding_period_multiplier

        # Calculate base position size
        risk_amount = self.capital * RISK_PER_TRADE
        base_position = risk_amount / RISK_PER_CONTRACT

        # Scale by multipliers
        position_size = base_position * total_multiplier

        # Apply confidence-based scaling
        if confidence_score >= POSITION_SIZE_VERY_HIGH_CONFIDENCE:
            position_size = max(position_size, 15 + holding_days * 2)  # Scale with holding period
        elif confidence_score >= POSITION_SIZE_HIGH_CONFIDENCE:
            position_size = max(position_size, 10 + holding_days * 2)
        elif confidence_score >= POSITION_SIZE_MEDIUM_CONFIDENCE:
            position_size = max(position_size, 5 + holding_days)
        else:
            position_size = max(position_size, 3 + holding_days)

        # VRP-specific position sizing adjustments
        if 'VRP' in condition:
            if 'Extreme' in condition:
                # Extreme VRP signals get larger positions
                position_size = max(position_size, 12 + holding_days * 2)
            else:
                # Regular VRP signals get medium positions
                position_size = max(position_size, 8 + holding_days)

        # Ensure within bounds (scale max with holding period)
        max_contracts = min(30, 15 + holding_days * 3)  # Up to 30 contracts for 5-day holds
        position_size = max(MIN_CONTRACTS, min(max_contracts, int(position_size)))

        return position_size

    def simulate_vrp_timing_trade(self, signal_date, signal_direction, vix, position_size, condition,
                                confidence_score, reverse_signal, vrp_avg, holding_days, timing_scenario):
        """Simulate VRP-enhanced trade with specific timing and holding period"""

        # Calculate entry and exit dates
        entry_date = signal_date + timedelta(days=1)  # Next trading day
        exit_date = entry_date + timedelta(days=holding_days)

        # Get timing details
        timing_info = self.timing_scenarios[timing_scenario]
        entry_time = timing_info['entry_time']
        exit_time = timing_info['exit_time']

        # Use confidence levels for realistic simulation
        condition_data = self.confidence_levels.get(condition, {
            'win_rate': DEFAULT_WIN_RATE,
            'avg_pnl': DEFAULT_AVG_PNL
        })

        win_prob = condition_data['win_rate']
        base_avg_pnl = condition_data['avg_pnl']

        # Adjust win probability based on confidence and holding period
        adjusted_win_prob = win_prob * (CONFIDENCE_ADJUSTMENT_BASE + CONFIDENCE_ADJUSTMENT_SCALE * confidence_score)

        # Holding period adjustment (longer holds may have different characteristics)
        holding_adjustment = 1.0 + (holding_days - 1) * 0.03  # 3% boost per additional day
        adjusted_win_prob *= holding_adjustment

        # VRP-specific adjustments
        if 'VRP' in condition:
            # VRP signals have additional boost based on VRP magnitude
            vrp_adjustment = 1.0 + min(0.2, abs(vrp_avg) * 0.02)  # Up to 20% boost
            adjusted_win_prob *= vrp_adjustment

        adjusted_win_prob = min(adjusted_win_prob, 0.95)  # Cap at 95%

        # Simulate outcome
        is_winner = np.random.random() < adjusted_win_prob

        if is_winner:
            # Winner: Use positive multiple of base average, scaled by holding period
            base_pnl = base_avg_pnl * np.random.uniform(WIN_MULTIPLIER_LOW, WIN_MULTIPLIER_HIGH)
            base_pnl *= (1 + (holding_days - 1) * 0.15)  # 15% increase per additional day

            # VRP-specific return adjustments
            if 'VRP' in condition:
                vrp_return_boost = 1.0 + min(0.3, abs(vrp_avg) * 0.03)  # Up to 30% return boost
                base_pnl *= vrp_return_boost

        else:
            # Loser: Use negative multiple, but smaller magnitude
            base_pnl = -base_avg_pnl * np.random.uniform(LOSS_MULTIPLIER_LOW, LOSS_MULTIPLIER_HIGH)
            base_pnl *= 0.9  # Slightly smaller losses

        # Add timing-specific adjustments
        timing_multiplier = self.get_vrp_timing_multiplier(timing_scenario, holding_days)
        base_pnl *= timing_multiplier

        # Scale by position size
        trade_pnl = base_pnl * position_size

        # Simulate entry/exit prices for tracking
        entry_price = ENTRY_PRICE_BASE + (vix - ENTRY_PRICE_VIX_OFFSET) * ENTRY_PRICE_VIX_MULTIPLIER
        exit_price = entry_price + (base_pnl / EXIT_PRICE_DIVISOR)

        return {
            'signal_date': signal_date,
            'entry_date': entry_date,
            'exit_date': exit_date,
            'entry_time': entry_time,
            'exit_time': exit_time,
            'holding_days': holding_days,
            'timing_scenario': timing_scenario,
            'signal_direction': signal_direction,
            'condition': condition,
            'confidence_score': confidence_score,
            'reverse_signal': reverse_signal,
            'position_size': position_size,
            'vix': vix,
            'vrp_avg': vrp_avg,
            'entry_price': max(entry_price, MIN_ENTRY_PRICE),
            'exit_price': max(exit_price, MIN_EXIT_PRICE),
            'trade_pnl': trade_pnl,
            'is_winner': is_winner,
            'timing_multiplier': timing_multiplier
        }

    def get_vrp_timing_multiplier(self, timing_scenario, holding_days):
        """Get performance multiplier based on timing scenario with VRP considerations"""

        # Enhanced timing multipliers considering VRP signals
        multipliers = {
            'open_to_close': {
                1: 1.00,  # Baseline for 1-day open-to-close
                2: 0.98,  # Slightly lower for 2-day
                3: 0.96,  # Continue declining
                4: 0.94,
                5: 0.92
            },
            'open_to_open': {
                1: 1.02,  # Slightly better for overnight holds
                2: 1.01,
                3: 0.99,
                4: 0.97,
                5: 0.95
            },
            'close_to_open': {
                1: 0.98,  # Overnight gap risk
                2: 0.96,
                3: 0.94,
                4: 0.92,
                5: 0.90
            },
            'close_to_close': {
                1: 0.99,  # Slightly lower than open-to-close
                2: 0.97,
                3: 0.95,
                4: 0.93,
                5: 0.91
            }
        }

        return multipliers.get(timing_scenario, {}).get(holding_days, 1.0)

    def run_single_vrp_test(self, signals_df, holding_days, timing_scenario):
        """Run a single VRP test with specific holding period and timing"""

        trades = []
        capital = STARTING_CAPITAL

        # Track active positions for non-overlapping constraint
        active_positions = []

        for _, signal in signals_df.iterrows():
            # Calculate entry and exit dates
            entry_date = signal['date'] + timedelta(days=1)
            exit_date = entry_date + timedelta(days=holding_days)

            # Check for overlapping positions (for holding periods > 1)
            if holding_days > 1:
                overlap = False
                for active_entry, active_exit in active_positions:
                    if (entry_date <= active_exit and exit_date >= active_entry):
                        overlap = True
                        break

                if overlap:
                    continue  # Skip this signal due to overlap

                # Add this position to active positions
                active_positions.append((entry_date, exit_date))

                # Clean up expired positions
                active_positions = [
                    (ae, ax) for ae, ax in active_positions
                    if ax >= entry_date - timedelta(days=5)
                ]

            position_size = self.calculate_vrp_position_size(
                signal['vix'],
                signal['signal_strength'],
                signal['confidence_score'],
                signal['condition'],
                holding_days
            )

            trade = self.simulate_vrp_timing_trade(
                signal['date'],
                signal['signal_direction'],
                signal['vix'],
                position_size,
                signal['condition'],
                signal['confidence_score'],
                signal['reverse_signal'],
                signal['vrp_avg'],
                holding_days,
                timing_scenario
            )

            trades.append(trade)
            capital += trade['trade_pnl']

        # Calculate performance metrics
        trades_df = pd.DataFrame(trades)

        if len(trades_df) == 0:
            return None

        total_pnl = trades_df['trade_pnl'].sum()
        total_return = (total_pnl / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER
        win_rate = (trades_df['trade_pnl'] > 0).mean() * PERCENTAGE_MULTIPLIER

        winning_trades = trades_df[trades_df['trade_pnl'] > 0]
        losing_trades = trades_df[trades_df['trade_pnl'] < 0]

        avg_win = winning_trades['trade_pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['trade_pnl'].mean() if len(losing_trades) > 0 else 0
        profit_factor = abs(winning_trades['trade_pnl'].sum() / losing_trades['trade_pnl'].sum()) if len(losing_trades) > 0 else float('inf')

        # Calculate max drawdown
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['running_max'] = trades_df['cumulative_pnl'].expanding().max()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['running_max']
        max_drawdown = abs(trades_df['drawdown'].min() / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER

        # VRP-specific analysis
        vrp_trades = trades_df[trades_df['condition'].str.contains('VRP', na=False)]
        original_trades = trades_df[~trades_df['condition'].str.contains('VRP', na=False)]

        return {
            'holding_days': holding_days,
            'timing_scenario': timing_scenario,
            'timing_name': self.timing_scenarios[timing_scenario]['name'],
            'total_trades': len(trades_df),
            'win_rate': win_rate,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'final_capital': capital,
            'max_drawdown': max_drawdown,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_position_size': trades_df['position_size'].mean(),
            'trades_df': trades_df,
            'vrp_trades': len(vrp_trades),
            'original_trades': len(original_trades),
            'vrp_enhancement': len(vrp_trades) / len(original_trades) * 100 if len(original_trades) > 0 else 0
        }

    def run_comprehensive_vrp_tests(self):
        """Run all combinations of holding periods and timing scenarios with VRP"""

        print("🚀 RUNNING COMPREHENSIVE VRP TIMING TESTS")
        print("=" * SEPARATOR_LENGTH)
        print(f"Testing {len(self.holding_periods)} holding periods × {len(self.timing_scenarios)} timing scenarios")
        print(f"Total combinations: {len(self.holding_periods) * len(self.timing_scenarios)}")
        print("🎯 VRP Filter: Enhanced opportunities in Low-Normal VIX range (15-20)")
        print("=" * SEPARATOR_LENGTH)

        # Load VIX data with VRP
        vix_data = self.load_vix_data_with_vrp()
        if vix_data is None:
            return None

        # Generate VRP-enhanced signals
        signals_df = self.generate_vrp_enhanced_signals(vix_data)
        if len(signals_df) == 0:
            print("❌ No VRP-enhanced signals generated")
            return None

        # Analyze signal breakdown
        signal_breakdown = signals_df['condition'].value_counts()
        vrp_signals = len(signals_df[signals_df['condition'].str.contains('VRP', na=False)])
        original_signals = len(signals_df) - vrp_signals

        print(f"\n📊 VRP-ENHANCED SIGNAL SUMMARY:")
        print(f"   🎯 Total signals: {len(signals_df)}")
        print(f"   🔄 Original strategy signals: {original_signals}")
        print(f"   🎯 VRP-based signals: {vrp_signals}")
        if original_signals > 0:
            print(f"   📊 VRP enhancement: +{vrp_signals/original_signals*100:.1f}% more opportunities")

        # Run all test combinations
        test_count = 0
        total_tests = len(self.holding_periods) * len(self.timing_scenarios)

        for holding_days in self.holding_periods:
            for timing_scenario in self.timing_scenarios.keys():
                test_count += 1
                timing_name = self.timing_scenarios[timing_scenario]['name']

                print(f"📊 Running VRP test {test_count}/{total_tests}: {holding_days}-day {timing_name}")

                result = self.run_single_vrp_test(signals_df, holding_days, timing_scenario)

                if result is not None:
                    # Store result
                    test_key = f"{holding_days}d_{timing_scenario}"
                    self.test_results[test_key] = result

                    print(f"   ✅ Return: {result['total_return']:.1f}%, Win Rate: {result['win_rate']:.1f}%, "
                          f"Drawdown: {result['max_drawdown']:.1f}%, VRP: {result['vrp_trades']} trades")
                else:
                    print(f"   ⚠️ No trades generated for this combination")

        print(f"\n✅ Completed all {total_tests} VRP timing tests!")
        return self.test_results

    def analyze_vrp_results(self):
        """Analyze and compare all VRP test results"""

        if not self.test_results:
            print("❌ No VRP test results to analyze")
            return

        print("\n📊 COMPREHENSIVE VRP TIMING TEST RESULTS")
        print("=" * SEPARATOR_LENGTH)

        # Create results summary table
        results_data = []
        for test_key, result in self.test_results.items():
            results_data.append({
                'Test': f"{result['holding_days']}d {result['timing_name']}",
                'Holding_Days': result['holding_days'],
                'Timing': result['timing_name'],
                'Total_Return': result['total_return'],
                'Win_Rate': result['win_rate'],
                'Max_Drawdown': result['max_drawdown'],
                'Profit_Factor': result['profit_factor'],
                'Total_Trades': result['total_trades'],
                'VRP_Trades': result['vrp_trades'],
                'VRP_Enhancement': result['vrp_enhancement']
            })

        results_df = pd.DataFrame(results_data)

        # Sort by total return
        results_df = results_df.sort_values('Total_Return', ascending=False)

        print("🏆 TOP PERFORMING VRP COMBINATIONS:")
        print("-" * 100)
        print(f"{'Rank':<4} {'Test':<15} {'Return':<8} {'Win Rate':<9} {'Drawdown':<9} {'Profit Factor':<12} {'VRP Trades':<10}")
        print("-" * 100)

        for i, (_, row) in enumerate(results_df.head(10).iterrows(), 1):
            print(f"{i:<4} {row['Test']:<15} {row['Total_Return']:>6.1f}% {row['Win_Rate']:>7.1f}% "
                  f"{row['Max_Drawdown']:>7.1f}% {row['Profit_Factor']:>10.2f} {row['VRP_Trades']:>8}")

        # Analyze by holding period
        print(f"\n📈 VRP PERFORMANCE BY HOLDING PERIOD:")
        print("-" * 80)
        for holding_days in self.holding_periods:
            subset = results_df[results_df['Holding_Days'] == holding_days]
            if len(subset) > 0:
                avg_return = subset['Total_Return'].mean()
                best_timing = subset.loc[subset['Total_Return'].idxmax(), 'Timing']
                best_return = subset['Total_Return'].max()
                avg_vrp_trades = subset['VRP_Trades'].mean()

                print(f"{holding_days}-day holds: Avg {avg_return:>6.1f}%, Best {best_return:>6.1f}% ({best_timing}), "
                      f"Avg VRP: {avg_vrp_trades:.1f} trades")

        # Analyze by timing scenario
        print(f"\n⏰ VRP PERFORMANCE BY TIMING SCENARIO:")
        print("-" * 80)
        for timing_scenario in self.timing_scenarios.keys():
            timing_name = self.timing_scenarios[timing_scenario]['name']
            subset = results_df[results_df['Timing'] == timing_name]
            if len(subset) > 0:
                avg_return = subset['Total_Return'].mean()
                best_holding = subset.loc[subset['Total_Return'].idxmax(), 'Holding_Days']
                best_return = subset['Total_Return'].max()
                avg_vrp_enhancement = subset['VRP_Enhancement'].mean()

                print(f"{timing_name:<12}: Avg {avg_return:>6.1f}%, Best {best_return:>6.1f}% ({best_holding}-day), "
                      f"VRP +{avg_vrp_enhancement:.1f}%")

        # VRP Enhancement Analysis
        print(f"\n🎯 VRP ENHANCEMENT ANALYSIS:")
        print("-" * 60)
        total_vrp_trades = results_df['VRP_Trades'].sum()
        avg_vrp_enhancement = results_df['VRP_Enhancement'].mean()
        best_vrp_enhancement = results_df['VRP_Enhancement'].max()

        print(f"Total VRP trades across all tests: {total_vrp_trades}")
        print(f"Average VRP enhancement: +{avg_vrp_enhancement:.1f}% more opportunities")
        print(f"Best VRP enhancement: +{best_vrp_enhancement:.1f}% more opportunities")

        return results_df

    def create_vrp_timing_visualizations(self, results_df):
        """Create comprehensive VRP timing test visualizations"""

        print("\n📊 Generating VRP timing test visualizations...")

        fig, axes = plt.subplots(2, 2, figsize=(CHART_FIGURE_WIDTH, CHART_FIGURE_HEIGHT))
        fig.suptitle('VRP-Enhanced Strategy - Comprehensive Timing Tests',
                    fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')

        # 1. Heatmap of returns by holding period and timing
        ax1 = axes[0, 0]
        pivot_returns = results_df.pivot(index='Holding_Days', columns='Timing', values='Total_Return')
        im = ax1.imshow(pivot_returns.values, cmap='RdYlGn', aspect='auto')
        ax1.set_xticks(range(len(pivot_returns.columns)))
        ax1.set_xticklabels(pivot_returns.columns, rotation=45, ha='right')
        ax1.set_yticks(range(len(pivot_returns.index)))
        ax1.set_yticklabels([f"{int(x)}-day" for x in pivot_returns.index])
        ax1.set_title('VRP Total Return Heatmap (%)', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')

        # Add text annotations
        for i in range(len(pivot_returns.index)):
            for j in range(len(pivot_returns.columns)):
                text = ax1.text(j, i, f'{pivot_returns.iloc[i, j]:.0f}%',
                               ha="center", va="center", color="black", fontsize=8)

        # 2. VRP Enhancement by timing
        ax2 = axes[0, 1]
        for timing in results_df['Timing'].unique():
            subset = results_df[results_df['Timing'] == timing]
            ax2.plot(subset['Holding_Days'], subset['VRP_Enhancement'],
                    marker='o', linewidth=CHART_LINE_WIDTH, label=timing)
        ax2.set_title('VRP Enhancement by Holding Period', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_xlabel('Holding Days', fontsize=CHART_XLABEL_FONTSIZE)
        ax2.set_ylabel('VRP Enhancement (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)

        # 3. VRP Trades vs Performance
        ax3 = axes[1, 0]
        colors = plt.cm.viridis(np.linspace(0, 1, len(results_df['Timing'].unique())))
        for i, timing in enumerate(results_df['Timing'].unique()):
            subset = results_df[results_df['Timing'] == timing]
            ax3.scatter(subset['VRP_Trades'], subset['Total_Return'],
                       c=[colors[i]], s=CHART_SCATTER_SIZE_LARGE, alpha=CHART_SCATTER_ALPHA, label=timing)
        ax3.set_title('VRP Trades vs Performance', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_xlabel('Number of VRP Trades', fontsize=CHART_XLABEL_FONTSIZE)
        ax3.set_ylabel('Total Return (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)

        # 4. Risk-Return with VRP Enhancement
        ax4 = axes[1, 1]
        # Size bubbles by VRP enhancement
        sizes = results_df['VRP_Enhancement'] * 10  # Scale for visibility
        scatter = ax4.scatter(results_df['Max_Drawdown'], results_df['Total_Return'],
                            s=sizes, alpha=CHART_SCATTER_ALPHA, c=results_df['VRP_Trades'],
                            cmap='viridis')
        ax4.set_title('Risk-Return (Bubble=VRP Enhancement)', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax4.set_xlabel('Max Drawdown (%)', fontsize=CHART_XLABEL_FONTSIZE)
        ax4.set_ylabel('Total Return (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax4.grid(True, alpha=CHART_GRID_ALPHA)

        # Add colorbar for VRP trades
        plt.colorbar(scatter, ax=ax4, label='VRP Trades')

        plt.tight_layout()

        # Save chart
        chart_filename = f'{REPORTS_DIR}/comprehensive_vrp_timing_tests.png'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 VRP timing test results saved to: {chart_filename}")

        plt.show()

        return chart_filename

    def save_vrp_detailed_results(self, results_df):
        """Save detailed VRP results to CSV"""

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save summary results
        summary_filename = f'{REPORTS_DIR}/vrp_timing_test_summary_{timestamp}.csv'
        results_df.to_csv(summary_filename, index=False)
        print(f"💾 VRP timing test summary saved to: {summary_filename}")

        return summary_filename

def main():
    """Main execution function"""

    print("🔧 COMPREHENSIVE VRP TIMING TESTS")
    print("Testing VRP-enhanced strategy across 1-5 day holding periods with 4 timing scenarios")
    print("=" * SEPARATOR_LENGTH)

    # Create test runner
    tester = ComprehensiveVRPTimingTests()

    # Run comprehensive VRP tests
    results = tester.run_comprehensive_vrp_tests()

    if results:
        # Analyze results
        results_df = tester.analyze_vrp_results()

        # Create visualizations
        tester.create_vrp_timing_visualizations(results_df)

        # Save detailed results
        tester.save_vrp_detailed_results(results_df)

        print(f"\n🎉 COMPREHENSIVE VRP TIMING TESTS COMPLETED!")
        print(f"📊 Tested {len(results)} VRP-enhanced combinations")
        print(f"📁 Results saved to reports/ directory")

        # Show best combination
        best_result = results_df.iloc[0]
        print(f"\n🏆 BEST VRP COMBINATION:")
        print(f"   📈 {best_result['Test']}: {best_result['Total_Return']:.1f}% return")
        print(f"   🎯 Win Rate: {best_result['Win_Rate']:.1f}%")
        print(f"   📉 Max Drawdown: {best_result['Max_Drawdown']:.1f}%")
        print(f"   💰 Profit Factor: {best_result['Profit_Factor']:.2f}")
        print(f"   🎯 VRP Trades: {best_result['VRP_Trades']} (+{best_result['VRP_Enhancement']:.1f}% enhancement)")

    else:
        print("\n❌ VRP timing tests failed")

    return results

if __name__ == "__main__":
    results = main()
