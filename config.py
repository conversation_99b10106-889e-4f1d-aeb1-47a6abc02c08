"""
Configuration file for Pure VIX Options Trading Strategy
Environment variables and system configuration
"""

import os
from pathlib import Path

# ============================================================================
# ENVIRONMENT CONFIGURATION
# ============================================================================

# Base directories
PROJECT_ROOT = Path(__file__).parent
DATA_ROOT = PROJECT_ROOT.parent / 'optionhistory'
SYSTEMS_ROOT = Path('/Users/<USER>/Downloads/systems/strategy_package/data/securities')

# Environment variables with defaults
ENVIRONMENT = os.getenv('TRADING_ENV', 'development')  # development, testing, production
DEBUG_MODE = os.getenv('DEBUG_MODE', 'False').lower() == 'true'
VERBOSE_LOGGING = os.getenv('VERBOSE_LOGGING', 'False').lower() == 'true'

# ============================================================================
# DATA SOURCE CONFIGURATION
# ============================================================================

# Options data configuration
OPTIONS_DATA_CONFIG = {
    'base_directory': str(DATA_ROOT),
    'file_pattern': 'spx_complete_{year}_{quarter}.csv',
    'required_columns': [
        'date', 'strike', 'expiration', 'option_type', 
        'Last Trade Price', 'bid', 'ask', 'volume', 'open_interest'
    ],
    'date_format': '%Y-%m-%d',
    'min_data_quality': 0.8
}

# E-mini futures configuration
EMINI_CONFIG = {
    'data_file': str(SYSTEMS_ROOT / 'ES_full_5min_continuous_ratio_adjusted.txt'),
    'columns': ['date', 'time', 'open', 'high', 'low', 'close', 'volume'],
    'overnight_start_time': '17:00',
    'overnight_end_time': '09:30',
    'timezone': 'US/Eastern'
}

# VIX data configuration
VIX_CONFIG = {
    'data_file': str(SYSTEMS_ROOT / 'VIX.txt'),
    'fallback_enabled': True,
    'synthetic_data_seed': 42,
    'base_level': 19.0,
    'volatility': 1.5,
    'mean_reversion_speed': 0.1
}

# ============================================================================
# SYSTEM PERFORMANCE CONFIGURATION
# ============================================================================

# Memory management
MEMORY_CONFIG = {
    'max_memory_usage_gb': 8,
    'chunk_size': 100000,
    'enable_garbage_collection': True,
    'gc_frequency': 1000
}

# Processing configuration
PROCESSING_CONFIG = {
    'max_workers': os.cpu_count(),
    'enable_multiprocessing': True,
    'batch_size': 1000,
    'progress_update_frequency': 50
}

# Caching configuration
CACHE_CONFIG = {
    'enable_caching': True,
    'cache_directory': str(PROJECT_ROOT / 'cache'),
    'max_cache_size_gb': 2,
    'cache_expiry_days': 7
}

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

# Logging levels
LOG_LEVELS = {
    'development': 'DEBUG',
    'testing': 'INFO', 
    'production': 'WARNING'
}

# Logging configuration
LOGGING_CONFIG = {
    'level': LOG_LEVELS.get(ENVIRONMENT, 'INFO'),
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_logging': True,
    'log_directory': str(PROJECT_ROOT / 'logs'),
    'max_log_size_mb': 100,
    'backup_count': 5,
    'console_logging': True
}

# ============================================================================
# TRADING ENVIRONMENT CONFIGURATION
# ============================================================================

# Market hours configuration
MARKET_HOURS = {
    'regular_open': '09:30',
    'regular_close': '16:00',
    'extended_open': '04:00',
    'extended_close': '20:00',
    'timezone': 'US/Eastern'
}

# Trading calendar configuration
TRADING_CALENDAR = {
    'exchange': 'NYSE',
    'holidays_file': None,  # Use default exchange holidays
    'custom_holidays': [],
    'half_days': []
}

# ============================================================================
# RISK MANAGEMENT CONFIGURATION
# ============================================================================

# Portfolio risk limits
PORTFOLIO_RISK = {
    'max_portfolio_risk': 0.20,        # Maximum 20% portfolio risk
    'max_single_position_risk': 0.05,  # Maximum 5% per position
    'max_sector_concentration': 0.30,  # Maximum 30% in single sector
    'max_correlation_exposure': 0.50   # Maximum 50% in correlated positions
}

# Position sizing configuration
POSITION_SIZING = {
    'method': 'fixed_fractional',      # fixed_fractional, kelly, volatility_adjusted
    'base_fraction': 0.02,             # 2% of capital per trade
    'max_positions': 10,               # Maximum concurrent positions
    'min_position_value': 100,         # Minimum position value
    'max_position_value': 10000        # Maximum position value
}

# ============================================================================
# BACKTESTING CONFIGURATION
# ============================================================================

# Backtest parameters
BACKTEST_CONFIG = {
    'start_date': '2020-01-03',
    'end_date': '2025-07-11',
    'initial_capital': 100000,
    'commission_per_contract': 1.0,
    'slippage_bps': 5,                 # 5 basis points slippage
    'interest_rate': 0.02              # 2% risk-free rate
}

# Data validation
DATA_VALIDATION = {
    'max_price_change_pct': 0.20,      # Maximum 20% price change
    'min_volume': 10,                  # Minimum volume filter
    'min_open_interest': 50,           # Minimum open interest
    'max_bid_ask_spread_pct': 0.50,    # Maximum 50% bid-ask spread
    'outlier_detection': True,
    'outlier_threshold': 3.0           # Standard deviations
}

# ============================================================================
# REPORTING CONFIGURATION
# ============================================================================

# Report generation
REPORT_CONFIG = {
    'auto_generate': True,
    'formats': ['pdf', 'html', 'csv'],
    'include_charts': True,
    'chart_resolution': 300,           # DPI
    'chart_style': 'seaborn-v0_8',
    'color_scheme': 'professional'
}

# Performance metrics
METRICS_CONFIG = {
    'benchmark': 'SPY',
    'risk_free_rate': 0.02,
    'confidence_level': 0.95,
    'var_method': 'historical',
    'rolling_window_days': 252
}

# ============================================================================
# NOTIFICATION CONFIGURATION
# ============================================================================

# Email notifications (if enabled)
EMAIL_CONFIG = {
    'enabled': False,
    'smtp_server': os.getenv('SMTP_SERVER', ''),
    'smtp_port': int(os.getenv('SMTP_PORT', '587')),
    'username': os.getenv('EMAIL_USERNAME', ''),
    'password': os.getenv('EMAIL_PASSWORD', ''),
    'from_address': os.getenv('FROM_EMAIL', ''),
    'to_addresses': os.getenv('TO_EMAILS', '').split(',') if os.getenv('TO_EMAILS') else []
}

# Slack notifications (if enabled)
SLACK_CONFIG = {
    'enabled': False,
    'webhook_url': os.getenv('SLACK_WEBHOOK_URL', ''),
    'channel': os.getenv('SLACK_CHANNEL', '#trading'),
    'username': 'Pure VIX Strategy Bot'
}

# ============================================================================
# DATABASE CONFIGURATION (if needed)
# ============================================================================

# Database connection (optional)
DATABASE_CONFIG = {
    'enabled': False,
    'type': os.getenv('DB_TYPE', 'sqlite'),
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', '5432')),
    'database': os.getenv('DB_NAME', 'trading_db'),
    'username': os.getenv('DB_USERNAME', ''),
    'password': os.getenv('DB_PASSWORD', ''),
    'connection_pool_size': 5
}

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

# API security
API_CONFIG = {
    'rate_limiting': True,
    'max_requests_per_minute': 60,
    'api_key_required': False,
    'encryption_enabled': False
}

# Data security
SECURITY_CONFIG = {
    'encrypt_sensitive_data': False,
    'secure_file_permissions': True,
    'audit_logging': True,
    'data_retention_days': 365
}

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Development tools
DEV_CONFIG = {
    'enable_profiling': DEBUG_MODE,
    'enable_memory_tracking': DEBUG_MODE,
    'enable_performance_monitoring': True,
    'save_intermediate_results': DEBUG_MODE,
    'enable_unit_tests': True
}

# Testing configuration
TEST_CONFIG = {
    'test_data_directory': str(PROJECT_ROOT / 'test_data'),
    'mock_data_enabled': ENVIRONMENT == 'testing',
    'test_coverage_threshold': 0.80,
    'integration_tests_enabled': True
}

# ============================================================================
# CONFIGURATION VALIDATION
# ============================================================================

def validate_config():
    """Validate configuration settings"""
    
    errors = []
    
    # Validate required directories exist
    required_dirs = [
        LOGGING_CONFIG['log_directory'],
        CACHE_CONFIG['cache_directory'] if CACHE_CONFIG['enable_caching'] else None
    ]
    
    for dir_path in required_dirs:
        if dir_path and not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create directory {dir_path}: {e}")
    
    # Validate data files exist (if not using fallbacks)
    if not VIX_CONFIG['fallback_enabled'] and not os.path.exists(VIX_CONFIG['data_file']):
        errors.append(f"VIX data file not found: {VIX_CONFIG['data_file']}")
    
    # Validate numeric ranges
    if not 0 < PORTFOLIO_RISK['max_portfolio_risk'] <= 1:
        errors.append("Portfolio risk must be between 0 and 1")
    
    if not 0 < POSITION_SIZING['base_fraction'] <= 1:
        errors.append("Position sizing fraction must be between 0 and 1")
    
    # Validate environment
    if ENVIRONMENT not in ['development', 'testing', 'production']:
        errors.append(f"Invalid environment: {ENVIRONMENT}")
    
    if errors:
        raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    print("✅ Configuration validated successfully")

# ============================================================================
# CONFIGURATION SUMMARY
# ============================================================================

def print_config_summary():
    """Print configuration summary"""
    
    print(f"🔧 Configuration Summary")
    print(f"Environment: {ENVIRONMENT}")
    print(f"Debug Mode: {DEBUG_MODE}")
    print(f"Verbose Logging: {VERBOSE_LOGGING}")
    print(f"Data Root: {DATA_ROOT}")
    print(f"Cache Enabled: {CACHE_CONFIG['enable_caching']}")
    print(f"Multiprocessing: {PROCESSING_CONFIG['enable_multiprocessing']}")
    print(f"Max Workers: {PROCESSING_CONFIG['max_workers']}")

# Run validation when module is imported
if __name__ == "__main__":
    validate_config()
    print_config_summary()
else:
    # Validate configuration on import
    try:
        validate_config()
    except ValueError as e:
        print(f"⚠️ Configuration warning: {e}")
