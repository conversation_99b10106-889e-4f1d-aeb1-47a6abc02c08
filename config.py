"""
Configuration file for Real Options Trading Strategy
Environment variables and system configuration
"""

import os
from pathlib import Path

# ============================================================================
# ENVIRONMENT CONFIGURATION
# ============================================================================

# Base directories
PROJECT_ROOT = Path(__file__).parent
DATA_ROOT = PROJECT_ROOT.parent / 'optionhistory'
SECURITIES_DATA_DIR = Path('/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities')

# Environment variables with defaults
ENVIRONMENT = os.getenv('TRADING_ENV', 'development')
DEBUG_MODE = os.getenv('DEBUG_MODE', 'False').lower() == 'true'

# ============================================================================
# DATA SOURCE CONFIGURATION
# ============================================================================

# Options data configuration
OPTIONS_DATA_CONFIG = {
    'base_directory': str(DATA_ROOT),
    'file_pattern': 'spx_complete_{year}_{quarter}.csv',
    'required_columns': [
        'date', 'strike', 'expiration', 'option_type',
        'Last Trade Price', 'bid', 'ask', 'volume', 'open_interest'
    ],
    'date_format': '%Y-%m-%d'
}

# VIX data configuration (real data only)
VIX_CONFIG = {
    'data_file': str(SECURITIES_DATA_DIR / 'VIX_full_1day.txt'),
    'fallback_enabled': False  # No synthetic data
}

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_logging': True,
    'log_directory': str(PROJECT_ROOT / 'logs'),
    'console_logging': True
}


# ============================================================================
# VALIDATION
# ============================================================================

def validate_config():
    """Validate configuration settings"""

    # Validate required directories exist
    log_dir = LOGGING_CONFIG['log_directory']
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
        except Exception as e:
            print(f"Warning: Cannot create log directory {log_dir}: {e}")

    # Validate VIX data file exists
    if not VIX_CONFIG['fallback_enabled'] and not os.path.exists(VIX_CONFIG['data_file']):
        print(f"Warning: VIX data file not found: {VIX_CONFIG['data_file']}")

    print("✅ Configuration validated successfully")

# Run validation when module is imported
if __name__ == "__main__":
    validate_config()
else:
    try:
        validate_config()
    except Exception as e:
        print(f"⚠️ Configuration warning: {e}")




