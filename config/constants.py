"""
Configuration constants for JPM Options Trading Strategies
"""
import os
from datetime import datetime

# Data Paths
VIX_DATA_PATH = "/Users/<USER>/Downloads/systems/strategy_package/data/securities/correl/VIX_daily.csv"
SPX_OPTIONS_BASE_PATH = "/Users/<USER>/Downloads/optionhistory"
UTY_DATA_PATH = "/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/UTY.csv"
ES_FUTURES_PATH = "/Users/<USER>/Downloads/systems/strategy_package/data/securities/ES_full_5min_continuous_ratio_adjusted.txt"

# Date Configuration
START_DATE = "2023-05-01"
END_DATE = datetime.now().strftime("%Y-%m-%d")  # Auto-update to today

# Trading Parameters
INITIAL_CAPITAL = 100000
COMMISSION_PER_CONTRACT = 1.0
COMMISSION_PER_LEG = 2  # Open and close

# VIX Strategy Parameters
VIX_LOW_THRESHOLD = 15.0
VIX_NORMAL_THRESHOLD = 25.0
VIX_HIGH_THRESHOLD = 30.0
VIX_EXTREME_THRESHOLD = 35.0

# VRP Strategy Parameters
VRP_THRESHOLD_LOW = -2.0
VRP_THRESHOLD_HIGH = 5.0
VRP_QUALITY_THRESHOLD = 0.3

# Call Spread Parameters
CALL_SPREAD_MIN_DTE = 25
CALL_SPREAD_MAX_DTE = 35
CALL_SPREAD_MAX_WIDTH = 75  # Points
CALL_SPREAD_MIN_CREDIT_RATIO = 0.20  # 20% of spread width

# Position Sizing
MIN_CONTRACTS = 8
MAX_CONTRACTS = 20
BASE_POSITION_SIZE = 15

# Strike Selection
STRIKE_MULTIPLE = 25  # SPX strikes are multiples of 25

# Call Spread Strike Ranges (as multipliers of SPX price)
BULLISH_SHORT_STRIKE_MIN = 0.98   # 2% ITM
BULLISH_SHORT_STRIKE_MAX = 1.01   # 1% OTM
BULLISH_LONG_STRIKE_MIN = 1.01    # 1% OTM
BULLISH_LONG_STRIKE_MAX = 1.04    # 4% OTM

BEARISH_SHORT_STRIKE_MIN = 1.00   # ATM
BEARISH_SHORT_STRIKE_MAX = 1.02   # 2% OTM
BEARISH_LONG_STRIKE_MIN = 1.02    # 2% OTM
BEARISH_LONG_STRIKE_MAX = 1.05    # 5% OTM

# Risk Management
MAX_DRAWDOWN_THRESHOLD = 0.05  # 5%
STOP_LOSS_THRESHOLD = 0.50     # 50% of premium

# Report Configuration
REPORTS_DIR = "reports"
TRADES_DIR = "trades"
CHARTS_DIR = "charts"

# PDF Report Settings
PDF_TITLE_FONT_SIZE = 16
PDF_HEADER_FONT_SIZE = 14
PDF_BODY_FONT_SIZE = 11

# Environment Variables
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')

# File Naming Patterns
TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
DATE_FORMAT = "%Y-%m-%d"

# Performance Thresholds
MIN_WIN_RATE = 0.50
MIN_PROFIT_FACTOR = 2.0
MAX_ACCEPTABLE_DRAWDOWN = 0.10

# Data Validation
MIN_OPTION_PRICE = 0.05
MAX_OPTION_PRICE = 1000.0
MIN_SPX_PRICE = 1000
MAX_SPX_PRICE = 10000

# Signal Generation
CONFIDENCE_THRESHOLD = 0.5
MIN_SIGNAL_STRENGTH = 0.3
MAX_SIGNALS_PER_DAY = 3

# Backtest Configuration
DEFAULT_HOLDING_DAYS = 1
MAX_HOLDING_DAYS = 5
LOOKBACK_DAYS = 252  # 1 year

# Technical Analysis
RSI_PERIOD = 14
RSI_OVERBOUGHT = 70
RSI_OVERSOLD = 30
MA_SHORT_PERIOD = 10
MA_LONG_PERIOD = 20

# Volatility Calculations
VOLATILITY_WINDOW = 30
REALIZED_VOL_WINDOW = 21
IMPLIED_VOL_THRESHOLD = 0.15

# Greeks Thresholds
MIN_DELTA = 0.10
MAX_DELTA = 0.90
MIN_GAMMA = 0.001
MAX_THETA = -0.10

# Data Quality Checks
MIN_VOLUME = 10
MIN_OPEN_INTEREST = 100
MAX_BID_ASK_SPREAD = 0.50

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Strategy Names
VRP_STRATEGY_NAME = "VRP Enhanced Strategy"
CALL_SPREAD_STRATEGY_NAME = "Call Spread Strategy"

# File Extensions
CSV_EXTENSION = ".csv"
PDF_EXTENSION = ".pdf"
PNG_EXTENSION = ".png"
