"""
Constants for Pure VIX Options Trading Strategy
Based on breakthrough discovery: 93.6% performance improvement through pure VIX filtering
"""

# ============================================================================
# VIX FILTERING PARAMETERS (ENHANCED WITH CRITICAL FIX)
# ============================================================================

# Enhanced 3-tier VIX regime system (based on investigation findings)
VIX_OPTIMAL_LOW = 15.0         # Start of optimal VIX range
VIX_OPTIMAL_HIGH = 25.0        # End of optimal VIX range
VIX_EXTREME_LOW = 12.0         # Extreme low VIX level
VIX_EXTREME_HIGH = 30.0        # Extreme high VIX level

# Legacy thresholds (for backward compatibility)
VIX_HIGH_THRESHOLD = 25.0      # Skip all trades when VIX > 25
VIX_LOW_THRESHOLD = 15.0       # Transition to low VIX regime

# VIX momentum parameters
VIX_MOMENTUM_LOOKBACK = 9      # Days for VIX9D calculation
VIX_MOMENTUM_THRESHOLD = 2.0   # Threshold for significant momentum

# ENHANCED POSITION SIZING MULTIPLIERS (OPTIMIZED FOR HIGH-CONFIDENCE SCENARIOS)
LOW_VIX_MULTIPLIER = 8       # Maximum confidence: 90.9% win rate (exceptional performance)
OPTIMAL_VIX_MULTIPLIER = 15   # High confidence: 78.4% win rate, primary driver (560 trades)
HIGH_VIX_MULTIPLIER = 3      # Moderate confidence: 68.3% win rate (defensive but improved)
MOMENTUM_MULTIPLIER = 8     # Enhanced momentum boost for high-confidence signals
EXTREME_MULTIPLIER = 8      # Enhanced at VIX extremes (rare high-confidence conditions)
MIN_POSITION_MULTIPLIER = 0.3  # Minimum position size (risk floor)
MAX_POSITION_MULTIPLIER = 25.0 # Enhanced maximum for high-confidence scenarios

# ============================================================================
# TRADING STRATEGY PARAMETERS
# ============================================================================

# Signal strength thresholds
MIN_SIGNAL_STRENGTH = 0.6      # Minimum signal strength to trade
STRONG_SIGNAL_THRESHOLD = 0.8  # Threshold for strong signals

# Position sizing and risk management (enhanced for dynamic scaling)
STARTING_CAPITAL = 100000      # Starting capital in dollars
MAX_RISK_PER_TRADE = 0.05      # Maximum 5% risk per trade (validated for large positions)
DEFAULT_POSITION_SIZE = 1      # Default number of contracts (base unit)
MAX_POSITION_SIZE = 20         # Enhanced maximum contracts per trade (dynamic scaling)

# Trade timing parameters (testing different holding periods)
MAX_HOLD_DAYS = 5              # Maximum days to hold position (testing 1-5 days)
TARGET_EXPIRATION_MIN = 7      # Minimum days to expiration
TARGET_EXPIRATION_MAX = 21     # Maximum days to expiration

# Holding period testing configuration
HOLDING_PERIODS_TO_TEST = [1, 2, 3, 4, 5]  # Test different holding periods
CURRENT_HOLDING_PERIOD_TEST = 5             # Current test (will be updated)

# ============================================================================
# OPTION SELECTION PARAMETERS
# ============================================================================

# Strike selection parameters
ATM_TOLERANCE = 0.02           # 2% tolerance for ATM selection
OTM_SLIGHT_DISTANCE = 0.01     # 1% OTM for slight OTM options
OTM_MODERATE_DISTANCE = 0.02   # 2% OTM for moderate OTM options
OTM_AGGRESSIVE_DISTANCE = 0.03 # 3% OTM for aggressive OTM options

# Spread parameters
SPREAD_WIDTH_POINTS = 25       # Standard spread width in points
SPREAD_WIDTH_CONSERVATIVE = 50 # Conservative spread width
SPREAD_WIDTH_AGGRESSIVE = 75   # Aggressive spread width

# Option filtering parameters
MIN_VOLUME = 10                # Minimum option volume
MIN_OPEN_INTEREST = 50         # Minimum open interest
MAX_BID_ASK_SPREAD = 0.5       # Maximum bid-ask spread ratio

# ============================================================================
# MARKET DATA PARAMETERS
# ============================================================================

# E-mini futures parameters
EMINI_DRIFT_THRESHOLD = 0.002  # 0.2% drift threshold (tightened)
EMINI_STRONG_DRIFT = 0.005     # 0.5% strong drift threshold

# Options wall parameters
MIN_WALL_STRENGTH = 5.0        # Minimum wall strength
VOLUME_THRESHOLD = 0.8         # 80% volume threshold for walls

# Data quality parameters
MIN_DATA_QUALITY = 0.2         # Minimum data quality score
OUTLIER_THRESHOLD = 3.0        # Standard deviations for outlier detection

# ============================================================================
# ENHANCED DIRECTIONAL STRATEGY PARAMETERS (INVESTIGATION OPTIMIZED)
# ============================================================================

# Approved strategy types (directional buying only)
APPROVED_STRATEGIES = [
    'long_calls',
    'long_puts',
    'call_spreads',
    'put_spreads'
]

# Banned strategy types (premium selling eliminated)
BANNED_STRATEGIES = [
    'short_straddles',
    'short_strangles',
    'iron_condors',
    'iron_butterflies',
    'covered_calls',
    'cash_secured_puts',
    'naked_puts',
    'naked_calls',
    'call_butterflies',  # Removed based on investigation
    'put_butterflies'    # Removed based on investigation
]

# Enhanced strategy selection (based on investigation findings)
PREFERRED_BEARISH_STRATEGY = 'long_puts'    # 88.9% win rate in investigation
PREFERRED_BULLISH_STRATEGY = 'long_calls'   # Solid performance

# Strategy selection based on VIX regime and signal direction
VIX_REGIME_STRATEGIES = {
    'LOW_VIX': {
        'BULLISH': 'long_calls',     # Simple strategies in low VIX
        'BEARISH': 'long_puts'       # Avoid spreads in low VIX
    },
    'OPTIMAL_VIX': {
        'BULLISH': 'long_calls',     # Can use any strategy
        'BEARISH': 'long_puts'       # Prefer long puts (best performer)
    },
    'HIGH_VIX': {
        'BULLISH': 'call_spreads',   # Defensive in high VIX
        'BEARISH': 'long_puts'       # Long puts still preferred
    }
}

# ============================================================================
# PERFORMANCE TRACKING PARAMETERS
# ============================================================================

# Reporting parameters
RECENT_TRADES_COUNT = 15       # Number of recent trades to show in reports
CHART_DPI = 300               # Chart resolution for reports
CHART_STYLE = 'seaborn-v0_8' # Chart style

# PDF Report Configuration (v3.2 Enhancement)
PDF_LAST_TRADES_COUNT = 15     # Number of recent trades to show in PDF
PDF_TRADES_PER_PAGE = 35       # Trades per page in complete trade list
PDF_FONT_SIZE_TITLE = 16       # Title font size
PDF_FONT_SIZE_HEADER = 14      # Header font size
PDF_FONT_SIZE_BODY = 10        # Body text font size
PDF_FONT_SIZE_TABLE = 9        # Table font size

# Performance Simulation Constants (extracted magic numbers)
OPTION_PRICE_MULTIPLIER = 0.02 # Option entry price as % of underlying (2%)
BULLISH_EXIT_MULTIPLIER = 1.1  # Bullish exit price multiplier (+10%)
BEARISH_EXIT_MULTIPLIER = 0.9  # Bearish exit price multiplier (-10%)
CONTRACT_MULTIPLIER = 100      # Standard option contract multiplier
PROGRESS_UPDATE_INTERVAL = 50  # Progress update frequency during backtest

# Performance metrics
BENCHMARK_RETURN = 0.0        # Benchmark return for comparison
RISK_FREE_RATE = 0.02         # Risk-free rate for Sharpe ratio

# ============================================================================
# FILE PATHS AND DIRECTORIES
# ============================================================================

# Data directories
OPTIONS_DATA_DIR = '../optionhistory'
EMINI_DATA_PATH = '/Users/<USER>/Downloads/systems/strategy_package/data/securities/ES_full_5min_continuous_ratio_adjusted.txt'
VIX_DATA_PATH = '/Users/<USER>/Downloads/systems/strategy_package/data/securities/VIX.txt'

# Output directories
REPORTS_DIR = 'reports'
DATA_DIR = 'data'
CHARTS_DIR = 'reports'

# File naming patterns
TRADES_FILE = 'pure_vix_trades.csv'
DAILY_FILE = 'pure_vix_daily.csv'
PERFORMANCE_CHART = 'pure_vix_performance.png'
PDF_REPORT_PREFIX = 'Pure_VIX_Strategy_Report'

# ============================================================================
# LOGGING AND DEBUG PARAMETERS
# ============================================================================

# Logging levels
LOG_LEVEL = 'INFO'            # Logging level (DEBUG, INFO, WARNING, ERROR)
VERBOSE_LOGGING = False       # Enable verbose logging
PROGRESS_UPDATE_INTERVAL = 50 # Update progress every N trades

# Debug parameters
DEBUG_MODE = False            # Enable debug mode
SAVE_INTERMEDIATE_RESULTS = True  # Save intermediate calculations

# ============================================================================
# VALIDATION PARAMETERS
# ============================================================================

# Data validation
MAX_PRICE_CHANGE = 0.2        # Maximum 20% price change filter
MIN_TRADING_DAYS = 250        # Minimum trading days for backtest
MAX_MISSING_DATA_PCT = 0.05   # Maximum 5% missing data tolerance

# Strategy validation
MIN_TRADES_FOR_VALIDATION = 20  # Minimum trades for strategy validation
MAX_CONSECUTIVE_LOSSES = 10     # Maximum consecutive losses before review

# ============================================================================
# PURE VIX STRATEGY CONFIGURATION
# ============================================================================

# Strategy name and version
STRATEGY_NAME = "Enhanced VIX Options Strategy"
STRATEGY_VERSION = "3.2"
STRATEGY_DESCRIPTION = "Enhanced VIX strategy with 20-contract dynamic scaling, comprehensive code cleanup, and professional reporting"

# Key features enabled
ENABLE_VIX_FILTERING = True
ENABLE_MOMENTUM_ADJUSTMENT = True
ENABLE_EXTREME_PROTECTION = True
ENABLE_DIRECTIONAL_ONLY = True

# Key features disabled (complex regime filtering removed)
ENABLE_REGIME_FILTERING = False
ENABLE_GREEKS_ANALYSIS = False
ENABLE_TERM_STRUCTURE = False
ENABLE_UTY_MOMENTUM = False
ENABLE_QUALITY_SCORING = False
ENABLE_PREMIUM_SELLING = False

# ============================================================================
# PERFORMANCE TARGETS
# ============================================================================

# Target performance metrics (based on breakthrough discovery)
TARGET_IMPROVEMENT_PCT = 93.6  # Target P&L improvement percentage
TARGET_WIN_RATE = 31.0        # Target win rate percentage
TARGET_MAX_DRAWDOWN = 0.05     # Target maximum drawdown (5%)
TARGET_SHARPE_RATIO = 0.5      # Target Sharpe ratio

# Success criteria
SUCCESS_MIN_IMPROVEMENT = 50.0  # Minimum 50% improvement for success
SUCCESS_MIN_TRADES = 50        # Minimum trades for statistical significance

# ============================================================================
# CONFIGURATION VALIDATION
# ============================================================================

def validate_constants():
    """Validate that all constants are properly configured"""
    
    # VIX threshold validation
    assert VIX_LOW_THRESHOLD < VIX_HIGH_THRESHOLD, "VIX low threshold must be less than high threshold"
    assert VIX_EXTREME_LOW < VIX_LOW_THRESHOLD, "VIX extreme low must be less than low threshold"
    assert VIX_HIGH_THRESHOLD < VIX_EXTREME_HIGH, "VIX high threshold must be less than extreme high"
    
    # Position multiplier validation
    assert MIN_POSITION_MULTIPLIER > 0, "Minimum position multiplier must be positive"
    assert MAX_POSITION_MULTIPLIER > MIN_POSITION_MULTIPLIER, "Max multiplier must be greater than min"
    assert LOW_VIX_MULTIPLIER <= MAX_POSITION_MULTIPLIER, "Low VIX multiplier within bounds"
    
    # Risk management validation
    assert 0 < MAX_RISK_PER_TRADE < 1, "Max risk per trade must be between 0 and 1"
    assert MAX_HOLD_DAYS > 0, "Max hold days must be positive"
    assert TARGET_EXPIRATION_MIN < TARGET_EXPIRATION_MAX, "Min expiration must be less than max"
    
    # Strategy validation
    assert len(APPROVED_STRATEGIES) > 0, "Must have at least one approved strategy"
    assert PREFERRED_BULLISH_STRATEGY in APPROVED_STRATEGIES, "Preferred bullish strategy must be approved"
    assert PREFERRED_BEARISH_STRATEGY in APPROVED_STRATEGIES, "Preferred bearish strategy must be approved"
    
    print("✅ All constants validated successfully")

# Run validation when module is imported
if __name__ == "__main__":
    validate_constants()
    print(f"✅ {STRATEGY_NAME} v{STRATEGY_VERSION} constants loaded")
    print(f"🎯 Target improvement: {TARGET_IMPROVEMENT_PCT}%")
    print(f"📊 VIX thresholds: Low={VIX_LOW_THRESHOLD}, High={VIX_HIGH_THRESHOLD}")
