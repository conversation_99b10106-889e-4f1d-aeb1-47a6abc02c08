"""
Constants for JPM Options Trading Strategies
Extracted from working strategies to avoid magic numbers
"""
from datetime import datetime

# Data Paths
VIX_DATA_PATH = "/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/VIX.csv"
SPX_OPTIONS_BASE_PATH = "/Users/<USER>/Downloads/optionhistory"

# Date Configuration
START_DATE = "2023-05-01"
END_DATE = datetime.now().strftime("%Y-%m-%d")  # Auto-update to today

# Trading Parameters
INITIAL_CAPITAL = 100000
COMMISSION_PER_CONTRACT = 1.0

# VIX Thresholds
VIX_LOW_THRESHOLD = 12.0
VIX_HIGH_THRESHOLD = 22.0

# VRP Thresholds
VRP_THRESHOLD = 2.0
VRP_QUALITY_THRESHOLD = 0.3

# Call Spread Parameters
CALL_SPREAD_MIN_DTE = 25
CALL_SPREAD_MAX_DTE = 35
CALL_SPREAD_MAX_WIDTH = 150  # Points
CALL_SPREAD_MIN_CREDIT_RATIO = 0.15  # 15% of spread width

# Position Sizing
MIN_CONTRACTS = 20
MAX_CONTRACTS = 60
BASE_POSITION_SIZE = 15

# Strike Selection
STRIKE_MULTIPLE = 25  # SPX strikes are multiples of 25

# Call Spread Strike Ranges (as multipliers of SPX price)
BULLISH_SHORT_STRIKE_MIN = 0.97   # 3% ITM
BULLISH_SHORT_STRIKE_MAX = 1.01   # 1% OTM
BULLISH_LONG_STRIKE_MIN = 1.01    # 1% OTM
BULLISH_LONG_STRIKE_MAX = 1.06    # 6% OTM

BEARISH_SHORT_STRIKE_MIN = 1.00   # ATM
BEARISH_SHORT_STRIKE_MAX = 1.02   # 2% OTM
BEARISH_LONG_STRIKE_MIN = 1.02    # 2% OTM
BEARISH_LONG_STRIKE_MAX = 1.07    # 7% OTM

# Report Configuration
REPORTS_DIR = "reports"
TRADES_DIR = "trades"

# File Naming
TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
DATE_FORMAT = "%Y-%m-%d"

# Performance Thresholds
MIN_WIN_RATE = 0.50
MIN_PROFIT_FACTOR = 2.0

# Data Validation
MIN_OPTION_PRICE = 0.05
MAX_OPTION_PRICE = 1000.0

# Technical Analysis
RSI_PERIOD = 14
RSI_OVERBOUGHT = 70
RSI_OVERSOLD = 30
