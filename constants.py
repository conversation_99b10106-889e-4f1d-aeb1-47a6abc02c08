"""
Constants for Enhanced Reverse Signal Strategy
Centralized configuration for all magic numbers and parameters
"""

# ============================================================================
# VIX FILTERING PARAMETERS (ENHANCED REVERSE STRATEGY)
# ============================================================================

# VIX range thresholds for Enhanced Reverse Strategy
VIX_LOW_THRESHOLD = 15.0           # Below this: Low VIX (reverse signals)
VIX_LOW_NORMAL_LOW = 15.0          # Low-Normal VIX range start (SKIP)
VIX_LOW_NORMAL_HIGH = 20.0         # Low-Normal VIX range end (SKIP)
VIX_NORMAL_HIGH_LOW = 20.0         # Normal-High VIX range start (reverse signals)
VIX_NORMAL_HIGH_HIGH = 25.0        # Normal-High VIX range end (reverse signals)
VIX_HIGH_LOW = 25.0                # High VIX range start (reverse signals)
VIX_HIGH_HIGH = 30.0               # High VIX range end (reverse signals)
VIX_VERY_HIGH_LOW = 30.0           # Very High VIX range start (original signals)
VIX_VERY_HIGH_HIGH = 35.0          # Very High VIX range end (original signals)

# VIX extreme thresholds
VIX_EXTREME_LOW = 12.0             # Extreme low VIX for confidence boost
VIX_HIGH_BOOST = 27.0              # High VIX level for confidence boost

# Legacy VIX thresholds (for compatibility)
VIX_OPTIMAL_LOW = 18.0             # Avoid low VIX (poor performance)
VIX_OPTIMAL_HIGH = 30.0            # Extend optimal range (high VIX performs better)
VIX_EXTREME_HIGH = 35.0            # Extreme high VIX level

# VIX momentum parameters
VIX_MOMENTUM_LOOKBACK = 9          # Days for VIX9D calculation

# Position sizing multipliers (legacy)
LOW_VIX_MULTIPLIER = 0.3           # Reduce exposure in low VIX (terrible performance)
OPTIMAL_VIX_MULTIPLIER = 1.0       # Standard exposure in optimal range
HIGH_VIX_MULTIPLIER = 1.5          # Increase exposure in high VIX (better performance)

# ============================================================================
# ENHANCED REVERSE STRATEGY CONFIDENCE LEVELS
# ============================================================================

# Confidence levels for different VIX conditions (based on historical analysis)
CONFIDENCE_VERY_HIGH_VIX = {
    'win_rate': 0.75,
    'avg_pnl': 625,
    'confidence': 0.95,
    'base_multiplier': 3.0
}

CONFIDENCE_LOW_VIX_REVERSED = {
    'win_rate': 0.61,
    'avg_pnl': 251,
    'confidence': 0.85,
    'base_multiplier': 2.5
}

CONFIDENCE_NORMAL_HIGH_VIX_REVERSED = {
    'win_rate': 0.57,
    'avg_pnl': 30,
    'confidence': 0.70,
    'base_multiplier': 1.5
}

CONFIDENCE_HIGH_VIX_REVERSED = {
    'win_rate': 0.55,
    'avg_pnl': 93,
    'confidence': 0.75,
    'base_multiplier': 2.0
}

# Default confidence levels
DEFAULT_WIN_RATE = 0.50
DEFAULT_AVG_PNL = 100
DEFAULT_BASE_MULTIPLIER = 1.0

# ============================================================================
# SIGNAL GENERATION PARAMETERS
# ============================================================================

# Default signal parameters
DEFAULT_SIGNAL_STRENGTH = 0.7
DEFAULT_CONFIDENCE_SCORE = 0.5

# Signal strength levels
SIGNAL_STRENGTH_LOW = 0.6
SIGNAL_STRENGTH_MEDIUM = 0.7
SIGNAL_STRENGTH_HIGH = 0.8
SIGNAL_STRENGTH_VERY_HIGH = 0.9

# Confidence score thresholds
CONFIDENCE_THRESHOLD_HIGH = 0.8
CONFIDENCE_THRESHOLD_MEDIUM = 0.7
CONFIDENCE_THRESHOLD_LOW = 0.5

# Confidence score levels
CONFIDENCE_SCORE_LOW_VIX = 0.85
CONFIDENCE_SCORE_LOW_VIX_EXTREME = 0.90
CONFIDENCE_SCORE_NORMAL_HIGH = 0.70
CONFIDENCE_SCORE_NORMAL_HIGH_RISING = 0.75
CONFIDENCE_SCORE_HIGH_VIX = 0.75
CONFIDENCE_SCORE_HIGH_VIX_BOOST = 0.80
CONFIDENCE_SCORE_VERY_HIGH_RISING = 0.95
CONFIDENCE_SCORE_VERY_HIGH_FALLING = 0.90

# ============================================================================
# POSITION SIZING PARAMETERS
# ============================================================================

# Position sizing thresholds
POSITION_SIZE_VERY_HIGH_CONFIDENCE = 0.90
POSITION_SIZE_HIGH_CONFIDENCE = 0.80
POSITION_SIZE_MEDIUM_CONFIDENCE = 0.70

# Position size ranges
POSITION_SIZE_VERY_HIGH_MIN = 15
POSITION_SIZE_VERY_HIGH_MAX = 20
POSITION_SIZE_HIGH_MIN = 10
POSITION_SIZE_HIGH_MAX = 15
POSITION_SIZE_MEDIUM_MIN = 5
POSITION_SIZE_MEDIUM_MAX = 10
POSITION_SIZE_LOW_MIN = 1
POSITION_SIZE_LOW_MAX = 5

# Position sizing calculation parameters
CONFIDENCE_MULTIPLIER_BASE = 1.0
CONFIDENCE_MULTIPLIER_SCALE = 4.0
CONFIDENCE_MULTIPLIER_OFFSET = 0.5
RISK_PER_CONTRACT = 1000           # Assume $1000 risk per contract

# ============================================================================
# TRADING PARAMETERS
# ============================================================================

# Capital and position sizing
STARTING_CAPITAL = 100000          # Starting capital in dollars
RISK_PER_TRADE = 0.02              # Risk 2% of capital per trade
MAX_CONTRACTS = 20                 # Maximum contracts per trade (increased for high confidence)
MIN_CONTRACTS = 1                  # Minimum contracts per trade

# Holding period
DEFAULT_HOLDING_DAYS = 1           # Default 1-day holding period

# Options parameters
SPX_STRIKE_MULTIPLE = 25           # SPX strikes are multiples of 25
TARGET_EXPIRY_DAYS = 30            # Target 30-day expiry
SPX_MULTIPLIER = 100               # SPX contract multiplier
OPTION_PRICE_MULTIPLIER = 0.02     # 2% of underlying for option pricing

# Trade simulation parameters
ENTRY_PRICE_BASE = 25              # Base entry price for simulation
ENTRY_PRICE_VIX_MULTIPLIER = 1.5   # VIX impact on entry price
ENTRY_PRICE_VIX_OFFSET = 20        # VIX offset for entry price calculation
MIN_ENTRY_PRICE = 5                # Minimum entry price
MIN_EXIT_PRICE = 1                 # Minimum exit price
EXIT_PRICE_DIVISOR = 100           # Divisor for exit price calculation

# Win/loss simulation parameters
WIN_MULTIPLIER_LOW = 0.8           # Low end of winner multiplier range
WIN_MULTIPLIER_HIGH = 1.5          # High end of winner multiplier range
LOSS_MULTIPLIER_LOW = 0.3          # Low end of loser multiplier range
LOSS_MULTIPLIER_HIGH = 0.8         # High end of loser multiplier range
CONFIDENCE_ADJUSTMENT_BASE = 0.8   # Base confidence adjustment
CONFIDENCE_ADJUSTMENT_SCALE = 0.4  # Scale for confidence adjustment

# Legacy VIX signal thresholds (for compatibility)
VIX_LOW_SIGNAL = 18                # VIX below this = bearish signal (more selective)
VIX_HIGH_SIGNAL = 22               # VIX above this = bullish signal (favor bullish trades)
VIX_SKIP_LOW_THRESHOLD = 15        # Skip all trades when VIX < 15 (terrible performance)
VIX_SKIP_HIGH_THRESHOLD = 35       # Skip all trades when VIX > 35 (extreme conditions)

# ============================================================================
# PERFORMANCE ANALYSIS PARAMETERS
# ============================================================================

# Performance calculation parameters
PERCENTAGE_MULTIPLIER = 100        # Convert to percentage
HIGH_CONFIDENCE_THRESHOLD = 0.8    # Threshold for high confidence analysis

# Position size analysis thresholds
LARGE_POSITION_THRESHOLD = 15      # Large position threshold
MEDIUM_POSITION_MIN = 5            # Medium position minimum
SMALL_POSITION_MAX = 5             # Small position maximum

# Chart and visualization parameters
CHART_FIGURE_WIDTH = 15            # Chart figure width
CHART_FIGURE_HEIGHT = 10           # Chart figure height
CHART_FIGURE_WIDTH_LARGE = 20      # Large chart figure width
CHART_FIGURE_HEIGHT_LARGE = 12     # Large chart figure height
CHART_FIGURE_WIDTH_SMALL = 12      # Small chart figure width
CHART_FIGURE_HEIGHT_SMALL = 8      # Small chart figure height
CHART_SUBPLOT_MAIN = (2, 1, 1)     # Main subplot position
CHART_SUBPLOT_POSITION = (2, 1, 2) # Position subplot position
CHART_SUBPLOT_2x2 = (2, 2)         # 2x2 subplot grid
CHART_SUBPLOT_2x3 = (2, 3)         # 2x3 subplot grid
CHART_SUBPLOT_3x2 = (3, 2)         # 3x2 subplot grid
CHART_LINE_WIDTH = 2               # Chart line width
CHART_LINE_WIDTH_THICK = 3         # Thick line width
CHART_SCATTER_SIZE = 50            # Scatter plot point size
CHART_SCATTER_SIZE_LARGE = 80      # Large scatter plot point size
CHART_SCATTER_ALPHA = 0.6          # Scatter plot transparency
CHART_BAR_ALPHA = 0.7              # Bar chart transparency
CHART_GRID_ALPHA = 0.3             # Grid transparency
CHART_AXHLINE_ALPHA = 0.7          # Horizontal line transparency
CHART_DPI = 300                    # Chart DPI for saving
CHART_TITLE_FONTSIZE = 14          # Chart title font size
CHART_TITLE_FONTSIZE_LARGE = 16    # Large chart title font size
CHART_YLABEL_FONTSIZE = 12         # Y-axis label font size
CHART_XLABEL_FONTSIZE = 12         # X-axis label font size
CHART_LEGEND_FONTSIZE = 10         # Legend font size
CHART_TICK_FONTSIZE = 10           # Tick label font size

# Chart colors
CHART_COLOR_PROFIT = 'darkgreen'   # Profit color
CHART_COLOR_LOSS = 'darkred'       # Loss color
CHART_COLOR_NEUTRAL = 'gray'       # Neutral color
CHART_COLOR_PRIMARY = 'steelblue'  # Primary color
CHART_COLOR_SECONDARY = 'orange'   # Secondary color
CHART_COLOR_ACCENT = 'purple'      # Accent color
CHART_COLOR_WARNING = 'red'        # Warning color
CHART_COLOR_SUCCESS = 'green'      # Success color

# Date range parameters
DEFAULT_START_DATE = "2023-05-01"  # Default strategy start date
DEFAULT_END_DATE = "2024-12-31"    # Default strategy end date

# Display formatting parameters
SEPARATOR_LENGTH = 70              # Length of separator lines
SEPARATOR_SHORT = 60               # Length of short separator lines

# ============================================================================
# DATA PATHS
# ============================================================================

# Data directories
OPTIONS_DATA_DIR = '../optionhistory'
SECURITIES_DATA_DIR = '/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities'

# VIX data file paths
VIX_DATA_FILES = {
    'VIX': f'{SECURITIES_DATA_DIR}/VIX_full_1day.txt',
    'VIX1D': f'{SECURITIES_DATA_DIR}/VIX1D_full_1day.txt',
    'VIX9D': f'{SECURITIES_DATA_DIR}/VIX9D_full_1day.txt'
}

# Output directories
REPORTS_DIR = 'reports'

# File naming patterns
ENHANCED_TRADES_PREFIX = 'enhanced_reverse_trades_'
REVERSE_TRADES_PREFIX = 'reverse_signal_trades_'
EQUITY_CURVE_FILENAME = 'enhanced_reverse_strategy_equity_curve.png'
PERFORMANCE_METRICS_FILENAME = 'performance_metrics_analysis.png'
VIX_ANALYSIS_FILENAME = 'vix_condition_analysis.png'
POSITION_SIZING_FILENAME = 'position_sizing_analysis.png'
MONTHLY_PERFORMANCE_FILENAME = 'monthly_performance_breakdown.png'
DRAWDOWN_ANALYSIS_FILENAME = 'drawdown_analysis.png'
WIN_LOSS_DISTRIBUTION_FILENAME = 'win_loss_distribution.png'

# ============================================================================
# STRATEGY CONFIGURATION
# ============================================================================

# Strategy name and version
STRATEGY_NAME = "Enhanced Reverse Signal Strategy"
STRATEGY_VERSION = "2.0"

# ============================================================================
# PDF REPORT CONSTANTS
# ============================================================================

# ChatGPT API parameters
CHATGPT_MODEL = "gpt-4"
CHATGPT_TEMPERATURE = 0.7
CHATGPT_MAX_TOKENS_DEFAULT = 500
CHATGPT_MAX_TOKENS_EXECUTIVE = 400
CHATGPT_MAX_TOKENS_OVERVIEW = 300
CHATGPT_MAX_TOKENS_SIGNAL = 250
CHATGPT_MAX_TOKENS_NO_SIGNAL = 200
CHATGPT_MAX_TOKENS_PERFORMANCE = 350

# PDF styling constants
PDF_TITLE_FONTSIZE = 24
PDF_TITLE_SPACE_AFTER = 30
PDF_HEADING_FONTSIZE = 16
PDF_HEADING_SPACE_AFTER = 12
PDF_HEADING_SPACE_BEFORE = 20
PDF_SUBHEADING_FONTSIZE = 14
PDF_SUBHEADING_SPACE_AFTER = 10
PDF_SUBHEADING_SPACE_BEFORE = 15
PDF_BODY_FONTSIZE = 11
PDF_BODY_SPACE_AFTER = 8
PDF_BODY_LEFT_INDENT = 0
PDF_BODY_RIGHT_INDENT = 0
PDF_BULLET_FONTSIZE = 11
PDF_BULLET_SPACE_AFTER = 6
PDF_BULLET_LEFT_INDENT = 20
PDF_BULLET_INDENT = 10
PDF_HIGHLIGHT_FONTSIZE = 12
PDF_HIGHLIGHT_SPACE_AFTER = 10

# PDF margins
PDF_RIGHT_MARGIN = 72
PDF_LEFT_MARGIN = 72
PDF_TOP_MARGIN = 72
PDF_BOTTOM_MARGIN = 18

# PDF spacing
PDF_SPACER_STANDARD = 20
PDF_SPACER_SMALL = 15
PDF_SPACER_TINY = 10

# Table styling
TABLE_HEADER_FONTSIZE = 10
TABLE_BODY_FONTSIZE = 8
TABLE_HEADER_PADDING = 12
TABLE_CONDITION_HEADER_FONTSIZE = 9
TABLE_TRADE_HEADER_FONTSIZE = 8
TABLE_TRADE_BODY_FONTSIZE = 7
TABLE_TRADE_PADDING = 8
TABLE_MONTHLY_HEADER_FONTSIZE = 9

# Report content constants
RECENT_TRADES_COUNT = 15
CONDITION_NAME_MAX_LENGTH = 20
DATE_FORMAT = '%Y-%m-%d'
DATE_SLICE_LENGTH = 10

# Historical performance references
ORIGINAL_STRATEGY_WIN_RATE = 43.2
ORIGINAL_STRATEGY_RETURN = -49.0
ORIGINAL_STRATEGY_PNL = -49000
BASIC_REVERSE_WIN_RATE = 59.8
BASIC_REVERSE_RETURN = 210.6
BASIC_REVERSE_PNL = 210600
BASIC_REVERSE_DRAWDOWN = 6.4
BASIC_REVERSE_PROFIT_FACTOR = 2.96

# VIX analysis constants for PDF
PDF_VIX_LOW_WIN_RATE = 61
PDF_VIX_NORMAL_HIGH_WIN_RATE = 57
PDF_VIX_HIGH_WIN_RATE = 55
PDF_VIX_VERY_HIGH_WIN_RATE = 75
PDF_VIX_RECORDS_ANALYZED = 421
PDF_SIGNALS_GENERATED = 266

# Position sizing display constants
PDF_VERY_HIGH_CONFIDENCE_MIN = 15
PDF_VERY_HIGH_CONFIDENCE_MAX = 20
PDF_HIGH_CONFIDENCE_MIN = 10
PDF_HIGH_CONFIDENCE_MAX = 15
PDF_MEDIUM_CONFIDENCE_MIN = 5
PDF_MEDIUM_CONFIDENCE_MAX = 10
PDF_LOW_CONFIDENCE_MIN = 1
PDF_LOW_CONFIDENCE_MAX = 5

# Confidence range constants
PDF_CONFIDENCE_RANGE_LOW = 0.70
PDF_CONFIDENCE_RANGE_HIGH = 0.95
PDF_HIGH_CONFIDENCE_THRESHOLD_DISPLAY = 0.8
PDF_MEDIUM_CONFIDENCE_THRESHOLD_DISPLAY = 0.7

# Risk management display constants
PDF_MAX_DRAWDOWN_WARNING = 15
PDF_MAX_DRAWDOWN_HALT = 25
PDF_MAX_CAPITAL_RISK = 100
