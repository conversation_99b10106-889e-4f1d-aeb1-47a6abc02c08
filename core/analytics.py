"""
Analytics Module for Options Trading Strategies
Handles performance analysis, risk metrics, and statistical calculations
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from config.constants import *

class PerformanceAnalyzer:
    """Comprehensive performance analysis for trading strategies"""
    
    def __init__(self, trades_df: pd.DataFrame):
        self.trades_df = trades_df.copy()
        self.prepare_data()
    
    def prepare_data(self):
        """Prepare data for analysis"""
        if len(self.trades_df) == 0:
            return
        
        # Ensure date columns are datetime
        date_columns = ['entry_date', 'exit_date']
        for col in date_columns:
            if col in self.trades_df.columns:
                self.trades_df[col] = pd.to_datetime(self.trades_df[col])
        
        # Calculate cumulative metrics
        self.trades_df['cumulative_pnl'] = self.trades_df['net_pnl'].cumsum()
        self.trades_df['equity'] = INITIAL_CAPITAL + self.trades_df['cumulative_pnl']
        
        # Calculate running metrics
        self.trades_df['running_win_rate'] = self.trades_df['win_loss_flag'].expanding().mean()
        self.trades_df['running_avg_pnl'] = self.trades_df['net_pnl'].expanding().mean()
    
    def calculate_comprehensive_metrics(self) -> Dict:
        """Calculate comprehensive performance metrics"""
        if len(self.trades_df) == 0:
            return {'error': 'No trades to analyze'}
        
        metrics = {}
        
        # Basic trade statistics
        metrics.update(self._calculate_basic_stats())
        
        # Risk metrics
        metrics.update(self._calculate_risk_metrics())
        
        # Time-based analysis
        metrics.update(self._calculate_time_metrics())
        
        # Strategy-specific metrics
        metrics.update(self._calculate_strategy_metrics())
        
        return metrics
    
    def _calculate_basic_stats(self) -> Dict:
        """Calculate basic trading statistics"""
        total_trades = len(self.trades_df)
        winning_trades = len(self.trades_df[self.trades_df['net_pnl'] > 0])
        losing_trades = total_trades - winning_trades
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = self.trades_df['net_pnl'].sum()
        total_return = (total_pnl / INITIAL_CAPITAL) * 100
        
        avg_win = self.trades_df[self.trades_df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = self.trades_df[self.trades_df['net_pnl'] < 0]['net_pnl'].mean() if losing_trades > 0 else 0
        
        largest_win = self.trades_df['net_pnl'].max()
        largest_loss = self.trades_df['net_pnl'].min()
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate * 100,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'final_capital': INITIAL_CAPITAL + total_pnl
        }
    
    def _calculate_risk_metrics(self) -> Dict:
        """Calculate risk and drawdown metrics"""
        if 'equity' not in self.trades_df.columns:
            return {}
        
        # Drawdown analysis
        peak = self.trades_df['equity'].expanding().max()
        drawdown = (self.trades_df['equity'] - peak) / peak
        max_drawdown = drawdown.min() * 100
        
        # Profit factor
        gross_profit = self.trades_df[self.trades_df['net_pnl'] > 0]['net_pnl'].sum()
        gross_loss = abs(self.trades_df[self.trades_df['net_pnl'] < 0]['net_pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        returns = self.trades_df['net_pnl'] / INITIAL_CAPITAL
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
        
        # Consecutive wins/losses
        consecutive_wins = self._calculate_consecutive_runs(self.trades_df['win_loss_flag'], 1)
        consecutive_losses = self._calculate_consecutive_runs(self.trades_df['win_loss_flag'], 0)
        
        return {
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'max_consecutive_wins': max(consecutive_wins) if consecutive_wins else 0,
            'max_consecutive_losses': max(consecutive_losses) if consecutive_losses else 0,
            'avg_drawdown': drawdown.mean() * 100,
            'recovery_factor': abs(self.trades_df['net_pnl'].sum() / (max_drawdown / 100 * INITIAL_CAPITAL)) if max_drawdown != 0 else float('inf')
        }
    
    def _calculate_time_metrics(self) -> Dict:
        """Calculate time-based performance metrics"""
        if 'holding_days' not in self.trades_df.columns:
            return {}
        
        avg_holding_days = self.trades_df['holding_days'].mean()
        
        # Monthly performance
        if 'entry_date' in self.trades_df.columns:
            monthly_pnl = self.trades_df.groupby(self.trades_df['entry_date'].dt.to_period('M'))['net_pnl'].sum()
            best_month = monthly_pnl.max()
            worst_month = monthly_pnl.min()
            profitable_months = len(monthly_pnl[monthly_pnl > 0])
            total_months = len(monthly_pnl)
        else:
            best_month = worst_month = profitable_months = total_months = 0
        
        return {
            'avg_holding_days': avg_holding_days,
            'best_month': best_month,
            'worst_month': worst_month,
            'profitable_months': profitable_months,
            'total_months': total_months,
            'monthly_win_rate': (profitable_months / total_months * 100) if total_months > 0 else 0
        }
    
    def _calculate_strategy_metrics(self) -> Dict:
        """Calculate strategy-specific metrics"""
        metrics = {}
        
        # Call spread specific metrics
        if 'spread_width' in self.trades_df.columns:
            metrics.update({
                'avg_spread_width': self.trades_df['spread_width'].mean(),
                'avg_net_credit': self.trades_df['net_credit'].mean() if 'net_credit' in self.trades_df.columns else 0,
                'avg_contracts': self.trades_df['contracts'].mean()
            })
        
        # Contract size analysis
        if 'contracts' in self.trades_df.columns:
            metrics.update({
                'min_contracts': self.trades_df['contracts'].min(),
                'max_contracts': self.trades_df['contracts'].max(),
                'total_contracts_traded': self.trades_df['contracts'].sum()
            })
        
        return metrics
    
    def _calculate_consecutive_runs(self, series: pd.Series, value: int) -> List[int]:
        """Calculate consecutive runs of a specific value"""
        runs = []
        current_run = 0
        
        for val in series:
            if val == value:
                current_run += 1
            else:
                if current_run > 0:
                    runs.append(current_run)
                current_run = 0
        
        if current_run > 0:
            runs.append(current_run)
        
        return runs
    
    def generate_equity_curve_data(self) -> pd.DataFrame:
        """Generate data for equity curve plotting"""
        if len(self.trades_df) == 0:
            return pd.DataFrame()
        
        equity_data = self.trades_df[['entry_date', 'equity', 'cumulative_pnl']].copy()
        equity_data = equity_data.sort_values('entry_date')
        
        return equity_data
    
    def analyze_drawdown_periods(self) -> pd.DataFrame:
        """Analyze drawdown periods in detail"""
        if len(self.trades_df) == 0:
            return pd.DataFrame()
        
        peak = self.trades_df['equity'].expanding().max()
        drawdown = (self.trades_df['equity'] - peak) / peak * 100
        
        # Find drawdown periods
        in_drawdown = drawdown < -0.1  # More than 0.1% drawdown
        drawdown_periods = []
        
        start_idx = None
        for idx, is_dd in enumerate(in_drawdown):
            if is_dd and start_idx is None:
                start_idx = idx
            elif not is_dd and start_idx is not None:
                end_idx = idx - 1
                period_data = {
                    'start_date': self.trades_df.iloc[start_idx]['entry_date'],
                    'end_date': self.trades_df.iloc[end_idx]['entry_date'],
                    'duration_days': (self.trades_df.iloc[end_idx]['entry_date'] - self.trades_df.iloc[start_idx]['entry_date']).days,
                    'max_drawdown': drawdown.iloc[start_idx:end_idx+1].min(),
                    'recovery_days': 0  # Could be calculated if needed
                }
                drawdown_periods.append(period_data)
                start_idx = None
        
        return pd.DataFrame(drawdown_periods)
    
    def get_trade_distribution_stats(self) -> Dict:
        """Get statistical distribution of trade results"""
        if len(self.trades_df) == 0:
            return {}
        
        pnl_series = self.trades_df['net_pnl']
        
        return {
            'mean_pnl': pnl_series.mean(),
            'median_pnl': pnl_series.median(),
            'std_pnl': pnl_series.std(),
            'skewness': pnl_series.skew(),
            'kurtosis': pnl_series.kurtosis(),
            'percentile_25': pnl_series.quantile(0.25),
            'percentile_75': pnl_series.quantile(0.75),
            'percentile_95': pnl_series.quantile(0.95),
            'percentile_5': pnl_series.quantile(0.05)
        }

class SignalAnalyzer:
    """Analyze trading signals and their effectiveness"""
    
    def __init__(self, market_data: pd.DataFrame):
        self.market_data = market_data
    
    def generate_vix_signals(self) -> pd.DataFrame:
        """Generate VIX-based trading signals"""
        signals = []
        
        for date, row in self.market_data.iterrows():
            vix_level = row['close_vix']
            vrp_avg = row.get('vrp_avg', 0)
            
            # VIX regime classification
            if vix_level < VIX_LOW_THRESHOLD:
                vix_regime = 'LOW'
                confidence = 0.4
            elif vix_level < VIX_NORMAL_THRESHOLD:
                vix_regime = 'NORMAL'
                confidence = 0.6
            elif vix_level < VIX_HIGH_THRESHOLD:
                vix_regime = 'HIGH'
                confidence = 0.8
            else:
                vix_regime = 'EXTREME'
                confidence = 0.9
            
            # VRP-based signal direction
            if vrp_avg > VRP_THRESHOLD_HIGH:
                signal_direction = 'BEARISH'
            elif vrp_avg < VRP_THRESHOLD_LOW:
                signal_direction = 'BULLISH'
            else:
                signal_direction = 'NEUTRAL'
            
            # Only generate signals for non-neutral conditions
            if signal_direction != 'NEUTRAL' and confidence >= CONFIDENCE_THRESHOLD:
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'confidence_score': confidence,
                    'vix_level': vix_level,
                    'vix_regime': vix_regime,
                    'vrp_avg': vrp_avg
                })
        
        return pd.DataFrame(signals)
    
    def analyze_signal_quality(self, signals: pd.DataFrame) -> Dict:
        """Analyze the quality and distribution of generated signals"""
        if len(signals) == 0:
            return {'error': 'No signals to analyze'}
        
        signal_stats = {
            'total_signals': len(signals),
            'bullish_signals': len(signals[signals['signal_direction'] == 'BULLISH']),
            'bearish_signals': len(signals[signals['signal_direction'] == 'BEARISH']),
            'avg_confidence': signals['confidence_score'].mean(),
            'high_confidence_signals': len(signals[signals['confidence_score'] >= 0.7]),
            'signal_frequency': len(signals) / len(self.market_data) * 100
        }
        
        # VIX regime distribution
        if 'vix_regime' in signals.columns:
            regime_dist = signals['vix_regime'].value_counts()
            signal_stats['vix_regime_distribution'] = regime_dist.to_dict()
        
        return signal_stats
