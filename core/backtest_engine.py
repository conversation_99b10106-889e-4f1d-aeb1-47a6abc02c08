"""
Backtest Engine Module for Options Trading Strategies
Handles position lifecycle, P&L calculation, and performance tracking
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from config.constants import *

class Position:
    """Represents an options position with lifecycle management"""
    
    def __init__(self, 
                 entry_date: datetime,
                 strategy_type: str,
                 signal_direction: str,
                 contracts: int,
                 short_option: pd.Series = None,
                 long_option: pd.Series = None,
                 single_option: pd.Series = None):
        
        self.entry_date = entry_date
        self.strategy_type = strategy_type  # 'call_spread' or 'single_option'
        self.signal_direction = signal_direction
        self.contracts = contracts
        self.short_option = short_option
        self.long_option = long_option
        self.single_option = single_option
        
        # Position state
        self.is_open = True
        self.exit_date = None
        self.entry_prices = {}
        self.exit_prices = {}
        self.pnl = 0.0
        self.commission = 0.0
        
        # Calculate entry prices and commission
        self._calculate_entry_metrics()
    
    def _calculate_entry_metrics(self):
        """Calculate entry prices and commission"""
        if self.strategy_type == 'call_spread':
            self.entry_prices = {
                'short': self.short_option['price'],
                'long': self.long_option['price'],
                'net_credit': self.short_option['price'] - self.long_option['price']
            }
            # Commission: 2 legs × 2 transactions (open/close) × contracts
            self.commission = self.contracts * 2 * COMMISSION_PER_LEG
            
        else:  # single_option
            self.entry_prices = {
                'option': self.single_option['price']
            }
            # Commission: 1 leg × 2 transactions (open/close) × contracts
            self.commission = self.contracts * 1 * COMMISSION_PER_LEG
    
    def close_position(self, exit_date: datetime, exit_prices: Dict) -> Dict:
        """Close the position and calculate final P&L"""
        self.exit_date = exit_date
        self.exit_prices = exit_prices
        self.is_open = False
        
        # Calculate P&L based on strategy type
        if self.strategy_type == 'call_spread':
            self.pnl = self._calculate_call_spread_pnl()
        else:
            self.pnl = self._calculate_single_option_pnl()
        
        return self.get_trade_record()
    
    def _calculate_call_spread_pnl(self) -> float:
        """Calculate P&L for call spread position"""
        # Short leg P&L: We sold for entry price, buy back at exit price
        short_pnl = (self.entry_prices['short'] - self.exit_prices['short']) * self.contracts * 100
        
        # Long leg P&L: We bought for entry price, sell at exit price
        long_pnl = (self.exit_prices['long'] - self.entry_prices['long']) * self.contracts * 100
        
        # Total P&L before commission
        total_pnl = short_pnl + long_pnl
        
        return total_pnl - self.commission
    
    def _calculate_single_option_pnl(self) -> float:
        """Calculate P&L for single option position"""
        if self.signal_direction in ['BULLISH', 'CALL']:
            # Long call position
            option_pnl = (self.exit_prices['option'] - self.entry_prices['option']) * self.contracts * 100
        else:
            # Long put position
            option_pnl = (self.exit_prices['option'] - self.entry_prices['option']) * self.contracts * 100
        
        return option_pnl - self.commission
    
    def get_trade_record(self) -> Dict:
        """Get complete trade record for analysis"""
        base_record = {
            'entry_date': self.entry_date,
            'exit_date': self.exit_date,
            'strategy_type': self.strategy_type,
            'signal_direction': self.signal_direction,
            'contracts': self.contracts,
            'holding_days': (self.exit_date - self.entry_date).days if self.exit_date else None,
            'commission': self.commission,
            'net_pnl': self.pnl,
            'win_loss_flag': 1 if self.pnl > 0 else 0
        }
        
        if self.strategy_type == 'call_spread':
            base_record.update({
                'short_strike': self.short_option['Strike'],
                'long_strike': self.long_option['Strike'],
                'short_entry_price': self.entry_prices['short'],
                'long_entry_price': self.entry_prices['long'],
                'net_credit': self.entry_prices['net_credit'],
                'short_exit_price': self.exit_prices.get('short', 0),
                'long_exit_price': self.exit_prices.get('long', 0),
                'spread_width': self.long_option['Strike'] - self.short_option['Strike'],
                'max_profit': self.entry_prices['net_credit'] * self.contracts * 100,
                'max_loss': (self.long_option['Strike'] - self.short_option['Strike'] - self.entry_prices['net_credit']) * self.contracts * 100
            })
        else:
            base_record.update({
                'strike': self.single_option['Strike'],
                'entry_price': self.entry_prices['option'],
                'exit_price': self.exit_prices.get('option', 0),
                'option_type': 'call' if self.signal_direction in ['BULLISH', 'CALL'] else 'put'
            })
        
        return base_record

class BacktestEngine:
    """Main backtesting engine for options strategies"""
    
    def __init__(self, data_manager, strike_selector):
        self.data_manager = data_manager
        self.strike_selector = strike_selector
        self.positions = []
        self.closed_trades = []
        self.equity_curve = []
        self.current_capital = INITIAL_CAPITAL
    
    def run_backtest(self, signals: pd.DataFrame, strategy_type: str, holding_days: int = 1) -> Dict:
        """
        Run complete backtest for given signals
        
        Args:
            signals: DataFrame with trading signals
            strategy_type: 'call_spread' or 'single_option'
            holding_days: Number of days to hold positions
            
        Returns:
            Dictionary with backtest results
        """
        print(f"🔄 Running {strategy_type} backtest...")
        
        self.positions = []
        self.closed_trades = []
        self.equity_curve = []
        self.current_capital = INITIAL_CAPITAL
        
        successful_trades = 0
        failed_trades = 0
        
        for _, signal in signals.iterrows():
            try:
                # Open position
                position = self._open_position(signal, strategy_type)
                
                if position is not None:
                    # Calculate exit date
                    exit_date = signal['date'] + timedelta(days=holding_days)
                    
                    # Close position
                    trade_record = self._close_position(position, exit_date)
                    
                    if trade_record is not None:
                        self.closed_trades.append(trade_record)
                        self.current_capital += trade_record['net_pnl']
                        successful_trades += 1
                    else:
                        failed_trades += 1
                else:
                    failed_trades += 1
                    
            except Exception as e:
                print(f"   ⚠️ Error processing signal for {signal['date']}: {e}")
                failed_trades += 1
        
        print(f"   ✅ Executed {successful_trades} trades")
        if failed_trades > 0:
            print(f"   ❌ Failed to execute {failed_trades} trades")
        
        return self._calculate_performance_metrics()
    
    def _open_position(self, signal: pd.Series, strategy_type: str) -> Optional[Position]:
        """Open a new position based on signal"""
        entry_date = signal['date']
        signal_direction = signal.get('signal_direction', 'BULLISH')
        confidence_score = signal.get('confidence_score', 0.6)
        
        # Calculate position size
        contracts = self.strike_selector.calculate_position_size(confidence_score, strategy_type)
        
        if strategy_type == 'call_spread':
            # Get SPX price for the day
            day_options = self.data_manager.get_options_for_date(entry_date)
            if len(day_options) == 0:
                return None
            
            spx_price = day_options['spx_close'].iloc[0]
            
            # Select strikes
            short_option, long_option = self.strike_selector.select_call_spread_strikes(
                entry_date, spx_price, signal_direction
            )
            
            if short_option is None or long_option is None:
                return None
            
            return Position(
                entry_date=entry_date,
                strategy_type=strategy_type,
                signal_direction=signal_direction,
                contracts=contracts,
                short_option=short_option,
                long_option=long_option
            )
        
        else:  # single_option
            # Get SPX price for the day
            day_options = self.data_manager.get_options_for_date(entry_date)
            if len(day_options) == 0:
                return None
            
            spx_price = day_options['spx_close'].iloc[0]
            option_type = 'call' if signal_direction == 'BULLISH' else 'put'
            
            # Select strike
            single_option = self.strike_selector.select_single_option_strike(
                entry_date, spx_price, option_type
            )
            
            if single_option is None:
                return None
            
            return Position(
                entry_date=entry_date,
                strategy_type=strategy_type,
                signal_direction=signal_direction,
                contracts=contracts,
                single_option=single_option
            )
    
    def _close_position(self, position: Position, exit_date: datetime) -> Optional[Dict]:
        """Close a position and calculate exit prices"""
        try:
            if position.strategy_type == 'call_spread':
                exit_prices = self._get_call_spread_exit_prices(position, exit_date)
            else:
                exit_prices = self._get_single_option_exit_prices(position, exit_date)
            
            if exit_prices is None:
                return None
            
            return position.close_position(exit_date, exit_prices)
            
        except Exception as e:
            print(f"   ⚠️ Error closing position: {e}")
            return None
    
    def _get_call_spread_exit_prices(self, position: Position, exit_date: datetime) -> Optional[Dict]:
        """Get exit prices for call spread position"""
        exit_options = self.data_manager.get_options_for_date(exit_date)
        
        if len(exit_options) == 0:
            return None
        
        # Find matching options for exit
        short_exit = exit_options[exit_options['Strike'] == position.short_option['Strike']]
        long_exit = exit_options[exit_options['Strike'] == position.long_option['Strike']]
        
        if len(short_exit) == 0 or len(long_exit) == 0:
            return None
        
        return {
            'short': short_exit['price'].iloc[0],
            'long': long_exit['price'].iloc[0]
        }
    
    def _get_single_option_exit_prices(self, position: Position, exit_date: datetime) -> Optional[Dict]:
        """Get exit prices for single option position"""
        option_type = 'c' if position.signal_direction in ['BULLISH', 'CALL'] else 'p'
        exit_options = self.data_manager.get_options_for_date(exit_date, option_type)
        
        if len(exit_options) == 0:
            return None
        
        # Find matching option for exit
        option_exit = exit_options[exit_options['Strike'] == position.single_option['Strike']]
        
        if len(option_exit) == 0:
            return None
        
        return {
            'option': option_exit['price'].iloc[0]
        }
    
    def _calculate_performance_metrics(self) -> Dict:
        """Calculate comprehensive performance metrics"""
        if not self.closed_trades:
            return {'error': 'No completed trades'}
        
        trades_df = pd.DataFrame(self.closed_trades)
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['net_pnl'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # P&L metrics
        total_pnl = trades_df['net_pnl'].sum()
        total_return = (total_pnl / INITIAL_CAPITAL) * 100
        avg_win = trades_df[trades_df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['net_pnl'] < 0]['net_pnl'].mean() if losing_trades > 0 else 0
        
        # Risk metrics
        trades_df['cumulative_pnl'] = trades_df['net_pnl'].cumsum()
        trades_df['equity'] = INITIAL_CAPITAL + trades_df['cumulative_pnl']
        
        peak = trades_df['equity'].expanding().max()
        drawdown = (trades_df['equity'] - peak) / peak
        max_drawdown = drawdown.min() * 100
        
        # Profit factor
        gross_profit = trades_df[trades_df['net_pnl'] > 0]['net_pnl'].sum()
        gross_loss = abs(trades_df[trades_df['net_pnl'] < 0]['net_pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate * 100,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'final_capital': INITIAL_CAPITAL + total_pnl,
            'trades_df': trades_df
        }
