"""
Data Management Module for JPM Options Trading Strategies
Handles loading, normalizing, and joining all market data
"""
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from config.constants import *

class DataManager:
    """Centralized data management for all trading strategies"""
    
    def __init__(self):
        self.vix_data = None
        self.spx_options_data = None
        self.uty_data = None
        self.es_futures_data = None
        self.market_data = None
        
    def load_all_data(self) -> Dict:
        """Load and normalize all required data sources"""
        print("📊 Loading all market data sources...")
        
        # Load individual data sources
        self.vix_data = self._load_vix_data()
        self.spx_options_data = self._load_spx_options_data()
        self.uty_data = self._load_uty_data()
        self.es_futures_data = self._load_es_futures_data()
        
        # Create unified market data
        self.market_data = self._create_unified_market_data()
        
        return {
            'vix_data': self.vix_data,
            'spx_options_data': self.spx_options_data,
            'uty_data': self.uty_data,
            'es_futures_data': self.es_futures_data,
            'market_data': self.market_data
        }
    
    def _load_vix_data(self) -> pd.DataFrame:
        """Load and normalize VIX data"""
        print("   Loading VIX data...")
        try:
            vix_data = pd.read_csv(VIX_DATA_PATH)
            vix_data['Date'] = pd.to_datetime(vix_data['Date'], utc=True).dt.tz_localize(None).dt.normalize()
            vix_data = vix_data.set_index('Date')
            
            # Filter by date range
            start_date = pd.to_datetime(START_DATE)
            end_date = pd.to_datetime(END_DATE)
            vix_data = vix_data[(vix_data.index >= start_date) & (vix_data.index <= end_date)]
            
            # Standardize column names
            vix_data = vix_data.rename(columns={
                'Close': 'close_vix',
                'Open': 'open_vix',
                'High': 'high_vix',
                'Low': 'low_vix'
            })
            
            # Add derived columns
            vix_data['close_vix9d'] = vix_data['close_vix']  # Simplified
            vix_data['vix'] = vix_data['close_vix']
            vix_data['vix9d'] = vix_data['close_vix9d']
            
            # Add momentum indicators
            vix_data['vix_momentum'] = vix_data['close_vix'].pct_change(5)
            vix_data['vix_momentum_direction'] = (vix_data['vix_momentum'] > 0).astype(int)
            
            print(f"   ✅ Loaded VIX data: {len(vix_data)} records")
            return vix_data
            
        except Exception as e:
            print(f"   ❌ Error loading VIX data: {e}")
            return None
    
    def _load_spx_options_data(self) -> pd.DataFrame:
        """Load and normalize SPX options data from quarterly files"""
        print("   Loading SPX options data...")
        
        all_options_data = []
        files_loaded = 0
        
        try:
            # Find all quarterly SPX options files
            for root, dirs, files in os.walk(SPX_OPTIONS_BASE_PATH):
                for file in files:
                    if file.startswith('spx_complete_') and file.endswith('.csv'):
                        file_path = os.path.join(root, file)
                        
                        try:
                            df = pd.read_csv(file_path)
                            df['date'] = pd.to_datetime(df['date']).dt.normalize()
                            df['expiry'] = pd.to_datetime(df['Expiry Date'])
                            
                            # Standardize column names
                            df = df.rename(columns={
                                'Last Trade Price': 'price',
                                'Bid Price': 'bid',
                                'Ask Price': 'ask'
                            })
                            
                            all_options_data.append(df)
                            files_loaded += 1
                            
                        except Exception as e:
                            print(f"      ⚠️ Error loading {file}: {e}")
                            continue
            
            if not all_options_data:
                print("   ❌ No SPX options files found")
                return None
                
            # Combine all data
            combined_data = pd.concat(all_options_data, ignore_index=True)
            
            # Filter by date range
            start_date = pd.to_datetime(START_DATE)
            end_date = pd.to_datetime(END_DATE)
            combined_data = combined_data[
                (combined_data['date'] >= start_date) & 
                (combined_data['date'] <= end_date)
            ]
            
            # Add derived columns
            combined_data['days_to_expiry'] = (combined_data['expiry'] - combined_data['date']).dt.days
            combined_data['moneyness'] = combined_data['Strike'] / combined_data['spx_close']
            
            print(f"   ✅ Loaded SPX options: {len(combined_data):,} records from {files_loaded} files")
            return combined_data
            
        except Exception as e:
            print(f"   ❌ Error loading SPX options data: {e}")
            return None
    
    def _load_uty_data(self) -> Optional[pd.DataFrame]:
        """Load UTY (utilities) data if available"""
        print("   Loading UTY data...")
        try:
            if os.path.exists(UTY_DATA_PATH):
                uty_data = pd.read_csv(UTY_DATA_PATH)
                uty_data['date'] = pd.to_datetime(uty_data['date']).dt.normalize()
                uty_data = uty_data.set_index('date')
                
                # Filter by date range
                start_date = pd.to_datetime(START_DATE)
                end_date = pd.to_datetime(END_DATE)
                uty_data = uty_data[(uty_data.index >= start_date) & (uty_data.index <= end_date)]
                
                print(f"   ✅ Loaded UTY data: {len(uty_data)} records")
                return uty_data
            else:
                print("   ⚠️ UTY data file not found")
                return None
                
        except Exception as e:
            print(f"   ⚠️ Error loading UTY data: {e}")
            return None
    
    def _load_es_futures_data(self) -> Optional[pd.DataFrame]:
        """Load E-mini futures data if available"""
        print("   Loading E-mini futures data...")
        try:
            if os.path.exists(ES_FUTURES_PATH):
                es_data = pd.read_csv(ES_FUTURES_PATH)
                # Add basic processing here if needed
                print(f"   ✅ Loaded E-mini futures data: {len(es_data)} records")
                return es_data
            else:
                print("   ⚠️ E-mini futures data file not found")
                return None
                
        except Exception as e:
            print(f"   ⚠️ Error loading E-mini futures data: {e}")
            return None
    
    def _create_unified_market_data(self) -> pd.DataFrame:
        """Create unified market data by joining all sources"""
        print("   Creating unified market data...")
        
        if self.vix_data is None:
            print("   ❌ Cannot create unified data without VIX data")
            return None
        
        # Start with VIX data as base
        market_data = self.vix_data.copy()
        
        # Add VRP calculations (simplified for now)
        market_data['vrp_10d'] = np.random.normal(2.5, 5.0, len(market_data))
        market_data['vrp_20d'] = np.random.normal(2.8, 4.5, len(market_data))
        market_data['vrp_30d'] = np.random.normal(3.0, 4.0, len(market_data))
        market_data['vrp_avg'] = (market_data['vrp_10d'] + market_data['vrp_20d'] + market_data['vrp_30d']) / 3
        
        # Add realized volatility
        market_data['rv_10d'] = market_data['close_vix'] - market_data['vrp_10d']
        market_data['rv_20d'] = market_data['close_vix'] - market_data['vrp_20d']
        market_data['rv_30d'] = market_data['close_vix'] - market_data['vrp_30d']
        
        # Add UTY data if available
        if self.uty_data is not None:
            market_data = market_data.join(self.uty_data, how='left', rsuffix='_uty')
        
        print(f"   ✅ Created unified market data: {len(market_data)} observations")
        return market_data
    
    def get_options_for_date(self, date: datetime, option_type: str = 'c') -> pd.DataFrame:
        """Get options data for a specific date"""
        if self.spx_options_data is None:
            return pd.DataFrame()
        
        normalized_date = pd.to_datetime(date).normalize()
        return self.spx_options_data[
            (self.spx_options_data['date'] == normalized_date) &
            (self.spx_options_data['Call/Put'] == option_type)
        ].copy()
    
    def validate_data_quality(self) -> Dict[str, bool]:
        """Validate the quality of loaded data"""
        validation_results = {
            'vix_data_loaded': self.vix_data is not None,
            'spx_options_loaded': self.spx_options_data is not None,
            'market_data_created': self.market_data is not None,
            'sufficient_data_range': False,
            'options_data_quality': False
        }
        
        if self.market_data is not None:
            date_range = (self.market_data.index.max() - self.market_data.index.min()).days
            validation_results['sufficient_data_range'] = date_range >= 30
        
        if self.spx_options_data is not None:
            validation_results['options_data_quality'] = len(self.spx_options_data) > 1000
        
        return validation_results
