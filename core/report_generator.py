"""
Report Generator Module for Options Trading Strategies
Handles PDF generation, CSV exports, and chart creation
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from datetime import datetime
import os
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

from config.constants import *

class ReportGenerator:
    """Comprehensive report generation for trading strategies"""
    
    def __init__(self, strategy_name: str, performance_metrics: Dict, trades_df: pd.DataFrame):
        self.strategy_name = strategy_name
        self.performance_metrics = performance_metrics
        self.trades_df = trades_df
        self.timestamp = datetime.now().strftime(TIMESTAMP_FORMAT)
        
        # Ensure directories exist
        os.makedirs(REPORTS_DIR, exist_ok=True)
        os.makedirs(TRADES_DIR, exist_ok=True)
        os.makedirs(CHARTS_DIR, exist_ok=True)
    
    def generate_comprehensive_report(self) -> Dict[str, str]:
        """Generate all report components"""
        print(f"📄 Generating comprehensive report for {self.strategy_name}...")
        
        report_files = {}
        
        # Generate CSV trade export
        csv_path = self._export_trades_csv()
        if csv_path:
            report_files['trades_csv'] = csv_path
        
        # Generate charts
        chart_paths = self._generate_charts()
        report_files.update(chart_paths)
        
        # Generate PDF report
        pdf_path = self._generate_pdf_report(chart_paths)
        if pdf_path:
            report_files['pdf_report'] = pdf_path
        
        print(f"   ✅ Report generation complete")
        return report_files
    
    def _export_trades_csv(self) -> Optional[str]:
        """Export trades to CSV file"""
        try:
            filename = f"{self.strategy_name.lower().replace(' ', '_')}_trades_{self.timestamp}.csv"
            filepath = os.path.join(TRADES_DIR, filename)
            
            # Prepare trades data for export
            export_df = self.trades_df.copy()
            
            # Format dates
            date_columns = ['entry_date', 'exit_date']
            for col in date_columns:
                if col in export_df.columns:
                    export_df[col] = export_df[col].dt.strftime(DATE_FORMAT)
            
            # Round numeric columns
            numeric_columns = ['net_pnl', 'commission']
            for col in numeric_columns:
                if col in export_df.columns:
                    export_df[col] = export_df[col].round(2)
            
            export_df.to_csv(filepath, index=False)
            print(f"   ✅ Exported trades to: {filename}")
            return filepath
            
        except Exception as e:
            print(f"   ❌ Error exporting trades CSV: {e}")
            return None
    
    def _generate_charts(self) -> Dict[str, str]:
        """Generate performance charts"""
        chart_paths = {}
        
        try:
            # Set style
            plt.style.use('seaborn-v0_8')
            
            # Equity curve chart
            equity_path = self._create_equity_curve_chart()
            if equity_path:
                chart_paths['equity_curve'] = equity_path
            
            # P&L distribution chart
            pnl_dist_path = self._create_pnl_distribution_chart()
            if pnl_dist_path:
                chart_paths['pnl_distribution'] = pnl_dist_path
            
            # Monthly performance chart
            monthly_path = self._create_monthly_performance_chart()
            if monthly_path:
                chart_paths['monthly_performance'] = monthly_path
            
        except Exception as e:
            print(f"   ⚠️ Error generating charts: {e}")
        
        return chart_paths
    
    def _create_equity_curve_chart(self) -> Optional[str]:
        """Create equity curve chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            if 'equity' in self.trades_df.columns:
                ax.plot(range(len(self.trades_df)), self.trades_df['equity'], 
                       linewidth=2, color='blue', label='Portfolio Value')
                ax.axhline(y=INITIAL_CAPITAL, color='red', linestyle='--', 
                          alpha=0.7, label='Initial Capital')
                
                ax.set_title(f'{self.strategy_name} - Equity Curve', fontsize=14, fontweight='bold')
                ax.set_xlabel('Trade Number')
                ax.set_ylabel('Portfolio Value ($)')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # Format y-axis as currency
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            filename = f"equity_curve_{self.timestamp}.png"
            filepath = os.path.join(CHARTS_DIR, filename)
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            return filepath
            
        except Exception as e:
            print(f"   ⚠️ Error creating equity curve: {e}")
            return None
    
    def _create_pnl_distribution_chart(self) -> Optional[str]:
        """Create P&L distribution chart"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # Histogram
            ax1.hist(self.trades_df['net_pnl'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Break-even')
            ax1.set_title('P&L Distribution', fontweight='bold')
            ax1.set_xlabel('Net P&L ($)')
            ax1.set_ylabel('Frequency')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # Box plot
            ax2.boxplot(self.trades_df['net_pnl'], vert=True)
            ax2.set_title('P&L Box Plot', fontweight='bold')
            ax2.set_ylabel('Net P&L ($)')
            ax2.grid(True, alpha=0.3)
            
            filename = f"pnl_distribution_{self.timestamp}.png"
            filepath = os.path.join(CHARTS_DIR, filename)
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            return filepath
            
        except Exception as e:
            print(f"   ⚠️ Error creating P&L distribution: {e}")
            return None
    
    def _create_monthly_performance_chart(self) -> Optional[str]:
        """Create monthly performance chart"""
        try:
            if 'entry_date' not in self.trades_df.columns:
                return None
            
            # Group by month
            monthly_pnl = self.trades_df.groupby(
                self.trades_df['entry_date'].dt.to_period('M')
            )['net_pnl'].sum()
            
            if len(monthly_pnl) == 0:
                return None
            
            fig, ax = plt.subplots(figsize=(12, 6))
            
            colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
            bars = ax.bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors, alpha=0.7)
            
            ax.set_title('Monthly Performance', fontweight='bold')
            ax.set_xlabel('Month')
            ax.set_ylabel('Net P&L ($)')
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            ax.grid(True, alpha=0.3)
            
            # Set x-axis labels
            ax.set_xticks(range(len(monthly_pnl)))
            ax.set_xticklabels([str(period) for period in monthly_pnl.index], rotation=45)
            
            filename = f"monthly_performance_{self.timestamp}.png"
            filepath = os.path.join(CHARTS_DIR, filename)
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            return filepath
            
        except Exception as e:
            print(f"   ⚠️ Error creating monthly performance: {e}")
            return None
    
    def _generate_pdf_report(self, chart_paths: Dict[str, str]) -> Optional[str]:
        """Generate comprehensive PDF report"""
        try:
            filename = f"{self.strategy_name.lower().replace(' ', '_')}_report_{self.timestamp}.pdf"
            filepath = os.path.join(REPORTS_DIR, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=PDF_TITLE_FONT_SIZE,
                spaceAfter=30,
                alignment=1  # Center
            )
            story.append(Paragraph(f"{self.strategy_name} - Performance Report", title_style))
            story.append(Spacer(1, 20))
            
            # Executive Summary
            story.append(Paragraph("Executive Summary", styles['Heading2']))
            summary_data = [
                ["Metric", "Value"],
                ["Total Return", f"{self.performance_metrics.get('total_return', 0):.1f}%"],
                ["Win Rate", f"{self.performance_metrics.get('win_rate', 0):.1f}%"],
                ["Total Trades", f"{self.performance_metrics.get('total_trades', 0)}"],
                ["Max Drawdown", f"{self.performance_metrics.get('max_drawdown', 0):.1f}%"],
                ["Profit Factor", f"{self.performance_metrics.get('profit_factor', 0):.2f}"],
                ["Final Capital", f"${self.performance_metrics.get('final_capital', 0):,.0f}"]
            ]
            
            summary_table = Table(summary_data)
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(summary_table)
            story.append(Spacer(1, 20))
            
            # Performance Details
            story.append(Paragraph("Detailed Performance Metrics", styles['Heading2']))
            
            detailed_data = [
                ["Metric", "Value"],
                ["Winning Trades", f"{self.performance_metrics.get('winning_trades', 0)}"],
                ["Losing Trades", f"{self.performance_metrics.get('losing_trades', 0)}"],
                ["Average Win", f"${self.performance_metrics.get('avg_win', 0):,.0f}"],
                ["Average Loss", f"${self.performance_metrics.get('avg_loss', 0):,.0f}"],
                ["Largest Win", f"${self.performance_metrics.get('largest_win', 0):,.0f}"],
                ["Largest Loss", f"${self.performance_metrics.get('largest_loss', 0):,.0f}"],
                ["Sharpe Ratio", f"{self.performance_metrics.get('sharpe_ratio', 0):.2f}"]
            ]
            
            detailed_table = Table(detailed_data)
            detailed_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(detailed_table)
            story.append(PageBreak())
            
            # Recent Trades
            story.append(Paragraph("Recent Trades (Last 10)", styles['Heading2']))
            
            if len(self.trades_df) > 0:
                recent_trades = self.trades_df.tail(10)
                trade_data = [["Date", "Direction", "Contracts", "P&L", "Win/Loss"]]
                
                for _, trade in recent_trades.iterrows():
                    trade_data.append([
                        trade.get('entry_date', '').strftime('%Y-%m-%d') if hasattr(trade.get('entry_date', ''), 'strftime') else str(trade.get('entry_date', '')),
                        trade.get('signal_direction', ''),
                        str(trade.get('contracts', '')),
                        f"${trade.get('net_pnl', 0):,.0f}",
                        "Win" if trade.get('net_pnl', 0) > 0 else "Loss"
                    ])
                
                trades_table = Table(trade_data)
                trades_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(trades_table)
            
            # Build PDF
            doc.build(story)
            print(f"   ✅ Generated PDF report: {filename}")
            return filepath
            
        except Exception as e:
            print(f"   ❌ Error generating PDF report: {e}")
            return None
