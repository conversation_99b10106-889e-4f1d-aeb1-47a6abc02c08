"""
Strike Selection Module for Options Trading Strategies
Handles intelligent strike selection for different option strategies
"""
import pandas as pd
import numpy as np
from typing import Tuple, Optional, Dict, List
from datetime import datetime

from config.constants import *

class StrikeSelector:
    """Intelligent strike selection for options strategies"""
    
    def __init__(self, options_data: pd.DataFrame):
        self.options_data = options_data
    
    def select_call_spread_strikes(self, 
                                 date: datetime, 
                                 spx_price: float, 
                                 signal_direction: str) -> <PERSON>ple[Optional[pd.Series], Optional[pd.Series]]:
        """
        Select optimal strikes for call spread strategy
        
        Args:
            date: Trading date
            spx_price: Current SPX price
            signal_direction: 'BULLISH' or 'BEARISH'
            
        Returns:
            Tuple of (short_option, long_option) or (None, None) if no suitable options
        """
        # Get options for this date
        day_options = self._get_filtered_options(date, spx_price)
        
        if len(day_options) == 0:
            return None, None
        
        # Define strike ranges based on signal direction
        if signal_direction == 'BULLISH':
            short_strike_min = spx_price * BULLISH_SHORT_STRIKE_MIN
            short_strike_max = spx_price * BULLISH_SHORT_STRIKE_MAX
            long_strike_min = spx_price * BULLISH_LONG_STRIKE_MIN
            long_strike_max = spx_price * BULLISH_LONG_STRIKE_MAX
        else:  # BEARISH
            short_strike_min = spx_price * BEARISH_SHORT_STRIKE_MIN
            short_strike_max = spx_price * BEARISH_SHORT_STRIKE_MAX
            long_strike_min = spx_price * BEARISH_LONG_STRIKE_MIN
            long_strike_max = spx_price * BEARISH_LONG_STRIKE_MAX
        
        # Find short leg candidates
        short_candidates = day_options[
            (day_options['Strike'] >= short_strike_min) &
            (day_options['Strike'] <= short_strike_max)
        ]
        
        if len(short_candidates) == 0:
            return None, None
        
        # Select best short option (highest premium)
        short_option = short_candidates.loc[short_candidates['price'].idxmax()]
        
        # Find long leg candidates
        long_candidates = day_options[
            (day_options['Strike'] >= long_strike_min) &
            (day_options['Strike'] <= long_strike_max) &
            (day_options['Strike'] > short_option['Strike']) &
            (day_options['Strike'] - short_option['Strike'] <= CALL_SPREAD_MAX_WIDTH)
        ]
        
        if len(long_candidates) == 0:
            return None, None
        
        # Select best long option (lowest premium for given strike distance)
        long_option = long_candidates.loc[long_candidates['price'].idxmin()]
        
        # Validate spread criteria
        if not self._validate_call_spread(short_option, long_option):
            return None, None
        
        return short_option, long_option
    
    def select_single_option_strike(self, 
                                  date: datetime, 
                                  spx_price: float, 
                                  option_type: str,
                                  target_delta: float = None,
                                  target_dte: int = 30) -> Optional[pd.Series]:
        """
        Select optimal strike for single option strategy
        
        Args:
            date: Trading date
            spx_price: Current SPX price
            option_type: 'call' or 'put'
            target_delta: Target delta (optional)
            target_dte: Target days to expiration
            
        Returns:
            Selected option or None if no suitable option found
        """
        # Get options for this date
        option_char = 'c' if option_type.lower() == 'call' else 'p'
        day_options = self._get_filtered_options(date, spx_price, option_char, target_dte)
        
        if len(day_options) == 0:
            return None
        
        # Apply delta filter if specified
        if target_delta is not None and 'Delta' in day_options.columns:
            delta_tolerance = 0.1
            day_options = day_options[
                abs(day_options['Delta'] - target_delta) <= delta_tolerance
            ]
        
        if len(day_options) == 0:
            return None
        
        # Select option with best liquidity (highest volume/open interest)
        if 'Volume' in day_options.columns and 'Open Interest' in day_options.columns:
            day_options['liquidity_score'] = (
                day_options['Volume'].fillna(0) + 
                day_options['Open Interest'].fillna(0) * 0.1
            )
            return day_options.loc[day_options['liquidity_score'].idxmax()]
        else:
            # Fallback to mid-price selection
            return day_options.loc[day_options['price'].idxmax()]
    
    def _get_filtered_options(self, 
                            date: datetime, 
                            spx_price: float, 
                            option_type: str = 'c',
                            target_dte: int = None) -> pd.DataFrame:
        """Get filtered options for a specific date with quality filters"""
        normalized_date = pd.to_datetime(date).normalize()
        
        # Get options for this date
        day_options = self.options_data[
            (self.options_data['date'] == normalized_date) &
            (self.options_data['Call/Put'] == option_type)
        ].copy()
        
        if len(day_options) == 0:
            return pd.DataFrame()
        
        # Calculate days to expiry if not already present
        if 'days_to_expiry' not in day_options.columns:
            day_options['days_to_expiry'] = (day_options['expiry'] - normalized_date).dt.days
        
        # Filter by expiration (default to call spread parameters)
        min_dte = target_dte - 5 if target_dte else CALL_SPREAD_MIN_DTE
        max_dte = target_dte + 5 if target_dte else CALL_SPREAD_MAX_DTE
        
        day_options = day_options[
            (day_options['days_to_expiry'] >= min_dte) &
            (day_options['days_to_expiry'] <= max_dte)
        ]
        
        # Filter for strikes that are multiples of STRIKE_MULTIPLE
        day_options = day_options[day_options['Strike'] % STRIKE_MULTIPLE == 0]
        
        # Quality filters
        day_options = day_options[
            (day_options['price'] >= MIN_OPTION_PRICE) &
            (day_options['price'] <= MAX_OPTION_PRICE)
        ]
        
        # Bid-ask spread filter if available
        if 'bid' in day_options.columns and 'ask' in day_options.columns:
            day_options = day_options[
                (day_options['ask'] - day_options['bid']) <= MAX_BID_ASK_SPREAD
            ]
        
        # Volume filter if available
        if 'Volume' in day_options.columns:
            day_options = day_options[
                day_options['Volume'].fillna(0) >= MIN_VOLUME
            ]
        
        return day_options
    
    def _validate_call_spread(self, short_option: pd.Series, long_option: pd.Series) -> bool:
        """Validate call spread meets quality criteria"""
        # Calculate spread metrics
        short_premium = short_option['price']
        long_premium = long_option['price']
        net_credit = short_premium - long_premium
        spread_width = long_option['Strike'] - short_option['Strike']
        
        # Validation checks
        checks = [
            net_credit > 0,  # Must be a credit spread
            spread_width > 0,  # Long strike must be higher
            spread_width <= CALL_SPREAD_MAX_WIDTH,  # Maximum spread width
            net_credit >= (spread_width * CALL_SPREAD_MIN_CREDIT_RATIO),  # Minimum credit ratio
            short_premium > MIN_OPTION_PRICE,  # Minimum option prices
            long_premium > MIN_OPTION_PRICE
        ]
        
        return all(checks)
    
    def calculate_position_size(self, 
                              confidence_score: float, 
                              strategy_type: str = 'call_spread') -> int:
        """
        Calculate optimal position size based on confidence and strategy
        
        Args:
            confidence_score: Signal confidence (0-1)
            strategy_type: Type of strategy for sizing rules
            
        Returns:
            Number of contracts to trade
        """
        if strategy_type == 'call_spread':
            # Call spread sizing: 8-20 contracts based on confidence
            base_contracts = max(MIN_CONTRACTS, 
                               min(MAX_CONTRACTS, 
                                   int(MIN_CONTRACTS + confidence_score * (MAX_CONTRACTS - MIN_CONTRACTS))))
        else:
            # Single option sizing
            base_contracts = max(MIN_CONTRACTS, 
                               min(MAX_CONTRACTS, 
                                   int(BASE_POSITION_SIZE + confidence_score * 5)))
        
        return base_contracts
    
    def get_strike_statistics(self, date: datetime) -> Dict:
        """Get statistics about available strikes for a given date"""
        normalized_date = pd.to_datetime(date).normalize()
        day_options = self.options_data[self.options_data['date'] == normalized_date]
        
        if len(day_options) == 0:
            return {}
        
        stats = {
            'total_options': len(day_options),
            'call_options': len(day_options[day_options['Call/Put'] == 'c']),
            'put_options': len(day_options[day_options['Call/Put'] == 'p']),
            'strike_range': (day_options['Strike'].min(), day_options['Strike'].max()),
            'avg_price': day_options['price'].mean(),
            'spx_price': day_options['spx_close'].iloc[0] if 'spx_close' in day_options.columns else None
        }
        
        return stats
