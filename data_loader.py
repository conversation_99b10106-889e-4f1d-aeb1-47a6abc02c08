"""
Simple Data Loader for Pure VIX Options Strategy
Loads options and market data with minimal complexity
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, List
import glob

from constants import *
from config import *

class SimpleDataLoader:
    """
    Simple data loader focused on essential data only
    Removes complex regime data processing
    """
    
    def __init__(self):
        """Initialize simple data loader"""
        
        self.options_data = None
        self.vix_data = None
        self.emini_data = None
        
        print("✅ Simple Data Loader initialized")
        print("🎯 Focus: Essential data only (options, VIX)")
        
    def load_all_data(self) -> bool:
        """
        Load all required data for pure VIX strategy
        
        Returns:
            bool: True if data loaded successfully
        """
        
        print("📊 Loading all market data...")
        
        try:
            # Load options data
            self.options_data = self.load_options_data()
            if self.options_data is None:
                print("❌ Failed to load options data")
                return False
            
            # Load VIX data
            self.vix_data = self.load_vix_data()
            if self.vix_data is None:
                print("❌ Failed to load VIX data")
                return False
            
            # Load E-mini data (optional for drift calculation)
            self.emini_data = self.load_emini_data()
            
            print("✅ All market data loaded successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error loading market data: {e}")
            return False
    
    def load_options_data(self) -> Optional[pd.DataFrame]:
        """
        Load SPX options data
        
        Returns:
            DataFrame with options data or None if failed
        """
        
        print("📈 Loading SPX options data...")
        
        try:
            # Check for existing combined data
            combined_file = os.path.join(DATA_DIR, 'SPX_COMPLETE_COMBINED.csv')
            
            if os.path.exists(combined_file):
                print(f"📁 Loading existing combined data: {combined_file}")
                data = pd.read_csv(combined_file)
                data['date'] = pd.to_datetime(data['date'])
                
                print(f"✅ Loaded {len(data):,} options records")
                print(f"📅 Date range: {data['date'].min()} to {data['date'].max()}")
                
                return data
            
            # Load from individual quarterly files
            print("📁 Loading from quarterly files...")
            all_data = []
            
            # Look for quarterly data files
            data_pattern = os.path.join(OPTIONS_DATA_DIR, '**/spx_complete_*.csv')
            data_files = glob.glob(data_pattern, recursive=True)
            
            if not data_files:
                print("⚠️ No options data files found")
                return None
            
            print(f"📁 Found {len(data_files)} data files")
            
            for file_path in sorted(data_files):
                try:
                    print(f"Loading: {os.path.basename(file_path)}")
                    df = pd.read_csv(file_path)
                    df['date'] = pd.to_datetime(df['date'])
                    all_data.append(df)
                    
                except Exception as e:
                    print(f"⚠️ Error loading {file_path}: {e}")
                    continue
            
            if not all_data:
                print("❌ No valid options data loaded")
                return None
            
            # Combine all data
            combined_data = pd.concat(all_data, ignore_index=True)
            combined_data = combined_data.sort_values('date').reset_index(drop=True)
            
            # Save combined data for future use
            os.makedirs(DATA_DIR, exist_ok=True)
            combined_data.to_csv(combined_file, index=False)
            
            print(f"✅ Combined and saved {len(combined_data):,} options records")
            print(f"📅 Date range: {combined_data['date'].min()} to {combined_data['date'].max()}")
            
            return combined_data
            
        except Exception as e:
            print(f"❌ Error loading options data: {e}")
            return None
    
    def load_vix_data(self) -> Optional[pd.DataFrame]:
        """
        Load VIX data with fallback to synthetic data
        
        Returns:
            DataFrame with VIX data or None if failed
        """
        
        print("📊 Loading VIX data...")
        
        try:
            # Try to load from file
            if os.path.exists(VIX_CONFIG['data_file']):
                print(f"📁 Loading VIX from: {VIX_CONFIG['data_file']}")
                
                vix_data = pd.read_csv(VIX_CONFIG['data_file'])
                
                # Handle different file formats
                if 'Date' in vix_data.columns:
                    vix_data['date'] = pd.to_datetime(vix_data['Date'])
                    vix_data = vix_data.rename(columns={'Close': 'VIX'})
                else:
                    vix_data['date'] = pd.to_datetime(vix_data.iloc[:, 0])
                    vix_data = vix_data.rename(columns={vix_data.columns[1]: 'VIX'})
                
                vix_data = vix_data[['date', 'VIX']].copy()
                
            elif VIX_CONFIG['fallback_enabled']:
                print("⚠️ VIX file not found, creating synthetic data")
                vix_data = self._create_synthetic_vix_data()
                
            else:
                print("❌ VIX file not found and fallback disabled")
                return None
            
            # Calculate VIX derivatives for pure VIX strategy
            vix_data = vix_data.sort_values('date').reset_index(drop=True)
            
            # VIX9D for momentum calculation
            vix_data['VIX9D'] = vix_data['VIX'].rolling(
                VIX_MOMENTUM_LOOKBACK, min_periods=1
            ).mean()
            
            # VIX momentum (key for pure VIX strategy)
            vix_data['VIX_momentum'] = vix_data['VIX'] - vix_data['VIX9D']
            
            # VIX volatility
            vix_data['VIX_volatility'] = vix_data['VIX'].rolling(20, min_periods=1).std()
            
            print(f"✅ VIX data processed: {len(vix_data)} observations")
            print(f"📊 VIX range: {vix_data['VIX'].min():.1f} to {vix_data['VIX'].max():.1f}")
            print(f"📈 Average VIX: {vix_data['VIX'].mean():.1f}")
            
            return vix_data
            
        except Exception as e:
            print(f"❌ Error loading VIX data: {e}")
            return None
    
    def _create_synthetic_vix_data(self) -> pd.DataFrame:
        """Create synthetic VIX data for testing"""
        
        print("🔧 Creating synthetic VIX data...")
        
        # Generate dates
        start_date = datetime.strptime(BACKTEST_CONFIG['start_date'], '%Y-%m-%d')
        end_date = datetime.strptime(BACKTEST_CONFIG['end_date'], '%Y-%m-%d')
        dates = pd.date_range(start_date, end_date, freq='D')
        
        # Set random seed for reproducibility
        np.random.seed(VIX_CONFIG['synthetic_data_seed'])
        
        # Generate realistic VIX with mean reversion
        vix_values = []
        base_vix = VIX_CONFIG['base_level']
        volatility = VIX_CONFIG['volatility']
        mean_reversion_speed = VIX_CONFIG['mean_reversion_speed']
        
        for i, date in enumerate(dates):
            if i == 0:
                vix = base_vix
            else:
                prev_vix = vix_values[-1]
                
                # Mean reversion component
                mean_reversion = mean_reversion_speed * (base_vix - prev_vix)
                
                # Random shock with volatility clustering
                shock = np.random.normal(0, volatility)
                
                # Update VIX
                vix = prev_vix + mean_reversion + shock
                
                # Keep within realistic bounds
                vix = np.clip(vix, 8, 50)
            
            vix_values.append(vix)
        
        synthetic_data = pd.DataFrame({
            'date': dates,
            'VIX': vix_values
        })
        
        print(f"✅ Created synthetic VIX data: {len(synthetic_data)} observations")
        
        return synthetic_data
    
    def load_emini_data(self) -> Optional[pd.DataFrame]:
        """
        Load E-mini futures data (optional)
        
        Returns:
            DataFrame with E-mini data or None if not available
        """
        
        print("📈 Loading E-mini futures data...")
        
        try:
            if not os.path.exists(EMINI_CONFIG['data_file']):
                print("⚠️ E-mini data file not found, skipping")
                return None
            
            # Load E-mini data
            emini_data = pd.read_csv(EMINI_CONFIG['data_file'])
            
            # Process datetime
            emini_data['datetime'] = pd.to_datetime(
                emini_data['date'].astype(str) + ' ' + emini_data['time'].astype(str)
            )
            emini_data['date'] = pd.to_datetime(emini_data['date'])
            
            print(f"✅ E-mini data loaded: {len(emini_data):,} records")
            print(f"📅 Date range: {emini_data['date'].min()} to {emini_data['date'].max()}")
            
            return emini_data
            
        except Exception as e:
            print(f"⚠️ Error loading E-mini data: {e}")
            return None
    
    def get_vix_for_date(self, target_date: datetime) -> Optional[Dict]:
        """
        Get VIX data for specific date
        
        Args:
            target_date: Target date
            
        Returns:
            Dictionary with VIX data or None
        """
        
        if self.vix_data is None:
            return None
        
        # Find closest date
        vix_row = self.vix_data[self.vix_data['date'] <= target_date].tail(1)
        
        if len(vix_row) == 0:
            return None
        
        vix_row = vix_row.iloc[0]
        
        return {
            'date': vix_row['date'],
            'VIX': vix_row['VIX'],
            'VIX9D': vix_row['VIX9D'],
            'VIX_momentum': vix_row['VIX_momentum'],
            'VIX_volatility': vix_row.get('VIX_volatility', 0)
        }
    
    def get_options_for_date(self, target_date: datetime) -> Optional[pd.DataFrame]:
        """
        Get options data for specific date
        
        Args:
            target_date: Target date
            
        Returns:
            DataFrame with options for that date or None
        """
        
        if self.options_data is None:
            return None
        
        # Filter options for target date
        date_options = self.options_data[self.options_data['date'] == target_date].copy()
        
        if len(date_options) == 0:
            return None
        
        return date_options
    
    def validate_data_quality(self) -> Dict:
        """
        Validate data quality for pure VIX strategy
        
        Returns:
            Dictionary with validation results
        """
        
        validation_results = {
            'options_data_valid': False,
            'vix_data_valid': False,
            'data_alignment_valid': False,
            'quality_score': 0.0,
            'issues': []
        }
        
        try:
            # Validate options data
            if self.options_data is not None:
                if len(self.options_data) > MIN_TRADES_FOR_VALIDATION:
                    validation_results['options_data_valid'] = True
                else:
                    validation_results['issues'].append("Insufficient options data")
            else:
                validation_results['issues'].append("No options data loaded")
            
            # Validate VIX data
            if self.vix_data is not None:
                if len(self.vix_data) > MIN_TRADING_DAYS:
                    validation_results['vix_data_valid'] = True
                else:
                    validation_results['issues'].append("Insufficient VIX data")
            else:
                validation_results['issues'].append("No VIX data loaded")
            
            # Check data alignment
            if validation_results['options_data_valid'] and validation_results['vix_data_valid']:
                options_dates = set(self.options_data['date'].dt.date)
                vix_dates = set(self.vix_data['date'].dt.date)
                
                overlap = len(options_dates.intersection(vix_dates))
                total_options_dates = len(options_dates)
                
                if overlap / total_options_dates > 0.8:  # 80% overlap required
                    validation_results['data_alignment_valid'] = True
                else:
                    validation_results['issues'].append("Poor data alignment between options and VIX")
            
            # Calculate overall quality score
            valid_components = sum([
                validation_results['options_data_valid'],
                validation_results['vix_data_valid'],
                validation_results['data_alignment_valid']
            ])
            
            validation_results['quality_score'] = valid_components / 3.0
            
            print(f"📊 Data Quality Score: {validation_results['quality_score']:.2f}")
            
            if validation_results['issues']:
                print("⚠️ Data Quality Issues:")
                for issue in validation_results['issues']:
                    print(f"   • {issue}")
            else:
                print("✅ All data quality checks passed")
            
            return validation_results
            
        except Exception as e:
            validation_results['issues'].append(f"Validation error: {e}")
            print(f"❌ Data validation failed: {e}")
            return validation_results


def main():
    """Test data loader functionality"""
    
    print("🧪 Testing Simple Data Loader")
    print("=" * 50)
    
    # Initialize loader
    loader = SimpleDataLoader()
    
    # Load all data
    success = loader.load_all_data()
    
    if success:
        # Validate data quality
        validation = loader.validate_data_quality()
        
        print(f"\n📊 Data Loading Summary:")
        print(f"Options Data: {'✅' if validation['options_data_valid'] else '❌'}")
        print(f"VIX Data: {'✅' if validation['vix_data_valid'] else '❌'}")
        print(f"Data Alignment: {'✅' if validation['data_alignment_valid'] else '❌'}")
        print(f"Quality Score: {validation['quality_score']:.2f}")
        
        # Test date-specific queries
        test_date = datetime(2023, 6, 15)
        vix_data = loader.get_vix_for_date(test_date)
        options_data = loader.get_options_for_date(test_date)
        
        print(f"\n🧪 Test Query for {test_date.strftime('%Y-%m-%d')}:")
        print(f"VIX Data: {'✅' if vix_data else '❌'}")
        print(f"Options Data: {'✅' if options_data is not None else '❌'}")
        
        if vix_data:
            print(f"VIX Level: {vix_data['VIX']:.2f}")
            print(f"VIX Momentum: {vix_data['VIX_momentum']:.2f}")
    
    else:
        print("❌ Data loading failed")
    
    print("\n✅ Data loader test completed")


if __name__ == "__main__":
    main()
