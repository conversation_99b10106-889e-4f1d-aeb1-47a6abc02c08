#!/usr/bin/env python3

import pandas as pd
import sys

print("🔍 DEBUGGING EXACT MATCH ISSUE")
print("=" * 50)

# Load the trade data
trades_df = pd.read_csv('trades/call_spread_trades.csv')
first_trade = trades_df.iloc[0]

print(f"📊 FIRST TRADE DETAILS:")
print(f"   Entry Date: {first_trade['entry_date']}")
print(f"   Short: Strike {first_trade['short_strike']:.0f}, Price ${first_trade['short_entry_price']:.2f}")
print(f"   Long:  Strike {first_trade['long_strike']:.0f}, Price ${first_trade['long_entry_price']:.2f}")

# Load options data
sys.path.append('.')
from final_strategy_clean import FinalRealDataStrategy

strategy = FinalRealDataStrategy()
market_data = strategy.load_market_data_with_real_vrp()
options_data = strategy.spx_options_data

options_data['days_to_expiry'] = (options_data['expiry_date'] - options_data['date']).dt.days
options_data['mid_price'] = (options_data['Bid Price'] + options_data['Ask Price']) / 2

# Check ALL options for the entry date and strikes
entry_date = pd.to_datetime(first_trade['entry_date'])

print(f"\n🔍 ALL SHORT STRIKE {first_trade['short_strike']:.0f} OPTIONS on {entry_date.date()}:")
short_all = options_data[
    (options_data['date'] == entry_date) &
    (options_data['Strike'] == first_trade['short_strike']) &
    (options_data['Call/Put'] == 'c')
].copy()

print(f"   Found {len(short_all)} total options")
for _, opt in short_all.iterrows():
    dte = opt['days_to_expiry']
    mid_price = opt['mid_price']
    price_diff = abs(mid_price - first_trade['short_entry_price'])
    
    # Mark exact matches and DTE range
    exact_marker = "🎯" if price_diff < 0.01 else "  "
    dte_marker = "✅" if 25 <= dte <= 35 else "❌"
    
    print(f"   {exact_marker} {dte_marker} {opt['expiry_date'].strftime('%Y-%m-%d')} ({dte:2d} DTE) - ${mid_price:6.2f} (diff: ${price_diff:6.2f})")

print(f"\n🔍 ALL LONG STRIKE {first_trade['long_strike']:.0f} OPTIONS on {entry_date.date()}:")
long_all = options_data[
    (options_data['date'] == entry_date) &
    (options_data['Strike'] == first_trade['long_strike']) &
    (options_data['Call/Put'] == 'c')
].copy()

print(f"   Found {len(long_all)} total options")
for _, opt in long_all.iterrows():
    dte = opt['days_to_expiry']
    mid_price = opt['mid_price']
    price_diff = abs(mid_price - first_trade['long_entry_price'])
    
    # Mark exact matches and DTE range
    exact_marker = "🎯" if price_diff < 0.01 else "  "
    dte_marker = "✅" if 25 <= dte <= 35 else "❌"
    
    print(f"   {exact_marker} {dte_marker} {opt['expiry_date'].strftime('%Y-%m-%d')} ({dte:2d} DTE) - ${mid_price:6.2f} (diff: ${price_diff:6.2f})")

# Check if there are exact matches outside the 25-35 DTE range
short_exact_all = short_all[abs(short_all['mid_price'] - first_trade['short_entry_price']) < 0.01]
long_exact_all = long_all[abs(long_all['mid_price'] - first_trade['long_entry_price']) < 0.01]

print(f"\n🎯 EXACT MATCHES ANALYSIS:")
if len(short_exact_all) > 0:
    print(f"   Short exact matches: {len(short_exact_all)}")
    for _, opt in short_exact_all.iterrows():
        dte = opt['days_to_expiry']
        in_range = "✅ IN RANGE" if 25 <= dte <= 35 else "❌ OUT OF RANGE"
        print(f"      {opt['expiry_date'].strftime('%Y-%m-%d')} ({dte} DTE) - ${opt['mid_price']:.2f} {in_range}")
else:
    print(f"   Short exact matches: 0")

if len(long_exact_all) > 0:
    print(f"   Long exact matches: {len(long_exact_all)}")
    for _, opt in long_exact_all.iterrows():
        dte = opt['days_to_expiry']
        in_range = "✅ IN RANGE" if 25 <= dte <= 35 else "❌ OUT OF RANGE"
        print(f"      {opt['expiry_date'].strftime('%Y-%m-%d')} ({dte} DTE) - ${opt['mid_price']:.2f} {in_range}")
else:
    print(f"   Long exact matches: 0")

print(f"\n🎯 CONCLUSION:")
if len(short_exact_all) > 0 and len(long_exact_all) > 0:
    short_in_range = any(25 <= opt['days_to_expiry'] <= 35 for _, opt in short_exact_all.iterrows())
    long_in_range = any(25 <= opt['days_to_expiry'] <= 35 for _, opt in long_exact_all.iterrows())
    
    if short_in_range and long_in_range:
        print("✅ Strategy is working correctly - exact matches found in 25-35 DTE range")
    else:
        print("⚠️ Strategy found exact matches but outside 25-35 DTE range")
        print("   This suggests the strategy is not actually using 25-35 DTE filtering")
else:
    print("❌ No exact matches found - strategy may have data integrity issues")
    print("   The strategy debug output showed exact matches, but validation cannot find them")
    print("   This suggests a timing issue or different data sources")
