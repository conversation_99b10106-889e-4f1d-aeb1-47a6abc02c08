#!/usr/bin/env python3

import pandas as pd
import sys

print("🔍 DEBUGGING EXACT OPTION AVAILABILITY")
print("=" * 50)

# Load the strategy
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()

print(f"✅ Loaded options data: {len(strategy.spx_options_data):,} records")

# Let's trace through one successful trade to see what happens
# From the output, we know one successful trade was executed
trades_df = pd.read_csv('trades/call_spread_trades.csv')
if len(trades_df) > 0:
    trade = trades_df.iloc[0]
    
    print(f"\n📊 SUCCESSFUL TRADE:")
    print(f"   Entry Date: {trade['entry_date']}")
    print(f"   Exit Date: {trade['exit_date']}")
    print(f"   Short Strike: {trade['short_strike']}, Expiry: {trade['short_expiry']}")
    print(f"   Long Strike: {trade['long_strike']}, Expiry: {trade['long_expiry']}")
    
    # Check if these exact options exist on both dates
    entry_date = pd.to_datetime(trade['entry_date'])
    exit_date = pd.to_datetime(trade['exit_date'])
    short_expiry = pd.to_datetime(trade['short_expiry'])
    long_expiry = pd.to_datetime(trade['long_expiry'])
    
    print(f"\n🔍 CHECKING SHORT LEG AVAILABILITY:")
    
    # Entry date
    entry_short = strategy.spx_options_data[
        (strategy.spx_options_data['date'] == entry_date) &
        (strategy.spx_options_data['Strike'] == trade['short_strike']) &
        (strategy.spx_options_data['expiry_date'] == short_expiry) &
        (strategy.spx_options_data['Call/Put'] == 'c')
    ]
    print(f"   Entry date ({entry_date.date()}): {len(entry_short)} matches")
    if len(entry_short) > 0:
        opt = entry_short.iloc[0]
        print(f"      Price: ${opt['Last Trade Price']:.2f}")
    
    # Exit date
    exit_short = strategy.spx_options_data[
        (strategy.spx_options_data['date'] == exit_date) &
        (strategy.spx_options_data['Strike'] == trade['short_strike']) &
        (strategy.spx_options_data['expiry_date'] == short_expiry) &
        (strategy.spx_options_data['Call/Put'] == 'c')
    ]
    print(f"   Exit date ({exit_date.date()}): {len(exit_short)} matches")
    if len(exit_short) > 0:
        opt = exit_short.iloc[0]
        print(f"      Price: ${opt['Last Trade Price']:.2f}")
    
    print(f"\n🔍 CHECKING LONG LEG AVAILABILITY:")
    
    # Entry date
    entry_long = strategy.spx_options_data[
        (strategy.spx_options_data['date'] == entry_date) &
        (strategy.spx_options_data['Strike'] == trade['long_strike']) &
        (strategy.spx_options_data['expiry_date'] == long_expiry) &
        (strategy.spx_options_data['Call/Put'] == 'c')
    ]
    print(f"   Entry date ({entry_date.date()}): {len(entry_long)} matches")
    if len(entry_long) > 0:
        opt = entry_long.iloc[0]
        print(f"      Price: ${opt['Last Trade Price']:.2f}")
    
    # Exit date
    exit_long = strategy.spx_options_data[
        (strategy.spx_options_data['date'] == exit_date) &
        (strategy.spx_options_data['Strike'] == trade['long_strike']) &
        (strategy.spx_options_data['expiry_date'] == long_expiry) &
        (strategy.spx_options_data['Call/Put'] == 'c')
    ]
    print(f"   Exit date ({exit_date.date()}): {len(exit_long)} matches")
    if len(exit_long) > 0:
        opt = exit_long.iloc[0]
        print(f"      Price: ${opt['Last Trade Price']:.2f}")

# Now let's check a failed case
print(f"\n🔍 CHECKING A FAILED CASE:")
print(f"Let's manually check if options exist for a failed trade...")

# Check what options exist for 2023-06-17 (a failed exit date)
failed_date = pd.to_datetime('2023-06-17')
failed_options = strategy.spx_options_data[
    (strategy.spx_options_data['date'] == failed_date) &
    (strategy.spx_options_data['Call/Put'] == 'c')
]

print(f"\n📊 OPTIONS AVAILABLE ON {failed_date.date()}:")
print(f"   Total call options: {len(failed_options)}")

if len(failed_options) > 0:
    print(f"   Strike range: {failed_options['Strike'].min():.0f} - {failed_options['Strike'].max():.0f}")
    print(f"   Sample strikes: {sorted(failed_options['Strike'].unique())[:20]}")
    
    # Check if 4375 and 4525 strikes exist (from failed debug output)
    strike_4375 = failed_options[failed_options['Strike'] == 4375]
    strike_4525 = failed_options[failed_options['Strike'] == 4525]
    
    print(f"\n   Strike 4375 options: {len(strike_4375)}")
    if len(strike_4375) > 0:
        print(f"      Available expiries: {[d.strftime('%Y-%m-%d') for d in strike_4375['expiry_date'].unique()]}")
    
    print(f"   Strike 4525 options: {len(strike_4525)}")
    if len(strike_4525) > 0:
        print(f"      Available expiries: {[d.strftime('%Y-%m-%d') for d in strike_4525['expiry_date'].unique()]}")
else:
    print(f"   ❌ NO OPTIONS FOUND for {failed_date.date()}")
    
    # Check nearby dates
    print(f"\n   Checking nearby dates:")
    for offset in [-1, 1, -2, 2]:
        check_date = failed_date + pd.Timedelta(days=offset)
        nearby_options = strategy.spx_options_data[strategy.spx_options_data['date'] == check_date]
        print(f"      {check_date.date()}: {len(nearby_options)} options")

print(f"\n🎯 CONCLUSION:")
print(f"   If the exact same option exists on entry but not exit,")
print(f"   then there's a bug in the filtering or data storage logic.")
print(f"   The same option MUST be available 3 days later.")
