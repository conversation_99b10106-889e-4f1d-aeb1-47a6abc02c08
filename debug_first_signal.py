#!/usr/bin/env python3

import sys
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()
signals = strategy.generate_enhanced_signals(market_data)

print(f'Generated {len(signals)} signals')
print('First 3 signals:')
for i, sig in enumerate(signals[:3]):
    print(f'  {i+1}. {sig["date"]}: {sig["signal_direction"]}, confidence: {sig["confidence_score"]:.3f}')

# Test the first signal
first_signal = signals[0]
date = first_signal['date']
signal_direction = first_signal['signal_direction']

print(f'\nTesting option selection for {date}:')
day_options = strategy.spx_options_data[strategy.spx_options_data['date'] == date]
spx_price = day_options['spx_close'].iloc[0] if len(day_options) > 0 else 4000

short_option, long_option = strategy.find_call_spread_options(date, spx_price, signal_direction)

if short_option is not None and long_option is not None:
    print(f'SUCCESS: Found options')
    print(f'  Short: Strike {short_option["Strike"]:.0f}, Price ${short_option["mid_price"]:.2f}')
    print(f'  Long: Strike {long_option["Strike"]:.0f}, Price ${long_option["mid_price"]:.2f}')
else:
    print(f'FAILED: No options found')
