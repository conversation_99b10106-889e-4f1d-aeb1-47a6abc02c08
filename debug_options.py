#!/usr/bin/env python3
"""
Debug script to test options data loading
"""

import pandas as pd
import os
from datetime import datetime

def test_options_loading():
    """Test loading options for a specific date"""
    
    # Test parameters
    test_date = pd.to_datetime('2023-06-15')
    test_strike = 4325
    options_base_path = "/Users/<USER>/Downloads/optionhistory"
    
    print(f"🔍 Testing options loading for {test_date.strftime('%Y-%m-%d')}")
    print(f"   Strike: {test_strike}")
    print(f"   Base path: {options_base_path}")
    
    # Construct file path
    year = test_date.year
    quarter = (test_date.month - 1) // 3 + 1
    subdir = f"{year}_q{quarter}_option_chain"
    filename = f"spx_complete_{year}_q{quarter}.csv"
    target_file = os.path.join(options_base_path, subdir, filename)
    
    print(f"\n📁 Looking for file: {target_file}")
    print(f"   File exists: {os.path.exists(target_file)}")
    
    if not os.path.exists(target_file):
        print("❌ File not found!")
        return
    
    try:
        print("\n📊 Loading data...")
        # Load a small sample first
        df = pd.read_csv(target_file, nrows=1000)
        print(f"   Loaded {len(df)} sample rows")
        
        # Check date format
        print(f"   Date column sample: {df['date'].head(3).tolist()}")
        
        # Parse dates
        df['date'] = pd.to_datetime(df['date'])
        print(f"   Parsed dates sample: {df['date'].head(3).tolist()}")
        
        # Check if our test date exists
        test_date_normalized = test_date.normalize()
        matching_dates = df[df['date'] == test_date_normalized]
        print(f"\n🎯 Checking for {test_date_normalized}:")
        print(f"   Found {len(matching_dates)} rows for this date")
        
        if len(matching_dates) > 0:
            print("✅ Date found in sample!")
            
            # Check for our strike
            strike_matches = matching_dates[
                (matching_dates['Strike'] == test_strike) &
                (matching_dates['Call/Put'] == 'c')
            ]
            print(f"   Found {len(strike_matches)} call options for strike {test_strike}")
            
            if len(strike_matches) > 0:
                print("✅ Strike found!")
                print("\n📋 Available options:")
                for _, row in strike_matches.head(5).iterrows():
                    expiry = pd.to_datetime(row['Expiry Date'])
                    dte = (expiry - test_date).days
                    print(f"   Expiry: {expiry.strftime('%Y-%m-%d')} ({dte} days), Price: ${row['Last Trade Price']:.2f}")
            else:
                print("❌ Strike not found in sample")
        else:
            print("❌ Date not found in sample")
            print(f"   Available dates in sample: {df['date'].min()} to {df['date'].max()}")
            
            # Try loading more data
            print("\n🔄 Loading full file...")
            df_full = pd.read_csv(target_file)
            df_full['date'] = pd.to_datetime(df_full['date'])
            
            matching_dates_full = df_full[df_full['date'] == test_date_normalized]
            print(f"   Found {len(matching_dates_full)} rows for {test_date_normalized} in full file")
            
            if len(matching_dates_full) > 0:
                strike_matches_full = matching_dates_full[
                    (matching_dates_full['Strike'] == test_strike) &
                    (matching_dates_full['Call/Put'] == 'c')
                ]
                print(f"   Found {len(strike_matches_full)} call options for strike {test_strike}")
                
                if len(strike_matches_full) > 0:
                    print("✅ Found options in full file!")
                    print("\n📋 Available options:")
                    for _, row in strike_matches_full.head(10).iterrows():
                        expiry = pd.to_datetime(row['Expiry Date'])
                        dte = (expiry - test_date).days
                        print(f"   Expiry: {expiry.strftime('%Y-%m-%d')} ({dte} days), Price: ${row['Last Trade Price']:.2f}")
                        
                    # Find 30-day options
                    thirty_day_options = strike_matches_full[
                        (strike_matches_full['Expiry Date'].apply(lambda x: (pd.to_datetime(x) - test_date).days) >= 25) &
                        (strike_matches_full['Expiry Date'].apply(lambda x: (pd.to_datetime(x) - test_date).days) <= 35)
                    ]
                    print(f"\n🎯 30-day options (25-35 days): {len(thirty_day_options)}")
                    for _, row in thirty_day_options.iterrows():
                        expiry = pd.to_datetime(row['Expiry Date'])
                        dte = (expiry - test_date).days
                        print(f"   ⭐ Expiry: {expiry.strftime('%Y-%m-%d')} ({dte} days), Price: ${row['Last Trade Price']:.2f}")
                        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_options_loading()
