#!/usr/bin/env python3

import pandas as pd
import sys
import os

print("🔍 DEBUGGING SIGNAL GENERATION")
print("=" * 50)

# Load the trade data to see what dates we expect
trades_df = pd.read_csv('trades/call_spread_trades.csv')
first_trade = trades_df.iloc[0]

print(f"📊 FIRST TRADE DETAILS:")
print(f"   Entry Date: {first_trade['entry_date']}")
print(f"   Signal Date: {first_trade['signal_date']}")
print(f"   Signal Direction: {first_trade['signal_direction']}")
print(f"   Short Strike: {first_trade['short_strike']}")
print(f"   Long Strike: {first_trade['long_strike']}")

# Load the strategy and check signal generation
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()

print(f"\n📊 MARKET DATA LOADED:")
print(f"   Records: {len(market_data)}")
print(f"   Date range: {market_data.index.min()} to {market_data.index.max()}")

# Check if the signal date exists in market data
signal_date = pd.to_datetime(first_trade['signal_date'])
entry_date = pd.to_datetime(first_trade['entry_date'])

print(f"\n🔍 CHECKING SIGNAL DATE: {signal_date}")
if signal_date in market_data.index:
    print(f"   ✅ Signal date found in market data")
    signal_row = market_data.loc[signal_date]
    print(f"   VIX: {signal_row.get('close_vix', 'N/A')}")
    print(f"   VRP: {signal_row.get('vrp_avg', 'N/A')}")
else:
    print(f"   ❌ Signal date NOT found in market data")
    print(f"   Available dates around signal date:")
    nearby_dates = market_data.index[
        (market_data.index >= signal_date - pd.Timedelta(days=5)) &
        (market_data.index <= signal_date + pd.Timedelta(days=5))
    ]
    for d in nearby_dates:
        print(f"      {d}")

print(f"\n🔍 CHECKING ENTRY DATE: {entry_date}")
if entry_date in market_data.index:
    print(f"   ✅ Entry date found in market data")
else:
    print(f"   ❌ Entry date NOT found in market data")

# Generate signals and see what we get
print(f"\n🎯 GENERATING SIGNALS:")
signals = strategy.generate_enhanced_signals(market_data)
print(f"   Generated {len(signals)} signals")

# Check if our target signal date is in the generated signals
target_signals = [s for s in signals if s['date'] == signal_date]
print(f"   Signals for {signal_date}: {len(target_signals)}")

if len(target_signals) > 0:
    for i, sig in enumerate(target_signals):
        print(f"   Signal {i+1}: {sig['signal_direction']}, confidence: {sig['confidence_score']:.3f}")
else:
    print(f"   ❌ No signals generated for {signal_date}")
    
    # Show signals around that date
    print(f"   Signals around {signal_date}:")
    for sig in signals:
        sig_date = sig['date']
        if abs((sig_date - signal_date).days) <= 3:
            print(f"      {sig_date}: {sig['signal_direction']}, confidence: {sig['confidence_score']:.3f}")

# Check options data for the entry date
print(f"\n🔍 CHECKING OPTIONS DATA FOR ENTRY DATE: {entry_date}")
options_data = strategy.spx_options_data
entry_options = options_data[options_data['date'] == entry_date]
print(f"   Options records for {entry_date}: {len(entry_options)}")

if len(entry_options) > 0:
    # Check for the specific strikes
    short_strike_options = entry_options[
        (entry_options['Strike'] == first_trade['short_strike']) &
        (entry_options['Call/Put'] == 'c')
    ]
    long_strike_options = entry_options[
        (entry_options['Strike'] == first_trade['long_strike']) &
        (entry_options['Call/Put'] == 'c')
    ]
    
    print(f"   Short strike {first_trade['short_strike']} options: {len(short_strike_options)}")
    print(f"   Long strike {first_trade['long_strike']} options: {len(long_strike_options)}")
    
    if len(short_strike_options) > 0:
        print(f"   Short strike expiries:")
        for _, opt in short_strike_options.iterrows():
            dte = (opt['expiry_date'] - opt['date']).days
            mid_price = (opt['Bid Price'] + opt['Ask Price']) / 2
            print(f"      {opt['expiry_date'].strftime('%Y-%m-%d')} ({dte} DTE) - ${mid_price:.2f}")
    
    if len(long_strike_options) > 0:
        print(f"   Long strike expiries:")
        for _, opt in long_strike_options.iterrows():
            dte = (opt['expiry_date'] - opt['date']).days
            mid_price = (opt['Bid Price'] + opt['Ask Price']) / 2
            print(f"      {opt['expiry_date'].strftime('%Y-%m-%d')} ({dte} DTE) - ${mid_price:.2f}")

print(f"\n🎯 CONCLUSION:")
print("This will show us exactly where the disconnect is between:")
print("1. Signal generation")
print("2. Options data availability") 
print("3. The actual trade execution")
