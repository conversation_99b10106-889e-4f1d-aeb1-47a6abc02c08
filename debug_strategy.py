#!/usr/bin/env python3

import pandas as pd
from datetime import datetime, timedelta

# Load trade data to see what the strategy actually selected
trades = pd.read_csv('trades/call_spread_trades.csv')
trade1 = trades.iloc[0]

print('🔍 TRADE #1 STRATEGY SELECTION:')
print(f'Entry Date: {trade1["entry_date"]}')
print(f'SPX Price: ${trade1["spx_price"]:.2f}')
print(f'Signal Direction: {trade1["signal_direction"]}')
print(f'Short Strike: {trade1["short_strike"]:.0f}, Entry Price: ${trade1["short_entry_price"]:.2f}')
print(f'Long Strike: {trade1["long_strike"]:.0f}, Entry Price: ${trade1["long_entry_price"]:.2f}')
print(f'Net Credit: ${trade1["net_credit"]:.2f}')

# Load the actual options data
df = pd.read_csv('/Users/<USER>/Downloads/optionhistory/2023_q2_option_chain/spx_complete_2023_q2.csv')
df['date'] = pd.to_datetime(df['date'])
df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
df['days_to_expiry'] = (df['expiry_date'] - df['date']).dt.days
df['mid_price'] = (df['Bid Price'] + df['Ask Price']) / 2

# Filter for the entry date
entry_date = pd.to_datetime('2023-06-15')
day_options = df[
    (df['date'] == entry_date) &
    (df['Call/Put'] == 'c')
].copy()

print(f'\n📊 ALL CALL OPTIONS on 2023-06-15:')
print(f'Total options available: {len(day_options)}')

# Filter for 25-35 DTE like the strategy does
dte_filtered = day_options[
    (day_options['days_to_expiry'] >= 25) &
    (day_options['days_to_expiry'] <= 35)
].copy()

print(f'Options with 25-35 DTE: {len(dte_filtered)}')

# Filter for strikes divisible by 25
strike_filtered = dte_filtered[dte_filtered['Strike'] % 25 == 0].copy()
print(f'Options with strikes divisible by 25: {len(strike_filtered)}')

if len(strike_filtered) == 0:
    print('❌ NO OPTIONS FOUND after filtering! This explains the bug.')
    print('\n🔍 Let\'s check what DTE ranges are actually available:')
    
    dte_counts = day_options['days_to_expiry'].value_counts().sort_index()
    print('Available DTE ranges:')
    for dte, count in dte_counts.head(20).items():
        print(f'  {dte} DTE: {count} options')
    
    print('\n🔍 Let\'s check strikes around the trade strikes:')
    spx_price = trade1["spx_price"]
    
    # Check what options exist around the selected strikes
    short_strike_options = day_options[day_options['Strike'] == 4325]
    long_strike_options = day_options[day_options['Strike'] == 4475]
    
    print(f'\nOptions for SHORT strike 4325:')
    for _, opt in short_strike_options.iterrows():
        print(f'  {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${opt["mid_price"]:.2f}')
    
    print(f'\nOptions for LONG strike 4475:')
    for _, opt in long_strike_options.iterrows():
        print(f'  {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${opt["mid_price"]:.2f}')

else:
    print(f'\n✅ Found {len(strike_filtered)} options after all filtering')
    
    # Now apply the strategy's strike selection logic
    spx_price = trade1["spx_price"]
    signal_direction = trade1["signal_direction"]
    
    print(f'\n🎯 STRATEGY STRIKE SELECTION:')
    print(f'SPX Price: ${spx_price:.2f}')
    print(f'Signal Direction: {signal_direction}')
    
    if signal_direction == 'BULLISH':
        short_strike_min = spx_price * 0.97  # 3% ITM
        short_strike_max = spx_price * 1.02  # 2% OTM
        long_strike_min = spx_price * 1.01   # 1% OTM
        long_strike_max = spx_price * 1.06   # 6% OTM
    else:
        short_strike_min = spx_price * 1.00  # ATM
        short_strike_max = spx_price * 1.04  # 4% OTM
        long_strike_min = spx_price * 1.02   # 2% OTM
        long_strike_max = spx_price * 1.07   # 7% OTM
    
    print(f'Short strike range: ${short_strike_min:.2f} - ${short_strike_max:.2f}')
    print(f'Long strike range: ${long_strike_min:.2f} - ${long_strike_max:.2f}')
    
    # Find candidates
    short_candidates = strike_filtered[
        (strike_filtered['Strike'] >= short_strike_min) &
        (strike_filtered['Strike'] <= short_strike_max) &
        (strike_filtered['mid_price'] >= 1.0)
    ]
    
    long_candidates = strike_filtered[
        (strike_filtered['Strike'] >= long_strike_min) &
        (strike_filtered['Strike'] <= long_strike_max) &
        (strike_filtered['mid_price'] >= 0.5)
    ]
    
    print(f'\nShort candidates: {len(short_candidates)}')
    for _, opt in short_candidates.iterrows():
        print(f'  Strike {opt["Strike"]:.0f}: ${opt["mid_price"]:.2f}')
    
    print(f'\nLong candidates: {len(long_candidates)}')
    for _, opt in long_candidates.iterrows():
        print(f'  Strike {opt["Strike"]:.0f}: ${opt["mid_price"]:.2f}')
