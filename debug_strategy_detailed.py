#!/usr/bin/env python3

import pandas as pd
from datetime import datetime, timedelta

# Load the actual strategy and run it step by step
import sys
sys.path.append('.')

from core.data_loader import DataLoader
from call_spread_strategy import CallSpreadStrategy

print('🔍 DEBUGGING CALL SPREAD STRATEGY STEP BY STEP')

# Load data exactly like the strategy does
data_loader = DataLoader()
market_data = data_loader.load_all_data()

print(f'✅ Loaded market data')
print(f'SPX Options data shape: {market_data["spx_options"].shape}')
print(f'SPX Options date range: {market_data["spx_options"]["date"].min()} to {market_data["spx_options"]["date"].max()}')

# Initialize strategy
strategy = CallSpreadStrategy()
strategy.spx_options_data = market_data['spx_options']

print(f'\n🎯 CHECKING SPECIFIC DATE: 2023-06-15')

# Check what data exists for 2023-06-15
target_date = pd.to_datetime('2023-06-15')
day_data = strategy.spx_options_data[strategy.spx_options_data['date'] == target_date]

print(f'Options data for 2023-06-15: {len(day_data)} rows')

if len(day_data) > 0:
    print(f'SPX price on 2023-06-15: ${day_data["spx_close"].iloc[0]:.2f}')
    
    # Check DTE distribution
    day_data['days_to_expiry'] = (day_data['expiry_date'] - day_data['date']).dt.days
    dte_counts = day_data['days_to_expiry'].value_counts().sort_index()
    
    print(f'\nDTE distribution on 2023-06-15:')
    for dte, count in dte_counts.head(15).items():
        print(f'  {dte} DTE: {count} options')
    
    # Check 25-35 DTE specifically
    dte_filtered = day_data[
        (day_data['days_to_expiry'] >= 25) &
        (day_data['days_to_expiry'] <= 35)
    ]
    print(f'\nOptions with 25-35 DTE: {len(dte_filtered)}')
    
    if len(dte_filtered) > 0:
        # Check strikes 4325 and 4475 in 25-35 DTE range
        strike_4325 = dte_filtered[dte_filtered['Strike'] == 4325]
        strike_4475 = dte_filtered[dte_filtered['Strike'] == 4475]
        
        print(f'\nStrike 4325 options (25-35 DTE):')
        for _, opt in strike_4325.iterrows():
            mid_price = (opt['Bid Price'] + opt['Ask Price']) / 2
            print(f'  {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${mid_price:.2f}')
        
        print(f'\nStrike 4475 options (25-35 DTE):')
        for _, opt in strike_4475.iterrows():
            mid_price = (opt['Bid Price'] + opt['Ask Price']) / 2
            print(f'  {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${mid_price:.2f}')
    
    # Now let's check shorter DTE ranges to see where the trade prices come from
    print(f'\n🔍 CHECKING SHORTER DTE RANGES:')
    
    for dte_range in [(0, 5), (6, 10), (11, 15), (16, 20)]:
        min_dte, max_dte = dte_range
        short_dte = day_data[
            (day_data['days_to_expiry'] >= min_dte) &
            (day_data['days_to_expiry'] <= max_dte)
        ]
        
        if len(short_dte) > 0:
            strike_4325_short = short_dte[short_dte['Strike'] == 4325]
            strike_4475_short = short_dte[short_dte['Strike'] == 4475]
            
            print(f'\n{min_dte}-{max_dte} DTE range:')
            
            for _, opt in strike_4325_short.iterrows():
                mid_price = (opt['Bid Price'] + opt['Ask Price']) / 2
                diff_from_trade = abs(mid_price - 93.95)
                marker = '🎯' if diff_from_trade < 1 else '⭐' if diff_from_trade < 5 else '  '
                print(f'  {marker} 4325: {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${mid_price:.2f} (diff: ${diff_from_trade:.2f})')
            
            for _, opt in strike_4475_short.iterrows():
                mid_price = (opt['Bid Price'] + opt['Ask Price']) / 2
                diff_from_trade = abs(mid_price - 13.70)
                marker = '🎯' if diff_from_trade < 1 else '⭐' if diff_from_trade < 5 else '  '
                print(f'  {marker} 4475: {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${mid_price:.2f} (diff: ${diff_from_trade:.2f})')

else:
    print('❌ No options data found for 2023-06-15')

print(f'\n🎯 CONCLUSION:')
print('If we find exact matches in shorter DTE ranges, then the strategy has a bug in DTE filtering.')
print('If we don\'t find exact matches anywhere, then there\'s a different data source issue.')
