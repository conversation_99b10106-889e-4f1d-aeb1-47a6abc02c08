#!/usr/bin/env python3

import pandas as pd
import sys

print("🔍 DEBUGGING STRATEGY EXECUTION vs VALIDATION")
print("=" * 60)

# Load the strategy and execute just the first signal
sys.path.append('.')
from call_spread_strategy import CallSpreadStrategy

strategy = CallSpreadStrategy()
market_data = strategy.load_market_data_with_real_vrp()
signals = strategy.generate_enhanced_signals(market_data)

print(f"✅ Generated {len(signals)} signals")

# Get the first signal
first_signal = signals[0]
date = first_signal['date']
signal_direction = first_signal['signal_direction']

print(f"\n📊 FIRST SIGNAL:")
print(f"   Date: {date}")
print(f"   Direction: {signal_direction}")

# Get SPX price for that date
spx_price = 4200  # Approximate value, strategy will find exact

print(f"\n🔍 CALLING find_call_spread_options DIRECTLY:")
print(f"   Date: {date}")
print(f"   SPX Price: ~{spx_price}")
print(f"   Direction: {signal_direction}")

# Call the exact same method the strategy uses
short_option, long_option = strategy.find_call_spread_options(date, spx_price, signal_direction)

if short_option is not None and long_option is not None:
    print(f"\n✅ STRATEGY FOUND OPTIONS:")
    print(f"   Short: Strike {short_option['Strike']:.0f}, Price ${short_option['option_price']:.2f}")
    print(f"   Long:  Strike {long_option['Strike']:.0f}, Price ${long_option['option_price']:.2f}")
    
    # Now manually verify these exist in the raw data
    print(f"\n🔍 MANUAL VERIFICATION IN RAW DATA:")
    
    # Check short option
    short_matches = strategy.spx_options_data[
        (strategy.spx_options_data['date'] == date) &
        (strategy.spx_options_data['Strike'] == short_option['Strike']) &
        (strategy.spx_options_data['Call/Put'] == 'c') &
        (abs(strategy.spx_options_data['Last Trade Price'] - short_option['option_price']) < 0.01)
    ]
    
    print(f"   Short matches in raw data: {len(short_matches)}")
    if len(short_matches) > 0:
        match = short_matches.iloc[0]
        print(f"      ✅ Found: Strike {match['Strike']}, Price ${match['Last Trade Price']:.2f}, Expiry {match['expiry_date'].strftime('%Y-%m-%d')}")
    
    # Check long option  
    long_matches = strategy.spx_options_data[
        (strategy.spx_options_data['date'] == date) &
        (strategy.spx_options_data['Strike'] == long_option['Strike']) &
        (strategy.spx_options_data['Call/Put'] == 'c') &
        (abs(strategy.spx_options_data['Last Trade Price'] - long_option['option_price']) < 0.01)
    ]
    
    print(f"   Long matches in raw data: {len(long_matches)}")
    if len(long_matches) > 0:
        match = long_matches.iloc[0]
        print(f"      ✅ Found: Strike {match['Strike']}, Price ${match['Last Trade Price']:.2f}, Expiry {match['expiry_date'].strftime('%Y-%m-%d')}")
    
    # Final verdict
    if len(short_matches) > 0 and len(long_matches) > 0:
        print(f"\n✅ VALIDATION SUCCESSFUL!")
        print(f"   Strategy selections can be verified in raw data")
    else:
        print(f"\n❌ VALIDATION FAILED!")
        print(f"   Strategy claims to find options that don't exist in raw data")
        print(f"   This indicates a serious bug in the strategy logic")
        
else:
    print(f"\n❌ STRATEGY FOUND NO OPTIONS")
    print(f"   This indicates the strategy is not working at all")

print(f"\n🎯 CONCLUSION:")
print(f"   If validation fails, the strategy is NOT using real market data")
print(f"   If validation succeeds, the previous validation script had a bug")
