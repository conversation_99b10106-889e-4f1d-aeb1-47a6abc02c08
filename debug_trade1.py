#!/usr/bin/env python3

import pandas as pd
from datetime import datetime

# Load trade data
trades = pd.read_csv('trades/call_spread_trades.csv')
trade1 = trades.iloc[0]

print('🔍 TRADE #1 DETAILS:')
print(f'Entry Date: {trade1["entry_date"]}')
print(f'Short Strike: {trade1["short_strike"]}, Price: ${trade1["short_entry_price"]:.2f}')
print(f'Long Strike: {trade1["long_strike"]}, Price: ${trade1["long_entry_price"]:.2f}')

# Load options data for that date
df = pd.read_csv('/Users/<USER>/Downloads/optionhistory/2023_q2_option_chain/spx_complete_2023_q2.csv')
df['date'] = pd.to_datetime(df['date'])
df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
df['days_to_expiry'] = (df['expiry_date'] - df['date']).dt.days
df['mid_price'] = (df['Bid Price'] + df['Ask Price']) / 2

# Filter for exact trade conditions
entry_date = pd.to_datetime('2023-06-15')
short_options = df[
    (df['date'] == entry_date) &
    (df['Strike'] == 4325) &
    (df['Call/Put'] == 'c')
].copy()

long_options = df[
    (df['date'] == entry_date) &
    (df['Strike'] == 4475) &
    (df['Call/Put'] == 'c')
].copy()

print(f'\n📊 SHORT OPTIONS (4325 strike) on 2023-06-15:')
for _, opt in short_options.iterrows():
    price_diff = abs(opt['mid_price'] - 93.95)
    marker = '🎯' if price_diff < 0.01 else '⭐' if price_diff < 1 else '  '
    print(f'{marker} {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${opt["mid_price"]:.2f} (diff: ${price_diff:.2f})')

print(f'\n📊 LONG OPTIONS (4475 strike) on 2023-06-15:')
for _, opt in long_options.iterrows():
    price_diff = abs(opt['mid_price'] - 13.70)
    marker = '🎯' if price_diff < 0.01 else '⭐' if price_diff < 1 else '  '
    print(f'{marker} {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - Mid: ${opt["mid_price"]:.2f} (diff: ${price_diff:.2f})')

# Find exact matches
exact_short = short_options[abs(short_options['mid_price'] - 93.95) < 0.01]
exact_long = long_options[abs(long_options['mid_price'] - 13.70) < 0.01]

print(f'\n🎯 EXACT MATCHES:')
if len(exact_short) > 0:
    for _, opt in exact_short.iterrows():
        print(f'   SHORT: {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - ${opt["mid_price"]:.2f}')
else:
    print('   SHORT: No exact matches found')

if len(exact_long) > 0:
    for _, opt in exact_long.iterrows():
        print(f'   LONG: {opt["expiry_date"].strftime("%Y-%m-%d")} ({opt["days_to_expiry"]} DTE) - ${opt["mid_price"]:.2f}')
else:
    print('   LONG: No exact matches found')
