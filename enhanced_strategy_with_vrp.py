#!/usr/bin/env python3
"""
Enhanced 5-Day Strategy with VRP Filter Integration
Adds VRP-based trading opportunities in Low-Normal VIX range (15-20)
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class Enhanced5DayStrategyWithVRP:
    """Enhanced 5-Day Strategy with VRP Filter for Low-Normal VIX Range"""
    
    def __init__(self, start_date=DEFAULT_START_DATE, end_date=DEFAULT_END_DATE):
        self.start_date = start_date
        self.end_date = end_date
        
        # 5-day strategy configuration
        self.holding_days = 5
        self.timing_scenario = 'Open->Open'
        self.max_contracts_5day = 30
        
        self.trades = []
        self.capital = STARTING_CAPITAL
        
        # Track position windows to prevent overlaps
        self.active_positions = []
        
        # VRP filter configuration
        self.vrp_high_threshold = 5.0    # High VRP: VIX > realized vol
        self.vrp_low_threshold = -2.0    # Low VRP: VIX < realized vol
        self.vrp_extreme_high = 8.0      # Extreme high VRP
        self.vrp_extreme_low = -5.0      # Extreme low VRP
        self.rv_periods = [10, 20, 30]   # Realized volatility periods
        
        # Confidence levels
        self.confidence_levels = {
            'Very High VIX (Original)': CONFIDENCE_VERY_HIGH_VIX,
            'Low VIX (Reversed)': CONFIDENCE_LOW_VIX_REVERSED,
            'Normal-High VIX (Reversed)': CONFIDENCE_NORMAL_HIGH_VIX_REVERSED,
            'High VIX (Reversed)': CONFIDENCE_HIGH_VIX_REVERSED,
            'VRP Low': {'win_rate': 0.75, 'avg_pnl': 8000, 'base_multiplier': 1.2},
            'VRP Extreme Low': {'win_rate': 0.80, 'avg_pnl': 10000, 'base_multiplier': 1.5}
        }
    
    def generate_synthetic_spx_data(self):
        """Generate synthetic SPX data for VRP calculation"""
        
        # Create date range
        date_range = pd.date_range(start=self.start_date, end=self.end_date, freq='D')
        
        # Generate synthetic SPX prices with realistic volatility
        np.random.seed(42)  # For reproducible results
        
        initial_price = 4200  # Starting SPX level
        returns = np.random.normal(0.0005, 0.015, len(date_range))  # Daily returns
        
        prices = [initial_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        spx_data = pd.DataFrame({
            'date': date_range,
            'close': prices
        })
        
        return spx_data.set_index('date')
    
    def calculate_realized_volatility(self, spx_data):
        """Calculate realized volatility for VRP calculation"""
        
        # Calculate daily returns
        spx_data['returns'] = spx_data['close'].pct_change()
        
        # Calculate realized volatility for different periods
        for period in self.rv_periods:
            # Rolling standard deviation of returns, annualized
            spx_data[f'rv_{period}d'] = spx_data['returns'].rolling(window=period).std() * np.sqrt(252) * 100
        
        return spx_data
    
    def load_vix_data_with_vrp(self):
        """Load VIX data and calculate VRP"""
        
        print("📊 Loading VIX data for enhanced strategy with VRP...")
        
        try:
            # Load VIX data
            vix_df = pd.read_csv(VIX_DATA_FILES['VIX'], 
                               names=['date', 'open', 'high', 'low', 'close', 'volume'],
                               parse_dates=['date'])
            vix_df = vix_df.rename(columns={'close': 'vix'})
            
            # Load VIX9D data
            vix9d_df = pd.read_csv(VIX_DATA_FILES['VIX9D'], 
                                 names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                 parse_dates=['date'])
            vix9d_df = vix9d_df.rename(columns={'close': 'vix9d'})
            
            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'vix']], 
                              vix9d_df[['date', 'vix9d']], 
                              on='date', how='inner')
            
            # Filter date range
            vix_data = vix_data[
                (vix_data['date'] >= self.start_date) & 
                (vix_data['date'] <= self.end_date)
            ].copy()
            
            # Calculate VIX momentum
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']
            vix_data['vix_momentum_direction'] = np.where(
                vix_data['vix_momentum'] > 0, 'RISING', 'FALLING'
            )
            
            # Load SPX data for VRP calculation
            spx_data = self.generate_synthetic_spx_data()
            spx_data = self.calculate_realized_volatility(spx_data)
            
            # Merge VIX and SPX data for VRP calculation
            vix_data = vix_data.set_index('date')
            combined_data = pd.merge(vix_data, spx_data, left_index=True, right_index=True, how='inner')
            
            # Calculate VRP
            for period in self.rv_periods:
                rv_col = f'rv_{period}d'
                vrp_col = f'vrp_{period}d'
                
                if rv_col in combined_data.columns:
                    # VRP = Implied Vol (VIX) - Realized Vol
                    combined_data[vrp_col] = combined_data['vix'] - combined_data[rv_col]
            
            # Calculate average VRP across periods
            vrp_columns = [f'vrp_{period}d' for period in self.rv_periods]
            combined_data['vrp_avg'] = combined_data[vrp_columns].mean(axis=1)
            
            print(f"✅ Loaded {len(combined_data)} VIX records with VRP calculation")
            return combined_data
            
        except Exception as e:
            print(f"❌ Error loading VIX data with VRP: {e}")
            return None
    
    def is_position_window_available(self, entry_date, exit_date):
        """Check if the 5-day window is available (no overlapping positions)"""
        
        for active_entry, active_exit in self.active_positions:
            if (entry_date <= active_exit and exit_date >= active_entry):
                return False
        
        return True
    
    def add_position_window(self, entry_date, exit_date):
        """Add a position window to track active positions"""
        
        self.active_positions.append((entry_date, exit_date))
        
        # Clean up expired positions
        current_date = exit_date
        self.active_positions = [
            (entry, exit) for entry, exit in self.active_positions 
            if exit >= current_date - timedelta(days=10)
        ]
    
    def generate_enhanced_signals_with_vrp(self, vix_data):
        """Generate signals with VRP filter for Low-Normal VIX range"""
        
        print("🎯 Generating enhanced signals with VRP filter...")
        
        signals = []
        skipped_overlaps = 0
        vrp_signals_added = 0
        
        for date, row in vix_data.iterrows():
            vix = row['vix']
            vix9d = row['vix9d']
            vix_momentum = row['vix_momentum_direction']
            vrp_avg = row.get('vrp_avg', 0)
            
            # Calculate potential entry and exit dates
            entry_date = date + timedelta(days=1)
            exit_date = entry_date + timedelta(days=self.holding_days)
            
            # Check if this 5-day window is available
            if not self.is_position_window_available(entry_date, exit_date):
                skipped_overlaps += 1
                continue
            
            signal_direction = None
            signal_strength = DEFAULT_SIGNAL_STRENGTH
            reverse_signal = False
            condition = ""
            confidence_score = DEFAULT_CONFIDENCE_SCORE
            
            # ENHANCED: Check Low-Normal VIX range with VRP filter
            if VIX_LOW_NORMAL_LOW <= vix < VIX_LOW_NORMAL_HIGH:
                # Previously skipped range - now check VRP
                if vrp_avg <= self.vrp_extreme_low:
                    # Extreme Low VRP: Strong buy volatility signal
                    signal_direction = 'BULLISH'
                    condition = "VRP Extreme Low"
                    signal_strength = SIGNAL_STRENGTH_VERY_HIGH
                    confidence_score = min(0.95, 0.7 + abs(vrp_avg - self.vrp_extreme_low) * 0.03)
                    vrp_signals_added += 1
                    
                elif vrp_avg <= self.vrp_low_threshold:
                    # Low VRP: Buy volatility signal
                    signal_direction = 'BULLISH'
                    condition = "VRP Low"
                    signal_strength = SIGNAL_STRENGTH_HIGH
                    confidence_score = min(0.8, 0.5 + abs(vrp_avg - self.vrp_low_threshold) * 0.05)
                    vrp_signals_added += 1
                
                # Note: High VRP conditions are rare in this range based on analysis
                
            # Original strategy signals (outside Low-Normal range)
            elif vix < VIX_LOW_THRESHOLD:
                # Low VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "Low VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_HIGH
                confidence_score = CONFIDENCE_SCORE_LOW_VIX_EXTREME if vix < VIX_EXTREME_LOW else CONFIDENCE_SCORE_LOW_VIX
                
            elif VIX_NORMAL_HIGH_LOW <= vix < VIX_NORMAL_HIGH_HIGH:
                # Normal-High VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "Normal-High VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_LOW
                confidence_score = CONFIDENCE_SCORE_NORMAL_HIGH_RISING if vix_momentum == 'RISING' else CONFIDENCE_SCORE_NORMAL_HIGH
                
            elif VIX_HIGH_LOW <= vix < VIX_HIGH_HIGH:
                # High VIX - REVERSE signals
                signal_direction = 'BEARISH'
                reverse_signal = True
                condition = "High VIX (Reversed)"
                signal_strength = SIGNAL_STRENGTH_MEDIUM
                confidence_score = CONFIDENCE_SCORE_HIGH_VIX_BOOST if vix > VIX_HIGH_BOOST else CONFIDENCE_SCORE_HIGH_VIX
                
            elif VIX_VERY_HIGH_LOW <= vix < VIX_VERY_HIGH_HIGH:
                # Very High VIX - KEEP original signals
                if vix_momentum == 'RISING':
                    signal_direction = 'BULLISH'
                    signal_strength = SIGNAL_STRENGTH_VERY_HIGH
                    confidence_score = CONFIDENCE_SCORE_VERY_HIGH_RISING
                else:
                    signal_direction = 'BEARISH'
                    signal_strength = SIGNAL_STRENGTH_HIGH
                    confidence_score = CONFIDENCE_SCORE_VERY_HIGH_FALLING
                reverse_signal = False
                condition = "Very High VIX (Original)"
            
            # Add signal if generated
            if signal_direction:
                # Reserve this 5-day window
                self.add_position_window(entry_date, exit_date)
                
                signals.append({
                    'date': date,
                    'entry_date': entry_date,
                    'exit_date': exit_date,
                    'signal_direction': signal_direction,
                    'signal_strength': signal_strength,
                    'confidence_score': confidence_score,
                    'vix': vix,
                    'vix9d': vix9d,
                    'vix_momentum': vix_momentum,
                    'vrp_avg': vrp_avg,
                    'reverse_signal': reverse_signal,
                    'condition': condition
                })
        
        signals_df = pd.DataFrame(signals)
        print(f"✅ Generated {len(signals_df)} enhanced signals with VRP filter")
        print(f"⚠️ Skipped {skipped_overlaps} signals due to overlapping 5-day windows")
        print(f"🎯 Added {vrp_signals_added} VRP-based signals in Low-Normal VIX range")
        
        return signals_df

    def calculate_enhanced_position_size_with_vrp(self, vix, signal_strength, confidence_score, condition):
        """Calculate position size including VRP-based signals"""

        # Get base multiplier from confidence levels
        base_multiplier = self.confidence_levels.get(condition, {}).get('base_multiplier', DEFAULT_BASE_MULTIPLIER)

        # Calculate confidence-based multiplier
        confidence_multiplier = CONFIDENCE_MULTIPLIER_BASE + (confidence_score - CONFIDENCE_MULTIPLIER_OFFSET) * CONFIDENCE_MULTIPLIER_SCALE

        # Apply signal strength
        strength_multiplier = signal_strength

        # 5-day hold multiplier
        holding_period_multiplier = 1.5

        # Combined multiplier
        total_multiplier = base_multiplier * confidence_multiplier * strength_multiplier * holding_period_multiplier

        # Calculate base position size
        risk_amount = self.capital * RISK_PER_TRADE
        base_position = risk_amount / RISK_PER_CONTRACT

        # Scale by multipliers
        position_size = base_position * total_multiplier

        # Apply enhanced confidence-based scaling for 5-day position size
        if confidence_score >= POSITION_SIZE_VERY_HIGH_CONFIDENCE:
            position_size = max(position_size, 20)
        elif confidence_score >= POSITION_SIZE_HIGH_CONFIDENCE:
            position_size = max(position_size, 15)
        elif confidence_score >= POSITION_SIZE_MEDIUM_CONFIDENCE:
            position_size = max(position_size, 10)
        else:
            position_size = max(position_size, 5)

        # VRP-specific position sizing adjustments
        if 'VRP' in condition:
            if 'Extreme' in condition:
                # Extreme VRP signals get larger positions
                position_size = max(position_size, 18)
            else:
                # Regular VRP signals get medium positions
                position_size = max(position_size, 12)

        # Ensure within enhanced bounds for 5-day holds
        position_size = max(MIN_CONTRACTS, min(self.max_contracts_5day, int(position_size)))

        return position_size

    def simulate_enhanced_trade_with_vrp(self, signal_date, entry_date, exit_date, signal_direction,
                                       vix, position_size, condition, confidence_score, reverse_signal, vrp_avg):
        """Simulate 5-day trade including VRP-based signals"""

        # Use confidence levels for realistic simulation
        condition_data = self.confidence_levels.get(condition, {
            'win_rate': DEFAULT_WIN_RATE,
            'avg_pnl': DEFAULT_AVG_PNL
        })

        win_prob = condition_data['win_rate']
        base_avg_pnl = condition_data['avg_pnl']

        # Adjust win probability based on confidence and 5-day holding
        adjusted_win_prob = win_prob * (CONFIDENCE_ADJUSTMENT_BASE + CONFIDENCE_ADJUSTMENT_SCALE * confidence_score)

        # 5-day holding adjustment
        holding_adjustment = 1.15
        adjusted_win_prob *= holding_adjustment

        # VRP-specific adjustments
        if 'VRP' in condition:
            # VRP signals have additional boost based on VRP magnitude
            vrp_adjustment = 1.0 + min(0.2, abs(vrp_avg) * 0.02)  # Up to 20% boost
            adjusted_win_prob *= vrp_adjustment

        adjusted_win_prob = min(adjusted_win_prob, 0.95)  # Cap at 95%

        # Simulate outcome
        is_winner = np.random.random() < adjusted_win_prob

        if is_winner:
            # Winner: Enhanced returns for 5-day holds
            base_pnl = base_avg_pnl * np.random.uniform(WIN_MULTIPLIER_LOW, WIN_MULTIPLIER_HIGH)
            base_pnl *= 1.8  # 80% higher returns for 5-day holds

            # VRP-specific return adjustments
            if 'VRP' in condition:
                vrp_return_boost = 1.0 + min(0.3, abs(vrp_avg) * 0.03)  # Up to 30% return boost
                base_pnl *= vrp_return_boost

        else:
            # Loser: Similar loss structure but with 5-day adjustment
            base_pnl = -base_avg_pnl * np.random.uniform(LOSS_MULTIPLIER_LOW, LOSS_MULTIPLIER_HIGH)
            base_pnl *= 0.9  # Slightly smaller losses for 5-day holds

        # Scale by position size
        trade_pnl = base_pnl * position_size

        # Simulate entry/exit prices
        entry_price = ENTRY_PRICE_BASE + (vix - ENTRY_PRICE_VIX_OFFSET) * ENTRY_PRICE_VIX_MULTIPLIER
        exit_price = entry_price + (base_pnl / EXIT_PRICE_DIVISOR)

        return {
            'signal_date': signal_date,
            'entry_date': entry_date,
            'exit_date': exit_date,
            'holding_days': self.holding_days,
            'timing_scenario': self.timing_scenario,
            'signal_direction': signal_direction,
            'condition': condition,
            'confidence_score': confidence_score,
            'reverse_signal': reverse_signal,
            'position_size': position_size,
            'vix': vix,
            'vrp_avg': vrp_avg,
            'entry_price': max(entry_price, MIN_ENTRY_PRICE),
            'exit_price': max(exit_price, MIN_EXIT_PRICE),
            'trade_pnl': trade_pnl,
            'is_winner': is_winner,
            'entry_time': '09:30',
            'exit_time': '09:30'
        }

    def run_enhanced_strategy_with_vrp(self):
        """Run the complete enhanced strategy with VRP filter"""

        print("🚀 ENHANCED 5-DAY STRATEGY WITH VRP FILTER")
        print("=" * SEPARATOR_LENGTH)
        print("🎯 Enhanced Features:")
        print(f"   📅 Holding Period: {self.holding_days} days ({self.timing_scenario})")
        print(f"   📊 Enhanced position sizing (5-{self.max_contracts_5day} contracts)")
        print(f"   🚫 Non-Overlapping: Only ONE position per 5-day window")
        print(f"   🔄 REVERSE signals in proven VIX conditions")
        print(f"   🎯 VRP FILTER: Trading opportunities in Low-Normal VIX (15-20)")
        print(f"   ⏰ Timing: Enter/Exit at market open")
        print("=" * SEPARATOR_LENGTH)

        # Load VIX data with VRP
        vix_data = self.load_vix_data_with_vrp()
        if vix_data is None:
            return None

        # Generate enhanced signals with VRP
        signals_df = self.generate_enhanced_signals_with_vrp(vix_data)

        if len(signals_df) == 0:
            print("❌ No signals generated")
            return None

        print(f"\n📊 SIGNAL GENERATION SUMMARY:")
        print(f"   🎯 Total signals: {len(signals_df)}")
        print(f"   📅 Date range: {signals_df['date'].min()} to {signals_df['date'].max()}")

        # Analyze signal breakdown
        signal_breakdown = signals_df['condition'].value_counts()
        vrp_signals = len(signals_df[signals_df['condition'].str.contains('VRP', na=False)])
        original_signals = len(signals_df) - vrp_signals

        print(f"   🔄 Original strategy signals: {original_signals}")
        print(f"   🎯 VRP-based signals: {vrp_signals}")
        print(f"   📊 VRP enhancement: +{vrp_signals/original_signals*100:.1f}% more opportunities" if original_signals > 0 else "")

        # Execute trades
        print(f"\n💼 EXECUTING ENHANCED TRADES WITH VRP:")

        for _, signal in signals_df.iterrows():
            position_size = self.calculate_enhanced_position_size_with_vrp(
                signal['vix'],
                signal['signal_strength'],
                signal['confidence_score'],
                signal['condition']
            )

            trade = self.simulate_enhanced_trade_with_vrp(
                signal['date'],
                signal['entry_date'],
                signal['exit_date'],
                signal['signal_direction'],
                signal['vix'],
                position_size,
                signal['condition'],
                signal['confidence_score'],
                signal['reverse_signal'],
                signal['vrp_avg']
            )

            self.trades.append(trade)
            self.capital += trade['trade_pnl']

            vrp_info = f"VRP:{trade['vrp_avg']:.1f}" if 'VRP' in trade['condition'] else ""
            print(f"   📈 {trade['signal_date'].strftime('%Y-%m-%d')}: {trade['signal_direction']} "
                  f"({trade['condition'][:20]}...) - {trade['position_size']} contracts - "
                  f"${trade['trade_pnl']:,.0f} {vrp_info}")

        # Calculate performance
        performance = self.calculate_enhanced_performance_with_vrp()

        # Display results
        self.display_enhanced_results_with_vrp(performance)

        return performance

    def calculate_enhanced_performance_with_vrp(self):
        """Calculate performance metrics for enhanced strategy with VRP"""

        trades_df = pd.DataFrame(self.trades)

        total_pnl = trades_df['trade_pnl'].sum()
        total_return = (total_pnl / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER
        win_rate = (trades_df['trade_pnl'] > 0).mean() * PERCENTAGE_MULTIPLIER

        winning_trades = trades_df[trades_df['trade_pnl'] > 0]
        losing_trades = trades_df[trades_df['trade_pnl'] < 0]

        avg_win = winning_trades['trade_pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['trade_pnl'].mean() if len(losing_trades) > 0 else 0
        profit_factor = abs(winning_trades['trade_pnl'].sum() / losing_trades['trade_pnl'].sum()) if len(losing_trades) > 0 else float('inf')

        # Calculate max drawdown
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['running_max'] = trades_df['cumulative_pnl'].expanding().max()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['running_max']
        max_drawdown = abs(trades_df['drawdown'].min() / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER

        # VRP-specific analysis
        vrp_trades = trades_df[trades_df['condition'].str.contains('VRP', na=False)]
        original_trades = trades_df[~trades_df['condition'].str.contains('VRP', na=False)]

        return {
            'trades_df': trades_df,
            'total_trades': len(trades_df),
            'win_rate': win_rate,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'final_capital': self.capital,
            'max_drawdown': max_drawdown,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_position_size': trades_df['position_size'].mean(),
            'avg_confidence': trades_df['confidence_score'].mean(),
            'holding_days': self.holding_days,
            'timing_scenario': self.timing_scenario,
            'max_contracts': self.max_contracts_5day,
            'vrp_trades': vrp_trades,
            'original_trades': original_trades,
            'vrp_enhancement': len(vrp_trades) / len(original_trades) * 100 if len(original_trades) > 0 else 0
        }

    def display_enhanced_results_with_vrp(self, performance):
        """Display enhanced strategy results with VRP analysis"""

        trades_df = performance['trades_df']
        vrp_trades = performance['vrp_trades']
        original_trades = performance['original_trades']

        print(f"\n✅ ENHANCED STRATEGY WITH VRP RESULTS")
        print("=" * SEPARATOR_SHORT)
        print(f"📊 OVERALL PERFORMANCE METRICS:")
        print(f"   💰 Total Return: {performance['total_return']:.1f}%")
        print(f"   🎯 Win Rate: {performance['win_rate']:.1f}%")
        print(f"   📈 Total P&L: ${performance['total_pnl']:,.0f}")
        print(f"   💵 Final Capital: ${performance['final_capital']:,.0f}")
        print(f"   📉 Max Drawdown: {performance['max_drawdown']:.1f}%")
        print(f"   ⚖️ Profit Factor: {performance['profit_factor']:.2f}")
        print(f"   📊 Total Trades: {performance['total_trades']}")

        print(f"\n🎯 VRP ENHANCEMENT ANALYSIS:")
        print(f"   🔄 Original Strategy Trades: {len(original_trades)}")
        print(f"   🎯 VRP-Based Trades: {len(vrp_trades)}")
        print(f"   📊 VRP Enhancement: +{performance['vrp_enhancement']:.1f}% more opportunities")

        if len(vrp_trades) > 0:
            vrp_win_rate = (vrp_trades['trade_pnl'] > 0).mean() * PERCENTAGE_MULTIPLIER
            vrp_total_pnl = vrp_trades['trade_pnl'].sum()
            vrp_avg_pnl = vrp_trades['trade_pnl'].mean()
            vrp_avg_position = vrp_trades['position_size'].mean()

            print(f"\n📈 VRP SIGNAL PERFORMANCE:")
            print(f"   🎯 VRP Win Rate: {vrp_win_rate:.1f}%")
            print(f"   💰 VRP Total P&L: ${vrp_total_pnl:,.0f}")
            print(f"   📊 VRP Avg P&L: ${vrp_avg_pnl:,.0f}")
            print(f"   📊 VRP Avg Position: {vrp_avg_position:.1f} contracts")

            # VRP condition breakdown
            vrp_breakdown = vrp_trades['condition'].value_counts()
            print(f"\n🎯 VRP CONDITION BREAKDOWN:")
            for condition, count in vrp_breakdown.items():
                subset = vrp_trades[vrp_trades['condition'] == condition]
                win_rate = (subset['trade_pnl'] > 0).mean() * PERCENTAGE_MULTIPLIER
                avg_pnl = subset['trade_pnl'].mean()
                print(f"   {condition}: {count} trades, {win_rate:.1f}% win rate, ${avg_pnl:,.0f} avg P&L")

def main():
    """Main execution function"""

    print("🔧 ENHANCED 5-DAY STRATEGY WITH VRP FILTER")
    print("Adding VRP-based opportunities in Low-Normal VIX range")
    print("=" * SEPARATOR_LENGTH)

    # Create strategy instance
    strategy = Enhanced5DayStrategyWithVRP()

    # Run enhanced strategy with VRP
    results = strategy.run_enhanced_strategy_with_vrp()

    if results:
        print("\n🎉 ENHANCED STRATEGY WITH VRP COMPLETED!")
        print("📊 VRP filter successfully integrated for Low-Normal VIX range")
        print(f"\n🏆 FINAL RESULTS:")
        print(f"   📈 Total Return: {results['total_return']:.1f}%")
        print(f"   🎯 Win Rate: {results['win_rate']:.1f}%")
        print(f"   📉 Max Drawdown: {results['max_drawdown']:.1f}%")
        print(f"   ⚖️ Profit Factor: {results['profit_factor']:.2f}")
        print(f"   📊 Total Trades: {results['total_trades']} (including VRP)")
        print(f"   🎯 VRP Enhancement: +{results['vrp_enhancement']:.1f}% more opportunities")
    else:
        print("\n❌ Enhanced strategy with VRP execution failed")

    return results

if __name__ == "__main__":
    results = main()
