"""
Final Real Data Strategy Constants
Centralized configuration for all magic numbers and parameters
"""

# ============================================================================
# STRATEGY METADATA
# ============================================================================

STRATEGY_NAME = "Final Real Data VRP Strategy"
STRATEGY_VERSION = "1.0"
STRATEGY_DESCRIPTION = "Real SPX options data with enhanced position sizing"

# ============================================================================
# VIX REGIME THRESHOLDS
# ============================================================================

# VIX regime boundaries - ADJUSTED FOR ORIGINAL STRATEGY
VIX_LOW_THRESHOLD = 12.0           # Below this: Skip (poor performance) - LOWERED
VIX_LOW_NORMAL_LOW = 12.0          # Low-Normal VIX range start - LOWERED
VIX_LOW_NORMAL_HIGH = 18.0         # Low-Normal VIX range end - LOWERED
VIX_NORMAL_HIGH_THRESHOLD = 22.0   # Normal-High VIX threshold - LOWERED
VIX_HIGH_THRESHOLD = 28.0          # High VIX threshold - LOWERED

# ============================================================================
# VRP THRESHOLDS FOR SUB-BUCKETS
# ============================================================================

# Enhanced VRP thresholds for position sizing tiers
VRP_EXTREME_LOW = -6.0      # Highest confidence (20 contracts)
VRP_VERY_LOW = -4.0         # Very high confidence (15-18 contracts)
VRP_LOW = -2.0              # High confidence (10-12 contracts)
VRP_HIGH = 2.0              # High confidence (10-12 contracts)
VRP_VERY_HIGH = 4.0         # Very high confidence (15-18 contracts)
VRP_EXTREME_HIGH = 6.0      # Highest confidence (20 contracts)

# ============================================================================
# POSITION SIZING PARAMETERS
# ============================================================================

# Position sizing by confidence tier
POSITION_SIZE_EXTREME_MIN = 20
POSITION_SIZE_EXTREME_MAX = 20
POSITION_SIZE_VERY_HIGH_MIN = 15
POSITION_SIZE_VERY_HIGH_MAX = 18
POSITION_SIZE_HIGH_MIN = 10
POSITION_SIZE_HIGH_MAX = 12
POSITION_SIZE_MEDIUM_MIN = 5
POSITION_SIZE_MEDIUM_MAX = 8

# Position sizing calculation parameters
CONFIDENCE_MULTIPLIER_EXTREME = 1.0    # No scaling for extreme
CONFIDENCE_MULTIPLIER_VERY_HIGH_MIN = 0.9
CONFIDENCE_MULTIPLIER_VERY_HIGH_MAX = 1.1
CONFIDENCE_MULTIPLIER_HIGH_MIN = 0.85
CONFIDENCE_MULTIPLIER_HIGH_MAX = 1.15
CONFIDENCE_MULTIPLIER_MEDIUM_MIN = 0.8
CONFIDENCE_MULTIPLIER_MEDIUM_MAX = 1.2

# VIX-based position scaling
VIX_MULTIPLIER_VERY_HIGH = 1.1      # VIX >= 30
VIX_MULTIPLIER_HIGH = 1.05          # VIX >= 25
VIX_MULTIPLIER_NORMAL = 1.0         # VIX < 25

# ============================================================================
# CONFIDENCE LEVELS AND WIN RATES
# ============================================================================

# Confidence levels for each condition
CONFIDENCE_LEVELS = {
    # EXTREME TIER (20 contracts)
    'VRP Extreme Low': {
        'win_rate': 0.85, 'avg_pnl': 1500, 'tier': 'EXTREME',
        'min_pos': POSITION_SIZE_EXTREME_MIN, 'max_pos': POSITION_SIZE_EXTREME_MAX
    },
    'VRP Extreme High': {
        'win_rate': 0.82, 'avg_pnl': 1400, 'tier': 'EXTREME',
        'min_pos': POSITION_SIZE_EXTREME_MIN, 'max_pos': POSITION_SIZE_EXTREME_MAX
    },
    
    # VERY_HIGH TIER (15-18 contracts)
    'VRP Very Low': {
        'win_rate': 0.80, 'avg_pnl': 1300, 'tier': 'VERY_HIGH',
        'min_pos': POSITION_SIZE_VERY_HIGH_MIN, 'max_pos': POSITION_SIZE_VERY_HIGH_MAX
    },
    'VRP Very High': {
        'win_rate': 0.78, 'avg_pnl': 1200, 'tier': 'VERY_HIGH',
        'min_pos': POSITION_SIZE_VERY_HIGH_MIN, 'max_pos': POSITION_SIZE_VERY_HIGH_MAX
    },
    'Very High VIX Rising': {
        'win_rate': 0.78, 'avg_pnl': 1200, 'tier': 'VERY_HIGH',
        'min_pos': POSITION_SIZE_VERY_HIGH_MIN, 'max_pos': POSITION_SIZE_VERY_HIGH_MAX
    },
    
    # HIGH TIER (10-12 contracts)
    'VRP Low': {
        'win_rate': 0.75, 'avg_pnl': 1000, 'tier': 'HIGH',
        'min_pos': POSITION_SIZE_HIGH_MIN, 'max_pos': POSITION_SIZE_HIGH_MAX
    },
    'VRP High': {
        'win_rate': 0.72, 'avg_pnl': 900, 'tier': 'HIGH',
        'min_pos': POSITION_SIZE_HIGH_MIN, 'max_pos': POSITION_SIZE_HIGH_MAX
    },
    'Very High VIX Falling': {
        'win_rate': 0.68, 'avg_pnl': 900, 'tier': 'HIGH',
        'min_pos': POSITION_SIZE_HIGH_MIN, 'max_pos': POSITION_SIZE_HIGH_MAX
    },
    
    # MEDIUM TIER (5-8 contracts)
    'High VIX (Reversed)': {
        'win_rate': 0.65, 'avg_pnl': 800, 'tier': 'MEDIUM',
        'min_pos': POSITION_SIZE_MEDIUM_MIN, 'max_pos': POSITION_SIZE_MEDIUM_MAX
    },
    'Normal-High VIX (Reversed)': {
        'win_rate': 0.60, 'avg_pnl': 700, 'tier': 'MEDIUM',
        'min_pos': POSITION_SIZE_MEDIUM_MIN, 'max_pos': POSITION_SIZE_MEDIUM_MAX
    }
}

# Confidence score thresholds
CONFIDENCE_SCORE_EXTREME_LOW = 0.85
CONFIDENCE_SCORE_EXTREME_HIGH = 0.82
CONFIDENCE_SCORE_VERY_LOW = 0.80
CONFIDENCE_SCORE_VERY_HIGH = 0.78
CONFIDENCE_SCORE_VIX_RISING = 0.78
CONFIDENCE_SCORE_VIX_FALLING = 0.68
CONFIDENCE_SCORE_LOW = 0.75
CONFIDENCE_SCORE_HIGH = 0.72
CONFIDENCE_SCORE_NORMAL_HIGH = 0.60
CONFIDENCE_SCORE_HIGH_REVERSED = 0.65

# Win rate caps
MAX_WIN_RATE = 0.90

# ============================================================================
# OPTIONS DATA PARAMETERS
# ============================================================================

# Options filtering criteria
OPTIONS_MIN_PRICE = 0.50
OPTIONS_MAX_PRICE = 100.0
OPTIONS_MIN_EXPIRY_DAYS = 25
OPTIONS_MAX_EXPIRY_DAYS = 35
OPTIONS_MIN_VOLUME = 0

# Strike price parameters - MUCH FURTHER OTM
STRIKE_RANGE_PERCENTAGE = 0.15     # ±15% from SPX price (MUCH FURTHER OTM)
STRIKE_SEARCH_DAYS = 3             # Search within 3 days for options data

# OTM Strike selection ranges (FAR OTM - 15-30% OTM - OPTIMAL)
BEARISH_STRIKE_OTM_MIN = 0.70      # 30% OTM puts (extremely far OTM)
BEARISH_STRIKE_OTM_MAX = 0.85      # 15% OTM puts (very far OTM)
BULLISH_STRIKE_OTM_MIN = 1.15      # 15% OTM calls (very far OTM)
BULLISH_STRIKE_OTM_MAX = 1.30      # 30% OTM calls (extremely far OTM)

# Option pricing parameters
OPTION_PRICE_VIX_ADJUSTMENT = 0.03  # 3% per VIX point above 20
OPTION_PRICE_VIX_BASELINE = 20.0    # VIX baseline for pricing
OPTION_PRICE_MIN = 0.50             # Minimum option price

# ============================================================================
# VRP CALCULATION PARAMETERS
# ============================================================================

# VRP calculation periods
VRP_PERIODS = [10, 20, 30]
VRP_MIN_HISTORY_DAYS = 30

# Realized volatility calculation
ANNUALIZATION_FACTOR = 252
VOLATILITY_PERCENTAGE_MULTIPLIER = 100

# ============================================================================
# TRADING PARAMETERS
# ============================================================================

# Capital and risk management
STARTING_CAPITAL = 100000
SPX_MULTIPLIER = 100
MAX_CONTRACTS = 20
MIN_CONTRACTS = 1

# Holding period
DEFAULT_HOLDING_DAYS = 1
DEFAULT_TIMING_SCENARIO = 'close_to_close'

# Transaction costs
BID_ASK_SPREAD = 0.05
COMMISSION_PER_CONTRACT = 1.00
SLIPPAGE_FACTOR = 0.02

# ============================================================================
# TRADE SIMULATION PARAMETERS
# ============================================================================

# Return multipliers by tier
RETURN_MULT_EXTREME_MIN = 1.4
RETURN_MULT_EXTREME_MAX = 2.5
RETURN_MULT_VERY_HIGH_MIN = 1.3
RETURN_MULT_VERY_HIGH_MAX = 2.2
RETURN_MULT_HIGH_MIN = 1.2
RETURN_MULT_HIGH_MAX = 1.9
RETURN_MULT_MEDIUM_MIN = 1.1
RETURN_MULT_MEDIUM_MAX = 1.6

# Loss multipliers
LOSS_MULT_HIGH_CONF_MIN = 0.4
LOSS_MULT_HIGH_CONF_MAX = 0.8
LOSS_MULT_STANDARD_MIN = 0.3
LOSS_MULT_STANDARD_MAX = 0.75

# Confidence adjustments by tier
CONF_ADJ_EXTREME_MIN = 0.95
CONF_ADJ_EXTREME_MAX = 1.05
CONF_ADJ_VERY_HIGH_MIN = 0.90
CONF_ADJ_VERY_HIGH_MAX = 1.10
CONF_ADJ_HIGH_MIN = 0.85
CONF_ADJ_HIGH_MAX = 1.15
CONF_ADJ_MEDIUM_MIN = 0.80
CONF_ADJ_MEDIUM_MAX = 1.20

# ============================================================================
# DATA PATHS
# ============================================================================

# Data directories
OPTIONS_DATA_DIR = '../optionhistory'
SECURITIES_DATA_DIR = '/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities'

# VIX data files
VIX_DATA_FILES = {
    'VIX': f'{SECURITIES_DATA_DIR}/VIX_full_1day.txt',
    'VIX9D': f'{SECURITIES_DATA_DIR}/VIX9D_full_1day.txt'
}

# Output directories
REPORTS_DIR = 'reports'
TRADES_DIR = 'trades'

# ============================================================================
# DATE PARAMETERS
# ============================================================================

DEFAULT_START_DATE = "2023-05-01"
# DEFAULT_END_DATE will be set dynamically to today's date in the strategy

# ============================================================================
# DISPLAY AND FORMATTING
# ============================================================================

SEPARATOR_LENGTH = 70
PERCENTAGE_MULTIPLIER = 100

# Chart parameters
CHART_DPI = 300
CHART_FIGURE_WIDTH = 16
CHART_FIGURE_HEIGHT = 12

# ============================================================================
# FILE NAMING PATTERNS
# ============================================================================

TRADES_CSV_FILENAME = 'trades_analysis.csv'
STRATEGY_REPORT_PREFIX = 'Final_Real_Data_Strategy_Report_'
STRATEGY_CHART_PREFIX = 'final_strategy_analysis_'
