"""
Performance Report Generator for Cluster Strategy
- Tomorrow's signal generation
- Last 20 trades analysis
- Proper trade timing (enter on open day after signal, close on close day)
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from fpdf import FPDF
import warnings
warnings.filterwarnings('ignore')

sys.path.append('src')

from src.dataloader import OptionsDataLoader
from src.config import Config
from cluster_strategy_refactored import ClusterStrategy

class PerformanceReportGenerator:
    """Generate comprehensive performance reports for cluster strategy"""
    
    def __init__(self):
        self.config = Config()
        self.data_loader = OptionsDataLoader(self.config.data_path)
        self.strategy = ClusterStrategy()
        
    def generate_tomorrows_signal(self):
        """Generate trading signal for tomorrow based on latest data"""
        
        print("🔮 GENERATING TOMORROW'S SIGNAL")
        print("=" * 50)
        
        # Load latest data
        raw_data = self.data_loader.load_raw_data()
        
        # Get most recent trading date
        latest_date = raw_data['date'].max()
        print(f"📅 Latest data date: {latest_date.strftime('%Y-%m-%d')}")
        
        # Get options data for latest date
        latest_options = raw_data[raw_data['date'] == latest_date].copy()
        
        if latest_options.empty:
            print("❌ No options data available for signal generation")
            return None
        
        # Get current underlying price
        current_price = self.data_loader.extract_underlying_price(latest_options, latest_date)
        print(f"💰 Current SPX price: ${current_price:.2f}")
        
        # Get overnight drift
        overnight_drift = self.strategy.get_overnight_drift(latest_date)
        print(f"🌙 Overnight E-mini drift: {overnight_drift*100:.2f}%")
        
        # Detect option walls
        wall_data = self.strategy.detect_option_walls(latest_options, current_price)
        
        # Generate signal
        signal = self.strategy.generate_signal(wall_data, overnight_drift, current_price)
        
        # Calculate tomorrow's date (next trading day)
        tomorrow = latest_date + timedelta(days=1)
        # Skip weekends
        while tomorrow.weekday() >= 5:  # Saturday = 5, Sunday = 6
            tomorrow += timedelta(days=1)
        
        signal_summary = {
            'signal_date': latest_date,
            'trade_date': tomorrow,
            'current_price': current_price,
            'signal_type': signal['signal_type'],
            'signal_strength': signal['signal_strength'],
            'position_size': signal['position_size'],
            'rationale': signal['rationale'],
            'overnight_drift': overnight_drift,
            'wall_data': wall_data
        }
        
        print(f"\n🎯 TOMORROW'S SIGNAL ({tomorrow.strftime('%Y-%m-%d')}):")
        print(f"   Signal Type: {signal['signal_type']}")
        print(f"   Signal Strength: {signal['signal_strength']:.3f}")
        print(f"   Position Size: {signal['position_size']} contracts")
        print(f"   Rationale: {signal['rationale']}")
        print(f"   Wall Dominance: {wall_data['dominant_wall']}")
        print(f"   Call Walls: {wall_data['call_wall_count']} (strength: {wall_data['call_wall_strength']:.1f})")
        print(f"   Put Walls: {wall_data['put_wall_count']} (strength: {wall_data['put_wall_strength']:.1f})")
        
        return signal_summary
    
    def analyze_last_15_trades(self):
        """Analyze the last 15 completed trades with proper timing"""

        print("\n📊 ANALYZING LAST 15 TRADES")
        print("=" * 50)
        
        # Load trade results
        try:
            trades_df = pd.read_csv('reports/cluster_trades_refactored.csv')
            trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
            trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
        except FileNotFoundError:
            print("❌ No trade data found. Run cluster strategy first.")
            return None
        
        # Get last 15 trades
        last_15_trades = trades_df.tail(15).copy()

        print(f"📈 Analyzing {len(last_15_trades)} recent trades")
        
        # Calculate additional metrics
        last_15_trades['trade_duration'] = (last_15_trades['exit_date'] - last_15_trades['entry_date']).dt.days
        last_15_trades['pnl_per_contract'] = last_15_trades['final_pnl'] / last_15_trades['position_size']
        last_15_trades['return_pct'] = (last_15_trades['final_pnl'] /
                                       (last_15_trades['position_size'] * 100 * 30)) * 100  # Assume $30 premium

        # Performance metrics
        total_pnl = last_15_trades['final_pnl'].sum()
        win_count = (last_15_trades['final_pnl'] > 0).sum()
        loss_count = (last_15_trades['final_pnl'] <= 0).sum()
        win_rate = win_count / len(last_15_trades) * 100
        
        avg_win = last_15_trades[last_15_trades['final_pnl'] > 0]['final_pnl'].mean() if win_count > 0 else 0
        avg_loss = last_15_trades[last_15_trades['final_pnl'] <= 0]['final_pnl'].mean() if loss_count > 0 else 0

        profit_factor = abs(avg_win * win_count / (avg_loss * loss_count)) if avg_loss != 0 else float('inf')

        avg_duration = last_15_trades['trade_duration'].mean()
        max_win = last_15_trades['final_pnl'].max()
        max_loss = last_15_trades['final_pnl'].min()

        performance_summary = {
            'total_trades': len(last_15_trades),
            'total_pnl': total_pnl,
            'win_count': win_count,
            'loss_count': loss_count,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_duration': avg_duration,
            'max_win': max_win,
            'max_loss': max_loss
        }
        
        print(f"💰 Total P&L: ${total_pnl:,.0f}")
        print(f"🎯 Win Rate: {win_rate:.1f}% ({win_count}W/{loss_count}L)")
        print(f"📊 Profit Factor: {profit_factor:.2f}")
        print(f"⏱️  Average Duration: {avg_duration:.1f} days")
        print(f"🏆 Best Trade: ${max_win:,.0f}")
        print(f"💸 Worst Trade: ${max_loss:,.0f}")
        
        return last_15_trades, performance_summary
    
    def create_trade_timing_analysis(self, trades_df):
        """Analyze trade timing to ensure proper entry/exit timing"""
        
        print("\n⏰ TRADE TIMING ANALYSIS")
        print("=" * 30)
        
        # Analyze entry timing (should be day after signal)
        trades_df['signal_to_entry_days'] = (trades_df['entry_date'] - trades_df['entry_date']).dt.days  # This will be 0, but shows the concept
        
        # Analyze exit timing patterns
        exit_reasons = trades_df['exit_reason'].value_counts()
        
        print("Exit Reason Distribution:")
        for reason, count in exit_reasons.items():
            pct = count / len(trades_df) * 100
            print(f"  {reason}: {count} trades ({pct:.1f}%)")
        
        # Duration analysis
        duration_stats = trades_df['days_held'].describe()
        print(f"\nTrade Duration Statistics:")
        print(f"  Average: {duration_stats['mean']:.1f} days")
        print(f"  Median: {duration_stats['50%']:.1f} days")
        print(f"  Min: {duration_stats['min']:.0f} days")
        print(f"  Max: {duration_stats['max']:.0f} days")
        
        return {
            'exit_reasons': exit_reasons.to_dict(),
            'duration_stats': duration_stats.to_dict()
        }
    
    def generate_performance_charts(self, trades_df, save_path='reports/performance_charts.png'):
        """Generate performance visualization charts"""
        
        print(f"\n📈 GENERATING PERFORMANCE CHARTS")
        print("=" * 40)
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Cluster Strategy Performance Analysis - Last 20 Trades', fontsize=16, fontweight='bold')
        
        # 1. Cumulative P&L
        trades_df['cumulative_pnl'] = trades_df['final_pnl'].cumsum()
        ax1.plot(range(len(trades_df)), trades_df['cumulative_pnl'], 'b-', linewidth=2, marker='o', markersize=4)
        ax1.set_title('Cumulative P&L', fontweight='bold')
        ax1.set_xlabel('Trade Number')
        ax1.set_ylabel('Cumulative P&L ($)')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        
        # 2. Trade P&L Distribution
        colors = ['green' if pnl > 0 else 'red' for pnl in trades_df['final_pnl']]
        ax2.bar(range(len(trades_df)), trades_df['final_pnl'], color=colors, alpha=0.7)
        ax2.set_title('Individual Trade P&L', fontweight='bold')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('P&L ($)')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 3. Signal Type Distribution
        signal_counts = trades_df['signal_type'].value_counts()
        ax3.pie(signal_counts.values, labels=signal_counts.index, autopct='%1.1f%%', startangle=90)
        ax3.set_title('Signal Type Distribution', fontweight='bold')
        
        # 4. Exit Reason Distribution
        exit_counts = trades_df['exit_reason'].value_counts()
        ax4.pie(exit_counts.values, labels=exit_counts.index, autopct='%1.1f%%', startangle=90)
        ax4.set_title('Exit Reason Distribution', fontweight='bold')
        
        plt.tight_layout()
        
        # Save chart
        os.makedirs('reports', exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Charts saved to: {save_path}")
        
        return save_path

    def create_pdf_report(self, report_data, save_path='reports/cluster_strategy_report.pdf'):
        """Create comprehensive PDF report with equity curve and trade analysis"""

        print(f"\n📄 GENERATING PDF REPORT")
        print("=" * 40)

        from fpdf import FPDF
        import matplotlib.pyplot as plt

        # Create PDF
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)

        # Title
        pdf.cell(0, 10, 'CLUSTER STRATEGY PERFORMANCE REPORT', 0, 1, 'C')
        pdf.set_font('Arial', '', 10)
        pdf.cell(0, 5, f'Generated: {report_data["generation_time"].strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'C')
        pdf.ln(10)

        # Tomorrow's Signal Section
        if report_data['tomorrows_signal']:
            signal = report_data['tomorrows_signal']
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 8, "TOMORROW'S TRADING SIGNAL", 0, 1)
            pdf.set_font('Arial', '', 10)

            pdf.cell(0, 6, f"Trade Date: {signal['trade_date'].strftime('%Y-%m-%d')}", 0, 1)
            pdf.cell(0, 6, f"Signal Type: {signal['signal_type']}", 0, 1)
            pdf.cell(0, 6, f"Signal Strength: {signal['signal_strength']:.3f}", 0, 1)
            pdf.cell(0, 6, f"Position Size: {signal['position_size']} contracts", 0, 1)
            pdf.cell(0, 6, f"Current Price: ${signal['current_price']:.2f}", 0, 1)
            pdf.cell(0, 6, f"Overnight Drift: {signal['overnight_drift']*100:.2f}%", 0, 1)
            pdf.cell(0, 6, f"Rationale: {signal['rationale']}", 0, 1)
            pdf.ln(5)

        # Performance Summary
        perf = report_data['performance_summary']
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 8, "LAST 15 TRADES PERFORMANCE", 0, 1)
        pdf.set_font('Arial', '', 10)

        pdf.cell(0, 6, f"Total Trades: {perf['total_trades']}", 0, 1)
        pdf.cell(0, 6, f"Total P&L: ${perf['total_pnl']:,.0f}", 0, 1)
        pdf.cell(0, 6, f"Win Rate: {perf['win_rate']:.1f}% ({perf['win_count']}W/{perf['loss_count']}L)", 0, 1)
        pdf.cell(0, 6, f"Profit Factor: {perf['profit_factor']:.2f}", 0, 1)
        pdf.cell(0, 6, f"Average Win: ${perf['avg_win']:,.0f}", 0, 1)
        pdf.cell(0, 6, f"Average Loss: ${perf['avg_loss']:,.0f}", 0, 1)
        pdf.cell(0, 6, f"Best Trade: ${perf['max_win']:,.0f}", 0, 1)
        pdf.cell(0, 6, f"Worst Trade: ${perf['max_loss']:,.0f}", 0, 1)
        pdf.ln(10)

        # Add new page for equity curve
        pdf.add_page()
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 8, "EQUITY CURVE", 0, 1)

        # Create equity curve chart
        equity_chart_path = self.create_equity_curve_chart(report_data['last_15_trades'])
        if equity_chart_path:
            pdf.image(equity_chart_path, x=10, y=30, w=190)

        # Add trade table on new page
        pdf.add_page()
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 8, "DETAILED TRADE TABLE", 0, 1)
        pdf.ln(5)

        # Trade table headers
        pdf.set_font('Arial', 'B', 8)
        pdf.cell(25, 6, 'Entry Date', 1, 0, 'C')
        pdf.cell(25, 6, 'Exit Date', 1, 0, 'C')
        pdf.cell(20, 6, 'Signal', 1, 0, 'C')
        pdf.cell(15, 6, 'Size', 1, 0, 'C')
        pdf.cell(25, 6, 'Entry Price', 1, 0, 'C')
        pdf.cell(25, 6, 'Exit Price', 1, 0, 'C')
        pdf.cell(25, 6, 'P&L ($)', 1, 0, 'C')
        pdf.cell(15, 6, 'Days', 1, 0, 'C')
        pdf.cell(35, 6, 'Exit Reason', 1, 1, 'C')

        # Trade table data
        pdf.set_font('Arial', '', 7)
        for _, trade in report_data['last_15_trades'].iterrows():
            pdf.cell(25, 5, trade['entry_date'].strftime('%Y-%m-%d'), 1, 0, 'C')
            pdf.cell(25, 5, trade['exit_date'].strftime('%Y-%m-%d'), 1, 0, 'C')
            pdf.cell(20, 5, str(trade['signal_type']), 1, 0, 'C')
            pdf.cell(15, 5, str(trade['position_size']), 1, 0, 'C')
            pdf.cell(25, 5, f"${trade['entry_price']:.2f}", 1, 0, 'C')
            pdf.cell(25, 5, f"${trade['exit_price']:.2f}", 1, 0, 'C')
            pdf.cell(25, 5, f"${trade['final_pnl']:,.0f}", 1, 0, 'C')
            pdf.cell(15, 5, str(trade['days_held']), 1, 0, 'C')
            pdf.cell(35, 5, str(trade['exit_reason'])[:12], 1, 1, 'C')

        # Save PDF
        os.makedirs('reports', exist_ok=True)
        pdf.output(save_path)

        print(f"✅ PDF report saved: {save_path}")
        return save_path

    def create_equity_curve_chart(self, trades_df, save_path='reports/equity_curve.png'):
        """Create equity curve chart for PDF"""

        # Calculate cumulative P&L
        trades_df = trades_df.copy()
        trades_df['cumulative_pnl'] = trades_df['final_pnl'].cumsum()

        # Create chart
        plt.figure(figsize=(12, 6))
        plt.plot(range(len(trades_df)), trades_df['cumulative_pnl'], 'b-', linewidth=2, marker='o', markersize=4)
        plt.title('Equity Curve - Last 15 Trades', fontsize=14, fontweight='bold')
        plt.xlabel('Trade Number')
        plt.ylabel('Cumulative P&L ($)')
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)

        # Add annotations for best and worst trades
        max_idx = trades_df['cumulative_pnl'].idxmax()
        min_idx = trades_df['cumulative_pnl'].idxmin()

        plt.annotate(f'Peak: ${trades_df.loc[max_idx, "cumulative_pnl"]:,.0f}',
                    xy=(max_idx, trades_df.loc[max_idx, 'cumulative_pnl']),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        plt.annotate(f'Trough: ${trades_df.loc[min_idx, "cumulative_pnl"]:,.0f}',
                    xy=(min_idx, trades_df.loc[min_idx, 'cumulative_pnl']),
                    xytext=(10, -20), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        return save_path

    def create_detailed_trade_table(self, trades_df):
        """Create a detailed formatted trade table"""

        print("\n📋 DETAILED TRADE TABLE")
        print("=" * 80)

        # Format the trade table
        trade_table = trades_df[['entry_date', 'exit_date', 'signal_type', 'position_size',
                                'entry_price', 'exit_price', 'final_pnl', 'days_held',
                                'exit_reason', 'rationale']].copy()

        # Format dates
        trade_table['entry_date'] = trade_table['entry_date'].dt.strftime('%Y-%m-%d')
        trade_table['exit_date'] = trade_table['exit_date'].dt.strftime('%Y-%m-%d')

        # Format prices and P&L
        trade_table['entry_price'] = trade_table['entry_price'].round(2)
        trade_table['exit_price'] = trade_table['exit_price'].round(2)
        trade_table['final_pnl'] = trade_table['final_pnl'].round(0).astype(int)

        # Rename columns for display
        trade_table.columns = ['Entry Date', 'Exit Date', 'Signal', 'Contracts',
                              'Entry Price', 'Exit Price', 'P&L ($)', 'Days',
                              'Exit Reason', 'Rationale']

        # Print formatted table
        print(trade_table.to_string(index=False, max_colwidth=30))

        return trade_table

    def generate_comprehensive_report(self):
        """Generate the complete performance report"""

        print("🎯 CLUSTER STRATEGY PERFORMANCE REPORT")
        print("=" * 60)
        print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        # 1. Generate tomorrow's signal
        tomorrows_signal = self.generate_tomorrows_signal()

        # 2. Analyze last 15 trades
        last_15_trades, performance_summary = self.analyze_last_15_trades()

        if last_15_trades is None:
            print("❌ Cannot generate report without trade data")
            return

        # 3. Trade timing analysis
        timing_analysis = self.create_trade_timing_analysis(last_15_trades)

        # 4. Generate performance charts
        chart_path = self.generate_performance_charts(last_15_trades)

        # 5. Create detailed trade table
        trade_table = self.create_detailed_trade_table(last_15_trades)

        # 6. Save comprehensive report
        report_data = {
            'generation_time': datetime.now(),
            'tomorrows_signal': tomorrows_signal,
            'performance_summary': performance_summary,
            'timing_analysis': timing_analysis,
            'last_15_trades': last_15_trades,
            'trade_table': trade_table,
            'chart_path': chart_path
        }

        # Save to CSV for easy access
        trade_table.to_csv('reports/last_15_trades_detailed.csv', index=False)

        # Create PDF report
        pdf_path = self.create_pdf_report(report_data)

        # Create summary report
        self.create_summary_report(report_data)

        print(f"\n✅ COMPREHENSIVE REPORT GENERATED")
        print(f"📊 Charts: {chart_path}")
        print(f"📋 Trade Table: reports/last_15_trades_detailed.csv")
        print(f"📄 PDF Report: {pdf_path}")
        print(f"📄 Summary Report: reports/performance_summary.txt")

        return report_data

    def create_summary_report(self, report_data):
        """Create a text summary report"""

        summary_path = 'reports/performance_summary.txt'

        with open(summary_path, 'w') as f:
            f.write("CLUSTER STRATEGY PERFORMANCE REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {report_data['generation_time'].strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Tomorrow's Signal
            if report_data['tomorrows_signal']:
                signal = report_data['tomorrows_signal']
                f.write("🔮 TOMORROW'S TRADING SIGNAL\n")
                f.write("-" * 30 + "\n")
                f.write(f"Trade Date: {signal['trade_date'].strftime('%Y-%m-%d')}\n")
                f.write(f"Signal Type: {signal['signal_type']}\n")
                f.write(f"Signal Strength: {signal['signal_strength']:.3f}\n")
                f.write(f"Position Size: {signal['position_size']} contracts\n")
                f.write(f"Current Price: ${signal['current_price']:.2f}\n")
                f.write(f"Overnight Drift: {signal['overnight_drift']*100:.2f}%\n")
                f.write(f"Rationale: {signal['rationale']}\n")
                f.write(f"Wall Dominance: {signal['wall_data']['dominant_wall']}\n")
                f.write(f"Call Walls: {signal['wall_data']['call_wall_count']} (strength: {signal['wall_data']['call_wall_strength']:.1f})\n")
                f.write(f"Put Walls: {signal['wall_data']['put_wall_count']} (strength: {signal['wall_data']['put_wall_strength']:.1f})\n\n")

            # Performance Summary
            perf = report_data['performance_summary']
            f.write("📊 LAST 20 TRADES PERFORMANCE\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total Trades: {perf['total_trades']}\n")
            f.write(f"Total P&L: ${perf['total_pnl']:,.0f}\n")
            f.write(f"Win Rate: {perf['win_rate']:.1f}% ({perf['win_count']}W/{perf['loss_count']}L)\n")
            f.write(f"Profit Factor: {perf['profit_factor']:.2f}\n")
            f.write(f"Average Win: ${perf['avg_win']:,.0f}\n")
            f.write(f"Average Loss: ${perf['avg_loss']:,.0f}\n")
            f.write(f"Average Duration: {perf['avg_duration']:.1f} days\n")
            f.write(f"Best Trade: ${perf['max_win']:,.0f}\n")
            f.write(f"Worst Trade: ${perf['max_loss']:,.0f}\n\n")

            # Timing Analysis
            timing = report_data['timing_analysis']
            f.write("⏰ TRADE TIMING ANALYSIS\n")
            f.write("-" * 30 + "\n")
            f.write("Exit Reasons:\n")
            for reason, count in timing['exit_reasons'].items():
                pct = count / perf['total_trades'] * 100
                f.write(f"  {reason}: {count} trades ({pct:.1f}%)\n")

            f.write(f"\nDuration Statistics:\n")
            f.write(f"  Average: {timing['duration_stats']['mean']:.1f} days\n")
            f.write(f"  Median: {timing['duration_stats']['50%']:.1f} days\n")
            f.write(f"  Min: {timing['duration_stats']['min']:.0f} days\n")
            f.write(f"  Max: {timing['duration_stats']['max']:.0f} days\n")

        print(f"📄 Summary report saved: {summary_path}")


def main():
    """Main execution function"""

    # Create report generator
    report_gen = PerformanceReportGenerator()

    # Generate comprehensive report
    report_data = report_gen.generate_comprehensive_report()

    return report_data


if __name__ == "__main__":
    main()
