#!/usr/bin/env python3
"""
Holding Period Optimization for Enhanced VIX Options Strategy v3.2
Tests different holding periods (1-5 days) to optimize performance
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import subprocess
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class HoldingPeriodOptimizer:
    """Test different holding periods to optimize strategy performance"""
    
    def __init__(self):
        self.results = {}
        self.best_holding_period = None
        self.best_performance = None
        
    def test_holding_period(self, holding_days: int) -> dict:
        """Test a specific holding period"""
        
        print(f"\n🔧 Testing {holding_days}-day holding period...")
        print("=" * 50)
        
        # Update constants file with new holding period
        self._update_holding_period_constant(holding_days)
        
        # Run the backtest
        try:
            result = subprocess.run([
                sys.executable, 'pure_vix_options_strategy.py'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                # Parse performance from output
                performance = self._parse_performance_from_output(result.stdout)
                performance['holding_days'] = holding_days
                performance['success'] = True
                
                print(f"✅ {holding_days}-day test completed successfully")
                print(f"📊 Total Return: {performance.get('total_return', 'N/A')}%")
                print(f"📊 Win Rate: {performance.get('win_rate', 'N/A')}%")
                print(f"📊 Total Trades: {performance.get('total_trades', 'N/A')}")
                print(f"📊 Max Drawdown: {performance.get('max_drawdown', 'N/A')}%")
                
                return performance
            else:
                print(f"❌ {holding_days}-day test failed")
                print(f"Error: {result.stderr}")
                return {'holding_days': holding_days, 'success': False, 'error': result.stderr}
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {holding_days}-day test timed out")
            return {'holding_days': holding_days, 'success': False, 'error': 'Timeout'}
        except Exception as e:
            print(f"❌ {holding_days}-day test error: {str(e)}")
            return {'holding_days': holding_days, 'success': False, 'error': str(e)}
    
    def _update_holding_period_constant(self, holding_days: int):
        """Update the MAX_HOLD_DAYS constant in constants.py"""
        
        # Read the current constants file
        with open('constants.py', 'r') as f:
            content = f.read()
        
        # Replace the MAX_HOLD_DAYS value
        import re
        pattern = r'MAX_HOLD_DAYS = \d+'
        replacement = f'MAX_HOLD_DAYS = {holding_days}'
        content = re.sub(pattern, replacement, content)
        
        # Also update the current test indicator
        pattern = r'CURRENT_HOLDING_PERIOD_TEST = \d+'
        replacement = f'CURRENT_HOLDING_PERIOD_TEST = {holding_days}'
        content = re.sub(pattern, replacement, content)
        
        # Write back to file
        with open('constants.py', 'w') as f:
            f.write(content)
        
        print(f"🔧 Updated MAX_HOLD_DAYS to {holding_days}")
    
    def _parse_performance_from_output(self, output: str) -> dict:
        """Parse performance metrics from backtest output"""
        
        performance = {}
        
        try:
            lines = output.split('\n')
            for line in lines:
                if 'Total Trades:' in line:
                    performance['total_trades'] = int(line.split(':')[1].strip())
                elif 'Win Rate:' in line:
                    performance['win_rate'] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Total Return:' in line:
                    performance['total_return'] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Total P&L:' in line:
                    pnl_str = line.split(':')[1].strip().replace('$', '').replace(',', '')
                    performance['total_pnl'] = float(pnl_str)
                elif 'Final Capital:' in line:
                    capital_str = line.split(':')[1].strip().replace('$', '').replace(',', '')
                    performance['final_capital'] = float(capital_str)
                elif 'Max Drawdown:' in line:
                    performance['max_drawdown'] = float(line.split(':')[1].strip().replace('%', ''))
        except Exception as e:
            print(f"⚠️ Error parsing performance: {str(e)}")
        
        return performance
    
    def run_optimization(self) -> dict:
        """Run holding period optimization for all test periods"""
        
        print("🚀 HOLDING PERIOD OPTIMIZATION")
        print("=" * 50)
        print("Testing holding periods: 1, 2, 3, 4, 5 days")
        print("Current baseline: 3,568.9% return with 1-day holding")
        print("=" * 50)
        
        # Test each holding period
        for holding_days in HOLDING_PERIODS_TO_TEST:
            result = self.test_holding_period(holding_days)
            self.results[holding_days] = result
            
            # Track best performance
            if result.get('success', False):
                total_return = result.get('total_return', 0)
                if self.best_performance is None or total_return > self.best_performance:
                    self.best_performance = total_return
                    self.best_holding_period = holding_days
        
        # Generate summary
        self._generate_optimization_summary()
        
        return self.results
    
    def _generate_optimization_summary(self):
        """Generate optimization summary report"""
        
        print("\n📊 HOLDING PERIOD OPTIMIZATION RESULTS")
        print("=" * 60)
        
        # Create results table
        results_data = []
        for holding_days in HOLDING_PERIODS_TO_TEST:
            result = self.results.get(holding_days, {})
            if result.get('success', False):
                results_data.append({
                    'Holding Days': holding_days,
                    'Total Return (%)': f"{result.get('total_return', 0):.1f}",
                    'Win Rate (%)': f"{result.get('win_rate', 0):.1f}",
                    'Total Trades': result.get('total_trades', 0),
                    'Max Drawdown (%)': f"{result.get('max_drawdown', 0):.1f}",
                    'Final Capital ($)': f"${result.get('final_capital', 0):,.0f}"
                })
            else:
                results_data.append({
                    'Holding Days': holding_days,
                    'Total Return (%)': 'FAILED',
                    'Win Rate (%)': 'FAILED',
                    'Total Trades': 'FAILED',
                    'Max Drawdown (%)': 'FAILED',
                    'Final Capital ($)': 'FAILED'
                })
        
        # Display results table
        if results_data:
            df = pd.DataFrame(results_data)
            print(df.to_string(index=False))
        
        # Best performance summary
        if self.best_holding_period is not None:
            print(f"\n🏆 BEST PERFORMANCE:")
            print(f"   Holding Period: {self.best_holding_period} days")
            print(f"   Total Return: {self.best_performance:.1f}%")
            
            best_result = self.results[self.best_holding_period]
            print(f"   Win Rate: {best_result.get('win_rate', 0):.1f}%")
            print(f"   Total Trades: {best_result.get('total_trades', 0)}")
            print(f"   Max Drawdown: {best_result.get('max_drawdown', 0):.1f}%")
            print(f"   Final Capital: ${best_result.get('final_capital', 0):,.0f}")
        else:
            print("\n❌ No successful tests completed")
        
        # Save results to file
        self._save_results_to_file()
    
    def _save_results_to_file(self):
        """Save optimization results to file"""
        
        # Ensure reports directory exists
        os.makedirs('reports', exist_ok=True)
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f'reports/holding_period_optimization_{timestamp}.json'
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Create summary report
        summary_file = f'reports/holding_period_optimization_summary_{timestamp}.md'
        with open(summary_file, 'w') as f:
            f.write("# Holding Period Optimization Results\n\n")
            f.write(f"**Test Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## Results Summary\n\n")
            
            for holding_days in HOLDING_PERIODS_TO_TEST:
                result = self.results.get(holding_days, {})
                f.write(f"### {holding_days}-Day Holding Period\n")
                if result.get('success', False):
                    f.write(f"- **Total Return:** {result.get('total_return', 0):.1f}%\n")
                    f.write(f"- **Win Rate:** {result.get('win_rate', 0):.1f}%\n")
                    f.write(f"- **Total Trades:** {result.get('total_trades', 0)}\n")
                    f.write(f"- **Max Drawdown:** {result.get('max_drawdown', 0):.1f}%\n")
                    f.write(f"- **Final Capital:** ${result.get('final_capital', 0):,.0f}\n\n")
                else:
                    f.write("- **Status:** FAILED\n\n")
            
            if self.best_holding_period is not None:
                f.write(f"## Best Performance\n\n")
                f.write(f"**Optimal Holding Period:** {self.best_holding_period} days\n")
                f.write(f"**Best Return:** {self.best_performance:.1f}%\n\n")
        
        print(f"💾 Summary saved to: {summary_file}")

def main():
    """Main execution function"""
    
    print("🔧 Enhanced VIX Options Strategy v3.2")
    print("🎯 Holding Period Optimization Testing")
    print("=" * 60)
    
    # Create optimizer
    optimizer = HoldingPeriodOptimizer()
    
    # Run optimization
    results = optimizer.run_optimization()
    
    print("\n✅ Holding period optimization completed!")
    
    return results

if __name__ == "__main__":
    main()
