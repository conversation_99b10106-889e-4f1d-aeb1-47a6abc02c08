#!/usr/bin/env python3
"""
Improved VIX Strategy - Updated filters based on drawdown analysis
Key improvements:
1. Avoid low VIX trades (terrible 34.4% win rate)
2. Favor high VIX trades (better 46.1% win rate)
3. More selective signal generation
4. Reduce bearish bias (bearish trades losing money)
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class ImprovedVIXStrategy:
    """Improved VIX strategy with updated filters"""
    
    def __init__(self, start_date="2023-05-01", end_date=None):
        self.start_date = start_date
        self.end_date = end_date or datetime.now().strftime("%Y-%m-%d")
        self.holding_days = 1
        self.trades = []
        self.capital = STARTING_CAPITAL
        
    def load_vix_data(self):
        """Load real VIX data"""
        
        print("📊 Loading VIX data...")
        
        try:
            # Load VIX data
            vix_df = pd.read_csv(VIX_DATA_FILES['VIX'], 
                               names=['date', 'open', 'high', 'low', 'close', 'volume'],
                               parse_dates=['date'])
            vix_df = vix_df.rename(columns={'close': 'vix'})
            
            # Load VIX9D data
            vix9d_df = pd.read_csv(VIX_DATA_FILES['VIX9D'], 
                                 names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                 parse_dates=['date'])
            vix9d_df = vix9d_df.rename(columns={'close': 'vix9d'})
            
            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'vix']], 
                              vix9d_df[['date', 'vix9d']], 
                              on='date', how='inner')
            
            # Filter date range
            vix_data = vix_data[
                (vix_data['date'] >= self.start_date) & 
                (vix_data['date'] <= self.end_date)
            ].copy()
            
            # Calculate VIX momentum
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']
            vix_data['vix_momentum_direction'] = np.where(
                vix_data['vix_momentum'] > 0, 'RISING', 'FALLING'
            )
            
            print(f"✅ Loaded {len(vix_data)} VIX records from {vix_data['date'].min()} to {vix_data['date'].max()}")
            return vix_data.set_index('date')
            
        except Exception as e:
            print(f"❌ Error loading VIX data: {e}")
            return None
    
    def generate_improved_signals(self, vix_data):
        """Generate improved trading signals with updated VIX filters"""
        
        signals = []
        
        for date, row in vix_data.iterrows():
            vix = row['vix']
            vix9d = row['vix9d']
            vix_momentum = row['vix_momentum_direction']
            
            # SKIP TRADES: Avoid terrible low VIX performance
            if vix < VIX_SKIP_LOW_THRESHOLD:
                continue
                
            # SKIP TRADES: Avoid extreme high VIX
            if vix > VIX_SKIP_HIGH_THRESHOLD:
                continue
            
            # Generate signals with improved logic
            signal_direction = None
            signal_strength = 0.5
            
            # BULLISH SIGNALS (favor these - they perform better)
            if vix >= VIX_HIGH_SIGNAL:  # VIX >= 22
                signal_direction = 'BULLISH'
                signal_strength = 0.8 if vix > 25 else 0.7
                
                # Boost signal if VIX momentum is rising (fear increasing)
                if vix_momentum == 'RISING':
                    signal_strength = min(signal_strength + 0.1, 1.0)
            
            # BEARISH SIGNALS (be more selective - they lose money)
            elif vix <= VIX_LOW_SIGNAL and vix >= VIX_SKIP_LOW_THRESHOLD:  # 15 <= VIX <= 18
                # Only take bearish signals in specific conditions
                if vix_momentum == 'FALLING' and vix > 16:  # More selective
                    signal_direction = 'BEARISH'
                    signal_strength = 0.6  # Lower confidence
            
            # Add signal if generated
            if signal_direction:
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'signal_strength': signal_strength,
                    'vix': vix,
                    'vix9d': vix9d,
                    'vix_momentum': vix_momentum
                })
        
        signals_df = pd.DataFrame(signals)
        print(f"✅ Generated {len(signals_df)} improved signals")
        
        if len(signals_df) > 0:
            bullish_count = len(signals_df[signals_df['signal_direction'] == 'BULLISH'])
            bearish_count = len(signals_df[signals_df['signal_direction'] == 'BEARISH'])
            print(f"   📈 Bullish signals: {bullish_count}")
            print(f"   📉 Bearish signals: {bearish_count}")
            print(f"   🎯 Bullish/Bearish ratio: {bullish_count/bearish_count:.2f}")
        
        return signals_df
    
    def calculate_position_size(self, vix, signal_strength):
        """Calculate position size based on improved VIX regime"""
        
        # Determine VIX regime multiplier
        if vix < VIX_OPTIMAL_LOW:
            multiplier = LOW_VIX_MULTIPLIER  # 0.3 (reduce exposure)
        elif vix <= VIX_OPTIMAL_HIGH:
            multiplier = OPTIMAL_VIX_MULTIPLIER  # 1.0 (standard)
        else:
            multiplier = HIGH_VIX_MULTIPLIER  # 1.5 (increase exposure)
        
        # Apply signal strength
        multiplier *= signal_strength
        
        # Calculate position size
        risk_amount = self.capital * RISK_PER_TRADE
        position_size = max(MIN_CONTRACTS, min(MAX_CONTRACTS, int(risk_amount / 1000 * multiplier)))
        
        return position_size
    
    def simulate_option_trade(self, signal_date, signal_direction, vix, position_size):
        """Simulate option trade with simplified pricing"""
        
        # Simplified option pricing based on VIX level
        if signal_direction == 'BULLISH':
            option_type = 'c'
            # Call options: higher VIX = higher premium
            entry_price = 20 + (vix - 15) * 2
            # Simulate 1-day outcome (calls benefit from VIX decline)
            exit_price = entry_price + np.random.normal(-2, 8)  # Slight negative bias
        else:
            option_type = 'p'
            # Put options: higher VIX = higher premium
            entry_price = 20 + (vix - 15) * 2
            # Simulate 1-day outcome (puts benefit from VIX increase)
            exit_price = entry_price + np.random.normal(-1, 8)  # Slight negative bias
        
        # Ensure positive prices
        entry_price = max(entry_price, 5)
        exit_price = max(exit_price, 1)
        
        # Calculate P&L
        trade_pnl = (exit_price - entry_price) * position_size * SPX_MULTIPLIER
        
        return {
            'signal_date': signal_date,
            'entry_date': signal_date + timedelta(days=1),
            'exit_date': signal_date + timedelta(days=2),
            'signal_direction': signal_direction,
            'option_type': option_type,
            'position_size': position_size,
            'vix': vix,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'trade_pnl': trade_pnl
        }
    
    def run_improved_strategy(self):
        """Run the improved strategy"""
        
        print("🚀 IMPROVED VIX STRATEGY")
        print("=" * 50)
        print("Key improvements:")
        print("✅ Skip low VIX trades (VIX < 15)")
        print("✅ Favor high VIX trades (better performance)")
        print("✅ More selective bearish signals")
        print("✅ Improved position sizing")
        print("=" * 50)
        
        # Load VIX data
        vix_data = self.load_vix_data()
        if vix_data is None:
            return None
        
        # Generate improved signals
        signals_df = self.generate_improved_signals(vix_data)
        if len(signals_df) == 0:
            print("❌ No signals generated")
            return None
        
        # Simulate trades
        print("📊 Simulating trades...")
        for _, signal in signals_df.iterrows():
            position_size = self.calculate_position_size(signal['vix'], signal['signal_strength'])
            
            trade = self.simulate_option_trade(
                signal['date'],
                signal['signal_direction'],
                signal['vix'],
                position_size
            )
            
            self.trades.append(trade)
            self.capital += trade['trade_pnl']
        
        # Calculate performance
        trades_df = pd.DataFrame(self.trades)
        
        total_pnl = trades_df['trade_pnl'].sum()
        total_return = (total_pnl / STARTING_CAPITAL) * 100
        win_rate = (trades_df['trade_pnl'] > 0).mean() * 100
        
        winning_trades = trades_df[trades_df['trade_pnl'] > 0]
        losing_trades = trades_df[trades_df['trade_pnl'] < 0]
        
        avg_win = winning_trades['trade_pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['trade_pnl'].mean() if len(losing_trades) > 0 else 0
        profit_factor = abs(winning_trades['trade_pnl'].sum() / losing_trades['trade_pnl'].sum()) if len(losing_trades) > 0 else float('inf')
        
        # Calculate max drawdown
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['running_max'] = trades_df['cumulative_pnl'].expanding().max()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['running_max']
        max_drawdown = abs(trades_df['drawdown'].min() / STARTING_CAPITAL) * 100
        
        performance = {
            'total_trades': len(trades_df),
            'win_rate': win_rate,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'final_capital': self.capital,
            'max_drawdown': max_drawdown,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor
        }
        
        # Print results
        self.print_results(performance, trades_df)
        
        # Generate equity curve
        self.generate_equity_curve(trades_df)
        
        return performance
    
    def print_results(self, performance, trades_df):
        """Print strategy results"""
        
        print(f"\n✅ IMPROVED STRATEGY RESULTS")
        print("=" * 50)
        print(f"📊 Total Trades: {performance['total_trades']}")
        print(f"📊 Win Rate: {performance['win_rate']:.1f}%")
        print(f"📊 Total Return: {performance['total_return']:.1f}%")
        print(f"📊 Total P&L: ${performance['total_pnl']:,.0f}")
        print(f"📊 Final Capital: ${performance['final_capital']:,.0f}")
        print(f"📊 Max Drawdown: {performance['max_drawdown']:.1f}%")
        print(f"📊 Average Win: ${performance['avg_win']:,.0f}")
        print(f"📊 Average Loss: ${performance['avg_loss']:,.0f}")
        print(f"📊 Profit Factor: {performance['profit_factor']:.2f}")
        
        # Signal breakdown
        bullish_trades = trades_df[trades_df['signal_direction'] == 'BULLISH']
        bearish_trades = trades_df[trades_df['signal_direction'] == 'BEARISH']
        
        print(f"\n📈 Bullish Trades: {len(bullish_trades)}, Win Rate: {(bullish_trades['trade_pnl'] > 0).mean()*100:.1f}%, Avg P&L: ${bullish_trades['trade_pnl'].mean():.0f}")
        print(f"📉 Bearish Trades: {len(bearish_trades)}, Win Rate: {(bearish_trades['trade_pnl'] > 0).mean()*100:.1f}%, Avg P&L: ${bearish_trades['trade_pnl'].mean():.0f}")
    
    def generate_equity_curve(self, trades_df):
        """Generate equity curve chart"""
        
        plt.figure(figsize=(15, 8))
        
        # Equity curve
        trades_df['equity'] = STARTING_CAPITAL + trades_df['cumulative_pnl']
        plt.plot(trades_df['signal_date'], trades_df['equity'], linewidth=2, color='blue', label='Improved Strategy')
        plt.axhline(y=STARTING_CAPITAL, color='gray', linestyle='--', alpha=0.7, label='Starting Capital')
        
        plt.title('Improved VIX Strategy - Equity Curve', fontsize=14, fontweight='bold')
        plt.ylabel('Portfolio Value ($)', fontsize=12)
        plt.xlabel('Date', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Format y-axis as currency
        ax = plt.gca()
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        plt.tight_layout()
        
        # Save chart
        chart_filename = f'reports/improved_vix_strategy_equity_curve.png'
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"📊 Improved strategy equity curve saved to: {chart_filename}")
        
        plt.show()

def main():
    """Main execution function"""
    
    print("🔧 IMPROVED VIX STRATEGY - BASED ON DRAWDOWN ANALYSIS")
    print("=" * 70)
    
    # Create and run improved strategy
    strategy = ImprovedVIXStrategy()
    results = strategy.run_improved_strategy()
    
    if results:
        print("\n🎉 IMPROVED STRATEGY EXECUTION COMPLETED!")
    else:
        print("\n❌ Strategy execution failed")
    
    return results

if __name__ == "__main__":
    results = main()
