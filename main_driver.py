"""
Main Driver for JPM Options Trading Strategies
Orchestrates data loading, signal generation, backtesting, and reporting
"""
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import our modular components
from config.constants import *
from core.data_manager import DataManager
from core.strike_selector import StrikeSelector
from core.backtest_engine import BacktestEngine
from core.analytics import PerformanceAnalyzer, SignalAnalyzer
from core.report_generator import ReportGenerator

class OptionsStrategyDriver:
    """Main driver class that orchestrates the entire trading strategy pipeline"""
    
    def __init__(self):
        self.data_manager = None
        self.strike_selector = None
        self.backtest_engine = None
        self.performance_analyzer = None
        self.signal_analyzer = None
        
        print("🚀 JPM Options Trading Strategy Driver Initialized")
        print("=" * 60)
    
    def run_complete_strategy(self, strategy_type: str = 'call_spread', holding_days: int = 1) -> dict:
        """
        Run the complete strategy pipeline
        
        Args:
            strategy_type: 'call_spread' or 'single_option'
            holding_days: Number of days to hold positions
            
        Returns:
            Dictionary with all results and file paths
        """
        print(f"🎯 Running {strategy_type} strategy with {holding_days}-day holding period")
        print("=" * 60)
        
        try:
            # Step 1: Load and normalize all data
            print("📊 STEP 1: Data Loading and Normalization")
            data_dict = self._load_and_validate_data()
            if not data_dict:
                return {'error': 'Failed to load required data'}
            
            # Step 2: Generate trading signals
            print("\n🎯 STEP 2: Signal Generation")
            signals = self._generate_trading_signals(data_dict['market_data'])
            if len(signals) == 0:
                return {'error': 'No trading signals generated'}
            
            # Step 3: Run backtest
            print("\n🔄 STEP 3: Backtesting")
            backtest_results = self._run_backtest(signals, strategy_type, holding_days)
            if 'error' in backtest_results:
                return backtest_results
            
            # Step 4: Analyze performance
            print("\n📈 STEP 4: Performance Analysis")
            performance_metrics = self._analyze_performance(backtest_results['trades_df'])
            
            # Step 5: Generate reports
            print("\n📄 STEP 5: Report Generation")
            report_files = self._generate_reports(strategy_type, performance_metrics, backtest_results['trades_df'])
            
            # Compile final results
            final_results = {
                'strategy_type': strategy_type,
                'holding_days': holding_days,
                'signals_generated': len(signals),
                'trades_executed': len(backtest_results['trades_df']),
                'performance_metrics': performance_metrics,
                'trades_df': backtest_results['trades_df'],
                'report_files': report_files,
                'execution_timestamp': datetime.now().strftime(TIMESTAMP_FORMAT)
            }
            
            print("\n✅ STRATEGY EXECUTION COMPLETE!")
            print("=" * 60)
            self._print_summary(final_results)
            
            return final_results
            
        except Exception as e:
            print(f"\n❌ STRATEGY EXECUTION FAILED: {e}")
            return {'error': str(e)}
    
    def _load_and_validate_data(self) -> dict:
        """Load and validate all required data sources"""
        try:
            # Initialize data manager
            self.data_manager = DataManager()
            
            # Load all data sources
            data_dict = self.data_manager.load_all_data()
            
            # Validate data quality
            validation_results = self.data_manager.validate_data_quality()
            
            print("   Data Validation Results:")
            for check, passed in validation_results.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {check.replace('_', ' ').title()}")
            
            # Check if we have minimum required data
            required_checks = ['vix_data_loaded', 'spx_options_loaded', 'market_data_created']
            if not all(validation_results.get(check, False) for check in required_checks):
                print("   ❌ Missing required data sources")
                return None
            
            # Initialize strike selector with options data
            self.strike_selector = StrikeSelector(data_dict['spx_options_data'])
            
            print(f"   ✅ Data loading complete")
            return data_dict
            
        except Exception as e:
            print(f"   ❌ Data loading failed: {e}")
            return None
    
    def _generate_trading_signals(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals from market data"""
        try:
            # Initialize signal analyzer
            self.signal_analyzer = SignalAnalyzer(market_data)
            
            # Generate VIX-based signals
            signals = self.signal_analyzer.generate_vix_signals()
            
            # Analyze signal quality
            signal_quality = self.signal_analyzer.analyze_signal_quality(signals)
            
            print(f"   ✅ Generated {len(signals)} trading signals")
            print(f"   📊 Signal Quality Metrics:")
            print(f"      • Bullish signals: {signal_quality.get('bullish_signals', 0)}")
            print(f"      • Bearish signals: {signal_quality.get('bearish_signals', 0)}")
            print(f"      • Average confidence: {signal_quality.get('avg_confidence', 0):.2f}")
            print(f"      • High confidence signals: {signal_quality.get('high_confidence_signals', 0)}")
            
            return signals
            
        except Exception as e:
            print(f"   ❌ Signal generation failed: {e}")
            return pd.DataFrame()
    
    def _run_backtest(self, signals: pd.DataFrame, strategy_type: str, holding_days: int) -> dict:
        """Run the backtesting engine"""
        try:
            # Initialize backtest engine
            self.backtest_engine = BacktestEngine(self.data_manager, self.strike_selector)
            
            # Run backtest
            backtest_results = self.backtest_engine.run_backtest(signals, strategy_type, holding_days)
            
            if 'error' in backtest_results:
                print(f"   ❌ Backtest failed: {backtest_results['error']}")
                return backtest_results
            
            print(f"   ✅ Backtest complete")
            print(f"   📊 Backtest Results:")
            print(f"      • Total trades: {backtest_results.get('total_trades', 0)}")
            print(f"      • Win rate: {backtest_results.get('win_rate', 0):.1f}%")
            print(f"      • Total return: {backtest_results.get('total_return', 0):.1f}%")
            print(f"      • Max drawdown: {backtest_results.get('max_drawdown', 0):.1f}%")
            
            return backtest_results
            
        except Exception as e:
            print(f"   ❌ Backtest failed: {e}")
            return {'error': str(e)}
    
    def _analyze_performance(self, trades_df: pd.DataFrame) -> dict:
        """Analyze strategy performance"""
        try:
            # Initialize performance analyzer
            self.performance_analyzer = PerformanceAnalyzer(trades_df)
            
            # Calculate comprehensive metrics
            performance_metrics = self.performance_analyzer.calculate_comprehensive_metrics()
            
            if 'error' in performance_metrics:
                print(f"   ❌ Performance analysis failed: {performance_metrics['error']}")
                return performance_metrics
            
            print(f"   ✅ Performance analysis complete")
            print(f"   📊 Key Metrics:")
            print(f"      • Profit factor: {performance_metrics.get('profit_factor', 0):.2f}")
            print(f"      • Sharpe ratio: {performance_metrics.get('sharpe_ratio', 0):.2f}")
            print(f"      • Recovery factor: {performance_metrics.get('recovery_factor', 0):.2f}")
            
            return performance_metrics
            
        except Exception as e:
            print(f"   ❌ Performance analysis failed: {e}")
            return {'error': str(e)}
    
    def _generate_reports(self, strategy_type: str, performance_metrics: dict, trades_df: pd.DataFrame) -> dict:
        """Generate all reports and exports"""
        try:
            # Determine strategy name
            strategy_name = CALL_SPREAD_STRATEGY_NAME if strategy_type == 'call_spread' else VRP_STRATEGY_NAME
            
            # Initialize report generator
            report_generator = ReportGenerator(strategy_name, performance_metrics, trades_df)
            
            # Generate comprehensive report
            report_files = report_generator.generate_comprehensive_report()
            
            print(f"   ✅ Report generation complete")
            print(f"   📄 Generated files:")
            for file_type, file_path in report_files.items():
                print(f"      • {file_type}: {file_path}")
            
            return report_files
            
        except Exception as e:
            print(f"   ❌ Report generation failed: {e}")
            return {'error': str(e)}
    
    def _print_summary(self, results: dict):
        """Print executive summary of results"""
        metrics = results.get('performance_metrics', {})
        
        print(f"📊 EXECUTIVE SUMMARY - {results['strategy_type'].upper()} STRATEGY")
        print("-" * 60)
        print(f"🎯 Total Return: {metrics.get('total_return', 0):.1f}%")
        print(f"🏆 Win Rate: {metrics.get('win_rate', 0):.1f}%")
        print(f"📈 Total Trades: {metrics.get('total_trades', 0)}")
        print(f"💰 Final Capital: ${metrics.get('final_capital', 0):,.0f}")
        print(f"📉 Max Drawdown: {metrics.get('max_drawdown', 0):.1f}%")
        print(f"⚖️ Profit Factor: {metrics.get('profit_factor', 0):.2f}")
        print(f"📊 Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
        print("-" * 60)

def main():
    """Main execution function"""
    # Initialize driver
    driver = OptionsStrategyDriver()
    
    # Run call spread strategy
    call_spread_results = driver.run_complete_strategy(
        strategy_type='call_spread',
        holding_days=1
    )
    
    # Run single option strategy
    single_option_results = driver.run_complete_strategy(
        strategy_type='single_option',
        holding_days=1
    )
    
    print("\n🎉 ALL STRATEGIES COMPLETED!")
    return {
        'call_spread': call_spread_results,
        'single_option': single_option_results
    }

if __name__ == "__main__":
    results = main()
