#!/usr/bin/env python3
"""
Performance Visualizations Module
Creates comprehensive charts and graphs for strategy performance analysis
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class PerformanceVisualizations:
    """Generate comprehensive performance visualization charts"""
    
    def __init__(self, trades_df, performance_metrics):
        self.trades_df = trades_df.copy()
        self.performance_metrics = performance_metrics
        self.setup_data()
        
    def setup_data(self):
        """Setup and prepare data for visualizations"""
        
        # Ensure date columns are datetime
        if 'signal_date' in self.trades_df.columns:
            self.trades_df['signal_date'] = pd.to_datetime(self.trades_df['signal_date'])
        
        # Calculate cumulative metrics
        self.trades_df['cumulative_pnl'] = self.trades_df['trade_pnl'].cumsum()
        self.trades_df['equity'] = STARTING_CAPITAL + self.trades_df['cumulative_pnl']
        self.trades_df['running_max'] = self.trades_df['cumulative_pnl'].expanding().max()
        self.trades_df['drawdown'] = self.trades_df['cumulative_pnl'] - self.trades_df['running_max']
        self.trades_df['drawdown_pct'] = (self.trades_df['drawdown'] / STARTING_CAPITAL) * PERCENTAGE_MULTIPLIER
        
        # Add month and quarter columns
        self.trades_df['month'] = self.trades_df['signal_date'].dt.to_period('M')
        self.trades_df['quarter'] = self.trades_df['signal_date'].dt.to_period('Q')
        
        # Win/loss indicators
        self.trades_df['is_winner'] = self.trades_df['trade_pnl'] > 0
        self.trades_df['win_loss'] = self.trades_df['is_winner'].map({True: 'Win', False: 'Loss'})
    
    def create_performance_metrics_dashboard(self):
        """Create comprehensive performance metrics dashboard"""
        
        fig, axes = plt.subplots(CHART_SUBPLOT_2x3[0], CHART_SUBPLOT_2x3[1], 
                                figsize=(CHART_FIGURE_WIDTH_LARGE, CHART_FIGURE_HEIGHT))
        fig.suptitle('Enhanced Reverse Strategy - Performance Metrics Dashboard', 
                    fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')
        
        # 1. Equity Curve
        ax1 = axes[0, 0]
        ax1.plot(self.trades_df['signal_date'], self.trades_df['equity'], 
                linewidth=CHART_LINE_WIDTH_THICK, color=CHART_COLOR_PROFIT, label='Strategy Equity')
        ax1.axhline(y=STARTING_CAPITAL, color=CHART_COLOR_NEUTRAL, 
                   linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='Starting Capital')
        ax1.set_title('Equity Curve', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax1.set_ylabel('Portfolio Value ($)', fontsize=CHART_YLABEL_FONTSIZE)
        ax1.grid(True, alpha=CHART_GRID_ALPHA)
        ax1.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # 2. Drawdown Analysis
        ax2 = axes[0, 1]
        ax2.fill_between(self.trades_df['signal_date'], self.trades_df['drawdown_pct'], 0,
                        color=CHART_COLOR_LOSS, alpha=CHART_BAR_ALPHA, label='Drawdown')
        ax2.set_title('Drawdown Analysis', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_ylabel('Drawdown (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)
        ax2.legend(fontsize=CHART_LEGEND_FONTSIZE)
        
        # 3. Rolling Win Rate
        ax3 = axes[0, 2]
        rolling_window = 20
        rolling_win_rate = self.trades_df['is_winner'].rolling(window=rolling_window).mean() * PERCENTAGE_MULTIPLIER
        ax3.plot(self.trades_df['signal_date'], rolling_win_rate, 
                linewidth=CHART_LINE_WIDTH, color=CHART_COLOR_PRIMARY, label=f'{rolling_window}-Trade Rolling Win Rate')
        ax3.axhline(y=50, color=CHART_COLOR_NEUTRAL, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='50% Breakeven')
        ax3.set_title('Rolling Win Rate', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_ylabel('Win Rate (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)
        ax3.legend(fontsize=CHART_LEGEND_FONTSIZE)
        
        # 4. Position Size Distribution
        ax4 = axes[1, 0]
        position_counts = self.trades_df['position_size'].value_counts().sort_index()
        bars = ax4.bar(position_counts.index, position_counts.values, 
                      color=CHART_COLOR_SECONDARY, alpha=CHART_BAR_ALPHA)
        ax4.set_title('Position Size Distribution', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax4.set_xlabel('Position Size (Contracts)', fontsize=CHART_XLABEL_FONTSIZE)
        ax4.set_ylabel('Number of Trades', fontsize=CHART_YLABEL_FONTSIZE)
        ax4.grid(True, alpha=CHART_GRID_ALPHA)
        
        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(height)}', ha='center', va='bottom', fontsize=CHART_TICK_FONTSIZE)
        
        # 5. P&L Distribution
        ax5 = axes[1, 1]
        wins = self.trades_df[self.trades_df['is_winner']]['trade_pnl']
        losses = self.trades_df[~self.trades_df['is_winner']]['trade_pnl']
        
        ax5.hist(wins, bins=20, alpha=CHART_BAR_ALPHA, color=CHART_COLOR_SUCCESS, label='Wins', density=True)
        ax5.hist(losses, bins=20, alpha=CHART_BAR_ALPHA, color=CHART_COLOR_LOSS, label='Losses', density=True)
        ax5.axvline(x=0, color=CHART_COLOR_NEUTRAL, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='Breakeven')
        ax5.set_title('P&L Distribution', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax5.set_xlabel('Trade P&L ($)', fontsize=CHART_XLABEL_FONTSIZE)
        ax5.set_ylabel('Density', fontsize=CHART_YLABEL_FONTSIZE)
        ax5.grid(True, alpha=CHART_GRID_ALPHA)
        ax5.legend(fontsize=CHART_LEGEND_FONTSIZE)
        
        # 6. Confidence vs Performance
        ax6 = axes[1, 2]
        confidence_bins = pd.cut(self.trades_df['confidence_score'], bins=5)
        confidence_performance = self.trades_df.groupby(confidence_bins).agg({
            'trade_pnl': 'mean',
            'is_winner': 'mean'
        })
        
        ax6_twin = ax6.twinx()
        bars = ax6.bar(range(len(confidence_performance)), confidence_performance['trade_pnl'], 
                      color=CHART_COLOR_ACCENT, alpha=CHART_BAR_ALPHA, label='Avg P&L')
        line = ax6_twin.plot(range(len(confidence_performance)), 
                           confidence_performance['is_winner'] * PERCENTAGE_MULTIPLIER,
                           color=CHART_COLOR_WARNING, marker='o', linewidth=CHART_LINE_WIDTH, 
                           label='Win Rate')
        
        ax6.set_title('Confidence vs Performance', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax6.set_xlabel('Confidence Level', fontsize=CHART_XLABEL_FONTSIZE)
        ax6.set_ylabel('Average P&L ($)', fontsize=CHART_YLABEL_FONTSIZE)
        ax6_twin.set_ylabel('Win Rate (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax6.grid(True, alpha=CHART_GRID_ALPHA)
        
        # Set x-axis labels for confidence bins
        bin_labels = [f'{interval.left:.2f}-{interval.right:.2f}' for interval in confidence_performance.index]
        ax6.set_xticks(range(len(bin_labels)))
        ax6.set_xticklabels(bin_labels, rotation=45, fontsize=CHART_TICK_FONTSIZE)
        
        plt.tight_layout()
        
        # Save chart
        chart_filename = f'{REPORTS_DIR}/{PERFORMANCE_METRICS_FILENAME}'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 Performance metrics dashboard saved to: {chart_filename}")
        
        return chart_filename
    
    def create_vix_condition_analysis(self):
        """Create VIX condition analysis charts"""
        
        fig, axes = plt.subplots(2, 2, figsize=(CHART_FIGURE_WIDTH, CHART_FIGURE_HEIGHT))
        fig.suptitle('VIX Condition Analysis', fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')
        
        # 1. Performance by VIX Condition
        ax1 = axes[0, 0]
        condition_stats = self.trades_df.groupby('condition').agg({
            'trade_pnl': ['count', 'mean', 'sum'],
            'is_winner': 'mean'
        }).round(2)
        
        condition_stats.columns = ['Count', 'Avg_PnL', 'Total_PnL', 'Win_Rate']
        condition_stats['Win_Rate'] *= PERCENTAGE_MULTIPLIER
        
        bars = ax1.bar(range(len(condition_stats)), condition_stats['Total_PnL'], 
                      color=CHART_COLOR_PRIMARY, alpha=CHART_BAR_ALPHA)
        ax1.set_title('Total P&L by VIX Condition', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax1.set_ylabel('Total P&L ($)', fontsize=CHART_YLABEL_FONTSIZE)
        ax1.grid(True, alpha=CHART_GRID_ALPHA)
        
        # Rotate x-axis labels
        ax1.set_xticks(range(len(condition_stats)))
        ax1.set_xticklabels(condition_stats.index, rotation=45, ha='right', fontsize=CHART_TICK_FONTSIZE)
        
        # Add value labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'${height:,.0f}', ha='center', va='bottom', fontsize=CHART_TICK_FONTSIZE)
        
        # 2. Win Rate by VIX Condition
        ax2 = axes[0, 1]
        bars = ax2.bar(range(len(condition_stats)), condition_stats['Win_Rate'], 
                      color=CHART_COLOR_SUCCESS, alpha=CHART_BAR_ALPHA)
        ax2.axhline(y=50, color=CHART_COLOR_NEUTRAL, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='50% Breakeven')
        ax2.set_title('Win Rate by VIX Condition', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_ylabel('Win Rate (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)
        ax2.legend(fontsize=CHART_LEGEND_FONTSIZE)
        
        ax2.set_xticks(range(len(condition_stats)))
        ax2.set_xticklabels(condition_stats.index, rotation=45, ha='right', fontsize=CHART_TICK_FONTSIZE)
        
        # Add value labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=CHART_TICK_FONTSIZE)
        
        # 3. VIX Level Distribution
        ax3 = axes[1, 0]
        ax3.hist(self.trades_df['vix'], bins=20, color=CHART_COLOR_SECONDARY, alpha=CHART_BAR_ALPHA, edgecolor='black')
        ax3.axvline(x=VIX_LOW_THRESHOLD, color=CHART_COLOR_WARNING, linestyle='--', label=f'Low VIX ({VIX_LOW_THRESHOLD})')
        ax3.axvline(x=VIX_LOW_NORMAL_LOW, color=CHART_COLOR_LOSS, linestyle='--', label=f'Skip Range ({VIX_LOW_NORMAL_LOW}-{VIX_LOW_NORMAL_HIGH})')
        ax3.axvline(x=VIX_LOW_NORMAL_HIGH, color=CHART_COLOR_LOSS, linestyle='--')
        ax3.axvline(x=VIX_VERY_HIGH_LOW, color=CHART_COLOR_SUCCESS, linestyle='--', label=f'Very High VIX ({VIX_VERY_HIGH_LOW})')
        ax3.set_title('VIX Level Distribution', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_xlabel('VIX Level', fontsize=CHART_XLABEL_FONTSIZE)
        ax3.set_ylabel('Number of Trades', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)
        ax3.legend(fontsize=CHART_LEGEND_FONTSIZE)
        
        # 4. Average Position Size by VIX Condition
        ax4 = axes[1, 1]
        avg_position = self.trades_df.groupby('condition')['position_size'].mean()
        bars = ax4.bar(range(len(avg_position)), avg_position.values, 
                      color=CHART_COLOR_ACCENT, alpha=CHART_BAR_ALPHA)
        ax4.axhline(y=MAX_CONTRACTS, color=CHART_COLOR_WARNING, linestyle='--', 
                   alpha=CHART_AXHLINE_ALPHA, label=f'Max Contracts ({MAX_CONTRACTS})')
        ax4.set_title('Average Position Size by Condition', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax4.set_ylabel('Average Position Size', fontsize=CHART_YLABEL_FONTSIZE)
        ax4.grid(True, alpha=CHART_GRID_ALPHA)
        ax4.legend(fontsize=CHART_LEGEND_FONTSIZE)
        
        ax4.set_xticks(range(len(avg_position)))
        ax4.set_xticklabels(avg_position.index, rotation=45, ha='right', fontsize=CHART_TICK_FONTSIZE)
        
        # Add value labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{height:.1f}', ha='center', va='bottom', fontsize=CHART_TICK_FONTSIZE)
        
        plt.tight_layout()
        
        # Save chart
        chart_filename = f'{REPORTS_DIR}/{VIX_ANALYSIS_FILENAME}'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 VIX condition analysis saved to: {chart_filename}")
        
        return chart_filename

    def create_monthly_performance_analysis(self):
        """Create monthly performance breakdown charts"""

        fig, axes = plt.subplots(2, 2, figsize=(CHART_FIGURE_WIDTH, CHART_FIGURE_HEIGHT))
        fig.suptitle('Monthly Performance Analysis', fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')

        # Calculate monthly statistics
        monthly_stats = self.trades_df.groupby('month').agg({
            'trade_pnl': ['count', 'sum', 'mean'],
            'is_winner': 'mean',
            'position_size': 'mean',
            'confidence_score': 'mean'
        }).round(2)

        monthly_stats.columns = ['Trade_Count', 'Total_PnL', 'Avg_PnL', 'Win_Rate', 'Avg_Position', 'Avg_Confidence']
        monthly_stats['Win_Rate'] *= PERCENTAGE_MULTIPLIER

        # Convert month index to strings for plotting
        month_labels = [str(month) for month in monthly_stats.index]
        x_pos = range(len(monthly_stats))

        # 1. Monthly P&L
        ax1 = axes[0, 0]
        colors = [CHART_COLOR_PROFIT if pnl > 0 else CHART_COLOR_LOSS for pnl in monthly_stats['Total_PnL']]
        bars = ax1.bar(x_pos, monthly_stats['Total_PnL'], color=colors, alpha=CHART_BAR_ALPHA)
        ax1.axhline(y=0, color=CHART_COLOR_NEUTRAL, linestyle='-', alpha=CHART_AXHLINE_ALPHA)
        ax1.set_title('Monthly Total P&L', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax1.set_ylabel('Total P&L ($)', fontsize=CHART_YLABEL_FONTSIZE)
        ax1.grid(True, alpha=CHART_GRID_ALPHA)
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(month_labels, rotation=45, fontsize=CHART_TICK_FONTSIZE)

        # 2. Monthly Win Rate
        ax2 = axes[0, 1]
        bars = ax2.bar(x_pos, monthly_stats['Win_Rate'], color=CHART_COLOR_SUCCESS, alpha=CHART_BAR_ALPHA)
        ax2.axhline(y=50, color=CHART_COLOR_NEUTRAL, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='50% Breakeven')
        ax2.set_title('Monthly Win Rate', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_ylabel('Win Rate (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)
        ax2.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(month_labels, rotation=45, fontsize=CHART_TICK_FONTSIZE)

        # 3. Monthly Trade Count
        ax3 = axes[1, 0]
        bars = ax3.bar(x_pos, monthly_stats['Trade_Count'], color=CHART_COLOR_PRIMARY, alpha=CHART_BAR_ALPHA)
        ax3.set_title('Monthly Trade Count', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_ylabel('Number of Trades', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(month_labels, rotation=45, fontsize=CHART_TICK_FONTSIZE)

        # 4. Monthly Average Position Size
        ax4 = axes[1, 1]
        bars = ax4.bar(x_pos, monthly_stats['Avg_Position'], color=CHART_COLOR_ACCENT, alpha=CHART_BAR_ALPHA)
        ax4.axhline(y=MAX_CONTRACTS, color=CHART_COLOR_WARNING, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'Max Contracts ({MAX_CONTRACTS})')
        ax4.set_title('Monthly Avg Position Size', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax4.set_ylabel('Average Position Size', fontsize=CHART_YLABEL_FONTSIZE)
        ax4.grid(True, alpha=CHART_GRID_ALPHA)
        ax4.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels(month_labels, rotation=45, fontsize=CHART_TICK_FONTSIZE)

        plt.tight_layout()

        # Save chart
        chart_filename = f'{REPORTS_DIR}/{MONTHLY_PERFORMANCE_FILENAME}'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 Monthly performance analysis saved to: {chart_filename}")

        return chart_filename

    def create_risk_analysis_charts(self):
        """Create risk analysis and drawdown charts"""

        fig, axes = plt.subplots(2, 2, figsize=(CHART_FIGURE_WIDTH, CHART_FIGURE_HEIGHT))
        fig.suptitle('Risk Analysis Dashboard', fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')

        # 1. Drawdown Timeline
        ax1 = axes[0, 0]
        ax1.fill_between(self.trades_df['signal_date'], self.trades_df['drawdown_pct'], 0,
                        color=CHART_COLOR_LOSS, alpha=CHART_BAR_ALPHA, label='Drawdown')
        ax1.axhline(y=-10, color=CHART_COLOR_WARNING, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='-10% Warning')
        ax1.axhline(y=-20, color=CHART_COLOR_LOSS, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='-20% Critical')
        ax1.set_title('Drawdown Timeline', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax1.set_ylabel('Drawdown (%)', fontsize=CHART_YLABEL_FONTSIZE)
        ax1.grid(True, alpha=CHART_GRID_ALPHA)
        ax1.legend(fontsize=CHART_LEGEND_FONTSIZE)

        # 2. Risk-Return Scatter (Position Size vs P&L)
        ax2 = axes[0, 1]
        colors = [CHART_COLOR_PROFIT if pnl > 0 else CHART_COLOR_LOSS for pnl in self.trades_df['trade_pnl']]
        scatter = ax2.scatter(self.trades_df['position_size'], self.trades_df['trade_pnl'],
                            c=colors, alpha=CHART_SCATTER_ALPHA, s=CHART_SCATTER_SIZE)
        ax2.axhline(y=0, color=CHART_COLOR_NEUTRAL, linestyle='--', alpha=CHART_AXHLINE_ALPHA)
        ax2.set_title('Position Size vs P&L', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_xlabel('Position Size (Contracts)', fontsize=CHART_XLABEL_FONTSIZE)
        ax2.set_ylabel('Trade P&L ($)', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)

        # 3. Consecutive Wins/Losses
        ax3 = axes[1, 0]

        # Calculate consecutive wins/losses
        consecutive_results = []
        current_streak = 1
        current_type = self.trades_df.iloc[0]['win_loss']

        for i in range(1, len(self.trades_df)):
            if self.trades_df.iloc[i]['win_loss'] == current_type:
                current_streak += 1
            else:
                consecutive_results.append((current_type, current_streak))
                current_type = self.trades_df.iloc[i]['win_loss']
                current_streak = 1
        consecutive_results.append((current_type, current_streak))

        # Plot consecutive results
        win_streaks = [streak for result_type, streak in consecutive_results if result_type == 'Win']
        loss_streaks = [streak for result_type, streak in consecutive_results if result_type == 'Loss']

        ax3.hist([win_streaks, loss_streaks], bins=range(1, max(max(win_streaks, default=1), max(loss_streaks, default=1)) + 2),
                alpha=CHART_BAR_ALPHA, color=[CHART_COLOR_SUCCESS, CHART_COLOR_LOSS],
                label=['Win Streaks', 'Loss Streaks'])
        ax3.set_title('Consecutive Win/Loss Distribution', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_xlabel('Consecutive Trades', fontsize=CHART_XLABEL_FONTSIZE)
        ax3.set_ylabel('Frequency', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)
        ax3.legend(fontsize=CHART_LEGEND_FONTSIZE)

        # 4. Rolling Sharpe Ratio (approximation)
        ax4 = axes[1, 1]
        rolling_window = 30
        rolling_returns = self.trades_df['trade_pnl'].rolling(window=rolling_window).mean()
        rolling_std = self.trades_df['trade_pnl'].rolling(window=rolling_window).std()
        rolling_sharpe = rolling_returns / rolling_std

        ax4.plot(self.trades_df['signal_date'], rolling_sharpe,
                linewidth=CHART_LINE_WIDTH, color=CHART_COLOR_PRIMARY, label=f'{rolling_window}-Trade Rolling Sharpe')
        ax4.axhline(y=1, color=CHART_COLOR_SUCCESS, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='Good (>1)')
        ax4.axhline(y=0, color=CHART_COLOR_NEUTRAL, linestyle='--', alpha=CHART_AXHLINE_ALPHA, label='Breakeven')
        ax4.set_title('Rolling Sharpe Ratio', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax4.set_ylabel('Sharpe Ratio', fontsize=CHART_YLABEL_FONTSIZE)
        ax4.grid(True, alpha=CHART_GRID_ALPHA)
        ax4.legend(fontsize=CHART_LEGEND_FONTSIZE)

        plt.tight_layout()

        # Save chart
        chart_filename = f'{REPORTS_DIR}/{DRAWDOWN_ANALYSIS_FILENAME}'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 Risk analysis charts saved to: {chart_filename}")

        return chart_filename

    def create_all_visualizations(self):
        """Create all performance visualization charts"""

        print("📊 Generating comprehensive performance visualizations...")

        chart_files = []

        # Create all visualization charts
        chart_files.append(self.create_performance_metrics_dashboard())
        chart_files.append(self.create_vix_condition_analysis())
        chart_files.append(self.create_monthly_performance_analysis())
        chart_files.append(self.create_risk_analysis_charts())

        print(f"✅ Generated {len(chart_files)} comprehensive visualization charts")

        return chart_files
