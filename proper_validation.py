#!/usr/bin/env python3

import pandas as pd
import sys
import os

print("🔍 PROPER TRADE VALIDATION")
print("=" * 50)

# Load the trade data
trades_df = pd.read_csv('trades/call_spread_trades.csv')
print(f"✅ Loaded {len(trades_df)} trades from CSV")

# Load the options data using the same method as the strategy
sys.path.append('.')
from final_strategy_clean import FinalRealDataStrategy

strategy = FinalRealDataStrategy()
market_data = strategy.load_market_data_with_real_vrp()
options_data = strategy.spx_options_data

print(f"✅ Loaded {len(options_data):,} options records")
print(f"Options date range: {options_data['date'].min()} to {options_data['date'].max()}")

# Add calculated fields
options_data['days_to_expiry'] = (options_data['expiry_date'] - options_data['date']).dt.days
options_data['mid_price'] = (options_data['Bid Price'] + options_data['Ask Price']) / 2

print(f"\n🎯 VALIDATING FIRST 10 TRADES:")
print("-" * 80)

validation_results = []

for i in range(min(10, len(trades_df))):
    trade = trades_df.iloc[i]
    
    print(f"\n📊 TRADE #{i+1} - {trade['entry_date']}")
    print(f"   Short: Strike {trade['short_strike']:.0f}, Price ${trade['short_entry_price']:.2f}")
    print(f"   Long:  Strike {trade['long_strike']:.0f}, Price ${trade['long_entry_price']:.2f}")
    
    # Find exact matches in options data
    entry_date = pd.to_datetime(trade['entry_date'])
    
    # Find short leg options
    short_matches = options_data[
        (options_data['date'] == entry_date) &
        (options_data['Strike'] == trade['short_strike']) &
        (options_data['Call/Put'] == 'c')
    ].copy()
    
    # Find long leg options  
    long_matches = options_data[
        (options_data['date'] == entry_date) &
        (options_data['Strike'] == trade['long_strike']) &
        (options_data['Call/Put'] == 'c')
    ].copy()
    
    print(f"   Found {len(short_matches)} short options, {len(long_matches)} long options")
    
    # Check for exact price matches
    short_exact = None
    long_exact = None
    
    if len(short_matches) > 0:
        short_price_diffs = abs(short_matches['mid_price'] - trade['short_entry_price'])
        min_diff_idx = short_price_diffs.idxmin()
        short_exact = short_matches.loc[min_diff_idx]
        short_diff = short_price_diffs.loc[min_diff_idx]
        
        print(f"   Short best match: ${short_exact['mid_price']:.2f} ({short_exact['days_to_expiry']} DTE) - diff: ${short_diff:.2f}")
    
    if len(long_matches) > 0:
        long_price_diffs = abs(long_matches['mid_price'] - trade['long_entry_price'])
        min_diff_idx = long_price_diffs.idxmin()
        long_exact = long_matches.loc[min_diff_idx]
        long_diff = long_price_diffs.loc[min_diff_idx]
        
        print(f"   Long best match:  ${long_exact['mid_price']:.2f} ({long_exact['days_to_expiry']} DTE) - diff: ${long_diff:.2f}")
    
    # Determine validation result
    if short_exact is not None and long_exact is not None:
        if short_diff < 0.01 and long_diff < 0.01:
            result = "🎯 PERFECT"
        elif short_diff < 1.0 and long_diff < 1.0:
            result = "✅ EXCELLENT"
        elif short_diff < 5.0 and long_diff < 5.0:
            result = "✅ GOOD"
        else:
            result = "❌ POOR"
    else:
        result = "❌ NO DATA"
    
    print(f"   Result: {result}")
    
    validation_results.append({
        'trade_num': i+1,
        'entry_date': trade['entry_date'],
        'short_diff': short_diff if short_exact is not None else 999,
        'long_diff': long_diff if long_exact is not None else 999,
        'result': result
    })

print(f"\n📊 VALIDATION SUMMARY:")
print("-" * 40)

perfect = sum(1 for r in validation_results if "PERFECT" in r['result'])
excellent = sum(1 for r in validation_results if "EXCELLENT" in r['result'])
good = sum(1 for r in validation_results if "GOOD" in r['result'])
poor = sum(1 for r in validation_results if "POOR" in r['result'] or "NO DATA" in r['result'])

total = len(validation_results)
success_rate = (perfect + excellent + good) / total * 100

print(f"🎯 Perfect matches: {perfect}/{total} ({perfect/total*100:.1f}%)")
print(f"✅ Excellent matches: {excellent}/{total} ({excellent/total*100:.1f}%)")
print(f"✅ Good matches: {good}/{total} ({good/total*100:.1f}%)")
print(f"❌ Poor/No matches: {poor}/{total} ({poor/total*100:.1f}%)")
print(f"📊 Overall success rate: {success_rate:.1f}%")

if success_rate < 80:
    print(f"\n❌ VALIDATION FAILED - Success rate too low!")
    print(f"   Expected: >80% exact/close matches")
    print(f"   Actual: {success_rate:.1f}%")
    print(f"   This indicates a bug in the strategy or data loading")
else:
    print(f"\n✅ VALIDATION PASSED - Strategy is working correctly!")
    print(f"   Success rate: {success_rate:.1f}% meets expectations")
