#!/usr/bin/env python3
"""
Real Holding Period Optimizer for Enhanced VIX Options Strategy
Tests different holding periods (1-5 days) using REAL historical options data
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import subprocess
import json
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *
from pure_vix_options_strategy import EnhancedVIXOptionsStrategyV3

class RealHoldingPeriodOptimizer:
    """Test different holding periods using real options data"""
    
    def __init__(self):
        self.results = {}
        self.best_holding_period = None
        self.best_performance = None
        
    def test_holding_period(self, holding_days: int) -> dict:
        """Test a specific holding period with real options data"""
        
        print(f"\n🔧 Testing {holding_days}-day holding period with REAL OPTIONS DATA...")
        print("=" * 60)
        
        # Update constants file with new holding period
        self._update_holding_period_constant(holding_days)
        
        # Run the real options backtest
        try:
            strategy = EnhancedVIXOptionsStrategyV3()
            
            # Run backtest with real data - using smaller date range for faster testing
            results = strategy.run_backtest(
                start_date='2012-01-03',
                end_date='2012-03-31'  # 3 months of data for faster testing
            )
            
            # Parse performance metrics
            performance = self._extract_performance_metrics(strategy)
            performance['holding_days'] = holding_days
            performance['success'] = True
            
            print(f"✅ {holding_days}-day test completed successfully")
            print(f"📊 Total Return: {performance.get('total_return', 'N/A'):.1f}%")
            print(f"📊 Win Rate: {performance.get('win_rate', 'N/A'):.1f}%")
            print(f"📊 Total Trades: {performance.get('total_trades', 'N/A')}")
            print(f"📊 Max Drawdown: {performance.get('max_drawdown', 'N/A'):.1f}%")
            
            return performance
            
        except Exception as e:
            print(f"❌ {holding_days}-day test failed: {str(e)}")
            return {'holding_days': holding_days, 'success': False, 'error': str(e)}
    
    def _update_holding_period_constant(self, holding_days: int):
        """Update the MAX_HOLD_DAYS constant in constants.py"""
        
        # Read the current constants file
        with open('constants.py', 'r') as f:
            content = f.read()
        
        # Replace the MAX_HOLD_DAYS value
        import re
        pattern = r'MAX_HOLD_DAYS = \d+'
        replacement = f'MAX_HOLD_DAYS = {holding_days}'
        content = re.sub(pattern, replacement, content)
        
        # Also update the current test indicator
        pattern = r'CURRENT_HOLDING_PERIOD_TEST = \d+'
        replacement = f'CURRENT_HOLDING_PERIOD_TEST = {holding_days}'
        content = re.sub(pattern, replacement, content)
        
        # Write back to file
        with open('constants.py', 'w') as f:
            f.write(content)
        
        print(f"🔧 Updated MAX_HOLD_DAYS to {holding_days}")
    
    def _extract_performance_metrics(self, strategy) -> dict:
        """Extract performance metrics from strategy results"""
        
        if not strategy.trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_return': 0,
                'total_pnl': 0,
                'final_capital': strategy.current_capital,
                'max_drawdown': 0
            }
        
        trades_df = pd.DataFrame(strategy.trades)
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['trade_pnl'] > 0])
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        total_pnl = trades_df['trade_pnl'].sum()
        total_return = ((strategy.current_capital - strategy.starting_capital) / strategy.starting_capital * 100)
        
        # Calculate drawdown
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['peak'] = trades_df['cumulative_pnl'].cummax()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['peak']
        max_drawdown = abs(trades_df['drawdown'].min() / strategy.starting_capital * 100) if len(trades_df) > 0 else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'final_capital': strategy.current_capital,
            'max_drawdown': max_drawdown
        }
    
    def run_optimization(self) -> dict:
        """Run holding period optimization for all test periods"""
        
        print("🚀 REAL OPTIONS HOLDING PERIOD OPTIMIZATION")
        print("=" * 60)
        print("Testing holding periods: 1, 2, 3, 4, 5 days")
        print("Using REAL historical SPX options data (2012-2013)")
        print("=" * 60)
        
        # Test each holding period
        for holding_days in HOLDING_PERIODS_TO_TEST:
            result = self.test_holding_period(holding_days)
            self.results[holding_days] = result
            
            # Track best performance
            if result.get('success', False):
                total_return = result.get('total_return', -999)
                if self.best_performance is None or total_return > self.best_performance:
                    self.best_performance = total_return
                    self.best_holding_period = holding_days
        
        # Generate summary
        self._generate_optimization_summary()
        
        return self.results
    
    def _generate_optimization_summary(self):
        """Generate optimization summary report"""
        
        print("\n📊 REAL OPTIONS HOLDING PERIOD OPTIMIZATION RESULTS")
        print("=" * 70)
        
        # Create results table
        results_data = []
        for holding_days in HOLDING_PERIODS_TO_TEST:
            result = self.results.get(holding_days, {})
            if result.get('success', False):
                results_data.append({
                    'Holding Days': holding_days,
                    'Total Return (%)': f"{result.get('total_return', 0):.1f}",
                    'Win Rate (%)': f"{result.get('win_rate', 0):.1f}",
                    'Total Trades': result.get('total_trades', 0),
                    'Max Drawdown (%)': f"{result.get('max_drawdown', 0):.1f}",
                    'Final Capital ($)': f"${result.get('final_capital', 0):,.0f}"
                })
            else:
                results_data.append({
                    'Holding Days': holding_days,
                    'Total Return (%)': 'FAILED',
                    'Win Rate (%)': 'FAILED',
                    'Total Trades': 'FAILED',
                    'Max Drawdown (%)': 'FAILED',
                    'Final Capital ($)': 'FAILED'
                })
        
        # Display results table
        if results_data:
            df = pd.DataFrame(results_data)
            print(df.to_string(index=False))
        
        # Best performance summary
        if self.best_holding_period is not None:
            print(f"\n🏆 BEST PERFORMANCE (REAL DATA):")
            print(f"   Holding Period: {self.best_holding_period} days")
            print(f"   Total Return: {self.best_performance:.1f}%")
            
            best_result = self.results[self.best_holding_period]
            print(f"   Win Rate: {best_result.get('win_rate', 0):.1f}%")
            print(f"   Total Trades: {best_result.get('total_trades', 0)}")
            print(f"   Max Drawdown: {best_result.get('max_drawdown', 0):.1f}%")
            print(f"   Final Capital: ${best_result.get('final_capital', 0):,.0f}")
        else:
            print("\n❌ No successful tests completed")
        
        # Save results to file
        self._save_results_to_file()
        
        # Comparison with fake system
        print(f"\n🎯 REAL vs FAKE COMPARISON:")
        print(f"FAKE System: 3,568.9% return, 0% drawdown (IMPOSSIBLE)")
        if self.best_performance is not None:
            best_result = self.results[self.best_holding_period]
            print(f"REAL System: {self.best_performance:.1f}% return, {best_result.get('max_drawdown', 0):.1f}% drawdown (REALISTIC)")
        else:
            print(f"REAL System: Failed to complete tests")
    
    def _save_results_to_file(self):
        """Save optimization results to file"""
        
        # Ensure reports directory exists
        os.makedirs('reports', exist_ok=True)
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f'reports/real_holding_period_optimization_{timestamp}.json'
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Create summary report
        summary_file = f'reports/real_holding_period_optimization_summary_{timestamp}.md'
        with open(summary_file, 'w') as f:
            f.write("# Real Options Holding Period Optimization Results\n\n")
            f.write(f"**Test Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("**Data Source:** Real historical SPX options data (2012-2013)\n\n")
            f.write("## Results Summary\n\n")
            
            for holding_days in HOLDING_PERIODS_TO_TEST:
                result = self.results.get(holding_days, {})
                f.write(f"### {holding_days}-Day Holding Period\n")
                if result.get('success', False):
                    f.write(f"- **Total Return:** {result.get('total_return', 0):.1f}%\n")
                    f.write(f"- **Win Rate:** {result.get('win_rate', 0):.1f}%\n")
                    f.write(f"- **Total Trades:** {result.get('total_trades', 0)}\n")
                    f.write(f"- **Max Drawdown:** {result.get('max_drawdown', 0):.1f}%\n")
                    f.write(f"- **Final Capital:** ${result.get('final_capital', 0):,.0f}\n\n")
                else:
                    f.write("- **Status:** FAILED\n\n")
            
            if self.best_holding_period is not None:
                f.write(f"## Best Performance\n\n")
                f.write(f"**Optimal Holding Period:** {self.best_holding_period} days\n")
                f.write(f"**Best Return:** {self.best_performance:.1f}%\n\n")
                
                f.write(f"## Real vs Fake Comparison\n\n")
                f.write(f"- **FAKE System:** 3,568.9% return, 0% drawdown (IMPOSSIBLE)\n")
                best_result = self.results[self.best_holding_period]
                f.write(f"- **REAL System:** {self.best_performance:.1f}% return, {best_result.get('max_drawdown', 0):.1f}% drawdown (REALISTIC)\n\n")
        
        print(f"💾 Summary saved to: {summary_file}")

def main():
    """Main execution function"""
    
    print("🔧 Enhanced VIX Options Strategy v3.2")
    print("🎯 REAL OPTIONS Holding Period Optimization Testing")
    print("=" * 70)
    
    # Create optimizer
    optimizer = RealHoldingPeriodOptimizer()
    
    # Run optimization
    results = optimizer.run_optimization()
    
    print("\n✅ Real options holding period optimization completed!")
    
    return results

if __name__ == "__main__":
    main()
