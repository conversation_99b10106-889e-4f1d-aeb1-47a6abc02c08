#!/usr/bin/env python3
"""
Real Options Backtesting System for Enhanced VIX Options Strategy
Uses actual historical SPX options data for realistic backtesting
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from real_options_data_loader import RealOptionsDataLoader
from constants import *
from config import *

class RealOptionsBacktester:
    """Real options backtesting using historical data"""
    
    def __init__(self, start_date: str = "2012-01-03", end_date: str = "2012-12-31"):
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.end_date = datetime.strptime(end_date, '%Y-%m-%d')
        self.options_loader = RealOptionsDataLoader()
        
        # Trading state
        self.current_capital = 100000  # $100k starting capital
        self.trades = []
        self.daily_performance = []
        
        # Load market data (VIX, etc.)
        self.market_data = self._load_market_data()
        
    def _load_market_data(self) -> pd.DataFrame:
        """Load VIX and other market data"""
        
        # For now, use the existing SPX data as a proxy
        # In a real implementation, you'd load actual VIX data
        print("📊 Loading market data...")
        
        # Get available dates from options data
        available_dates = self.options_loader.get_available_dates(self.start_date, self.end_date)
        
        market_data = []
        for date in available_dates:
            spx_price = self.options_loader.get_underlying_price(date)
            if spx_price:
                # Simulate VIX data (in real implementation, load actual VIX)
                vix = np.random.normal(20, 5)  # Placeholder
                vix9d = vix + np.random.normal(0, 2)  # Placeholder
                
                market_data.append({
                    'date': date,
                    'spx_close': spx_price,
                    'vix': max(10, vix),
                    'vix9d': max(10, vix9d),
                    'vix_momentum': np.random.choice(['RISING', 'FALLING'])  # Placeholder
                })
        
        df = pd.DataFrame(market_data)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        print(f"✅ Loaded market data for {len(df)} trading days")
        return df
    
    def _generate_signal(self, date: datetime, market_row: pd.Series) -> Dict:
        """Generate trading signal (simplified for testing)"""
        
        vix = market_row['vix']
        
        # Simple VIX-based signal
        if vix < 15:
            signal_direction = 'BEARISH'  # Low VIX, expect volatility increase
            signal_strength = 0.8
            strategy_type = 'long_puts'
        elif vix > 25:
            signal_direction = 'BULLISH'  # High VIX, expect volatility decrease
            signal_strength = 0.7
            strategy_type = 'long_calls'
        else:
            signal_direction = 'BEARISH'  # Default to puts
            signal_strength = 0.6
            strategy_type = 'long_puts'
        
        return {
            'signal_direction': signal_direction,
            'signal_strength': signal_strength,
            'strategy_type': strategy_type,
            'vix': vix,
            'vix9d': market_row['vix9d'],
            'vix_momentum': market_row['vix_momentum']
        }
    
    def _execute_real_trade(self, signal_date: datetime, signal: Dict) -> Optional[Dict]:
        """Execute a real trade using historical options data"""
        
        # Entry date is next trading day
        entry_date = self._get_next_trading_day(signal_date)
        if not entry_date:
            return None
        
        # Get underlying price on entry date
        underlying_price = self.options_loader.get_underlying_price(entry_date)
        if not underlying_price:
            return None
        
        # Determine option type
        option_type = 'p' if 'put' in signal['strategy_type'] else 'c'
        
        # Find best available option
        option_info = self.options_loader.find_best_strike(
            entry_date, underlying_price, option_type,
            target_expiry_days=30, otm_distance=0.02  # 2% OTM
        )
        
        if not option_info:
            return None
        
        strike, expiry_date, entry_price = option_info
        
        # Calculate position size
        position_size = self._calculate_position_size(signal, entry_price)
        
        # Calculate exit date
        exit_date = self._calculate_exit_date(entry_date, expiry_date)
        
        # Get exit price
        exit_price = self.options_loader.get_option_price(
            exit_date, strike, option_type, expiry_date
        )
        
        if exit_price is None:
            # Option expired or no data - assume worthless
            exit_price = 0.01
        
        # Calculate P&L
        price_diff = exit_price - entry_price
        total_pnl = price_diff * position_size * 100  # SPX contract multiplier
        
        # Update capital
        self.current_capital += total_pnl
        
        # Create trade record
        trade_record = {
            'signal_date': signal_date,
            'entry_date': entry_date,
            'exit_date': exit_date,
            'signal_direction': signal['signal_direction'],
            'signal_strength': signal['signal_strength'],
            'strategy_type': signal['strategy_type'],
            'position_size': position_size,
            'underlying_price': underlying_price,
            'vix': signal['vix'],
            'vix9d': signal['vix9d'],
            'vix_momentum': signal['vix_momentum'],
            'option_type': option_type,
            'strike_price': strike,
            'expiry_date': expiry_date,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'trade_pnl': total_pnl,
            'current_capital': self.current_capital,
            'trade_return': total_pnl / (entry_price * position_size * 100) if entry_price > 0 else 0
        }
        
        return trade_record
    
    def _get_next_trading_day(self, date: datetime) -> Optional[datetime]:
        """Get next available trading day"""
        
        available_dates = self.options_loader.get_available_dates(
            date + timedelta(days=1), 
            date + timedelta(days=10)
        )
        
        return available_dates[0] if available_dates else None
    
    def _calculate_position_size(self, signal: Dict, entry_price: float) -> int:
        """Calculate position size based on signal and available capital"""
        
        # Simple position sizing - risk 2% of capital per trade
        risk_amount = self.current_capital * 0.02
        max_loss_per_contract = entry_price * 100  # SPX contract multiplier
        
        if max_loss_per_contract > 0:
            position_size = int(risk_amount / max_loss_per_contract)
            return max(1, min(position_size, 10))  # Min 1, max 10 contracts
        
        return 1
    
    def _calculate_exit_date(self, entry_date: datetime, expiry_date: datetime) -> datetime:
        """Calculate exit date based on holding period"""
        
        # Exit after MAX_HOLD_DAYS or before expiry
        target_exit = entry_date + timedelta(days=MAX_HOLD_DAYS)
        
        # Don't hold past expiry
        if target_exit >= expiry_date:
            target_exit = expiry_date - timedelta(days=1)
        
        # Find actual trading day
        available_dates = self.options_loader.get_available_dates(
            target_exit - timedelta(days=2),
            target_exit + timedelta(days=2)
        )
        
        # Return closest available date
        if available_dates:
            return min(available_dates, key=lambda x: abs((x - target_exit).days))
        
        return target_exit
    
    def run_backtest(self) -> Dict:
        """Run the real options backtest"""
        
        print(f"🚀 Starting Real Options Backtest")
        print(f"📅 Period: {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")
        print(f"💰 Initial Capital: ${self.current_capital:,.0f}")
        print("=" * 60)
        
        # Validate data availability
        validation = self.options_loader.validate_data_availability(self.start_date, self.end_date)
        print(f"📊 Data Coverage: {validation['date_range_coverage']:.1%}")
        print(f"📊 Available Dates: {validation['available_dates']}")
        
        if validation['date_range_coverage'] < 0.5:
            print("⚠️ Warning: Low data coverage may affect results")
        
        # Run backtest
        total_signals = 0
        successful_trades = 0
        
        for idx, market_row in self.market_data.iterrows():
            signal_date = market_row['date'].to_pydatetime()
            
            # Generate signal
            signal = self._generate_signal(signal_date, market_row)
            total_signals += 1
            
            # Execute trade
            trade_record = self._execute_real_trade(signal_date, signal)
            
            if trade_record:
                self.trades.append(trade_record)
                successful_trades += 1
                
                if len(self.trades) % 10 == 0:
                    print(f"📊 Executed {len(self.trades)} trades, Capital: ${self.current_capital:,.0f}")
        
        # Calculate performance metrics
        performance = self._calculate_performance()
        
        print(f"\n✅ Backtest completed!")
        print(f"📊 Total Signals: {total_signals}")
        print(f"📊 Successful Trades: {successful_trades}")
        print(f"📊 Execution Rate: {successful_trades/total_signals:.1%}")
        
        return performance
    
    def _calculate_performance(self) -> Dict:
        """Calculate performance metrics"""
        
        if not self.trades:
            return {}
        
        trades_df = pd.DataFrame(self.trades)
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['trade_pnl'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = trades_df['trade_pnl'].sum()
        total_return = (self.current_capital - 100000) / 100000
        
        avg_win = trades_df[trades_df['trade_pnl'] > 0]['trade_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['trade_pnl'] < 0]['trade_pnl'].mean() if (total_trades - winning_trades) > 0 else 0
        
        # Calculate drawdown
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['peak'] = trades_df['cumulative_pnl'].cummax()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['peak']
        max_drawdown = trades_df['drawdown'].min() / 100000 if len(trades_df) > 0 else 0
        
        performance = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': win_rate * 100,
            'total_pnl': total_pnl,
            'total_return': total_return * 100,
            'final_capital': self.current_capital,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_drawdown': abs(max_drawdown) * 100,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
        }
        
        return performance
    
    def save_results(self, filename: str = None):
        """Save backtest results"""
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'reports/real_options_backtest_{timestamp}.csv'
        
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_df.to_csv(filename, index=False)
            print(f"💾 Results saved to: {filename}")

def main():
    """Run real options backtest"""
    
    # Test with a small date range first
    backtester = RealOptionsBacktester(
        start_date="2012-01-03",
        end_date="2012-03-31"
    )
    
    # Run backtest
    performance = backtester.run_backtest()
    
    # Display results
    if performance:
        print(f"\n📊 REAL OPTIONS BACKTEST RESULTS")
        print("=" * 50)
        print(f"Total Trades: {performance['total_trades']}")
        print(f"Win Rate: {performance['win_rate']:.1f}%")
        print(f"Total Return: {performance['total_return']:.1f}%")
        print(f"Total P&L: ${performance['total_pnl']:,.0f}")
        print(f"Final Capital: ${performance['final_capital']:,.0f}")
        print(f"Max Drawdown: {performance['max_drawdown']:.1f}%")
        print(f"Avg Win: ${performance['avg_win']:,.0f}")
        print(f"Avg Loss: ${performance['avg_loss']:,.0f}")
        print(f"Profit Factor: {performance['profit_factor']:.2f}")
    
    # Save results
    backtester.save_results()
    
    print(f"\n🎯 COMPARISON:")
    print(f"FAKE System: 3,568.9% return, 0% drawdown (IMPOSSIBLE)")
    print(f"REAL System: {performance.get('total_return', 0):.1f}% return, {performance.get('max_drawdown', 0):.1f}% drawdown (REALISTIC)")

if __name__ == "__main__":
    main()
