#!/usr/bin/env python3
"""
Real Options Trading Strategy - 2023-05-01 to Present
Using actual VIX data and real options data only
1-day holding period with equity curve generation
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *
from real_options_backtester import RealOptionsBacktester

class RealStrategy2023Present:
    """Real strategy using actual data from 2023-05-01 to present"""
    
    def __init__(self):
        self.start_date = "2023-05-01"
        self.end_date = datetime.now().strftime("%Y-%m-%d")
        self.holding_days = 1  # Optimal holding period
        
    def run_strategy(self):
        """Run the real strategy with actual data"""
        
        print("🚀 REAL OPTIONS STRATEGY - 2023-05-01 TO PRESENT")
        print("=" * 60)
        print(f"Start Date: {self.start_date}")
        print(f"End Date: {self.end_date}")
        print(f"Holding Period: {self.holding_days} day")
        print("Using REAL VIX data and REAL options data only")
        print("=" * 60)
        
        # Create backtester with real data
        backtester = RealOptionsBacktester(
            start_date=self.start_date,
            end_date=self.end_date,
            holding_days=self.holding_days
        )
        
        # Run backtest
        print("📊 Loading real options data...")
        performance = backtester.run_backtest()
        
        if not performance:
            print("❌ Strategy failed to run")
            return None
            
        # Generate equity curve
        self.generate_equity_curve(backtester)
        
        # Print results
        self.print_results(performance, backtester)
        
        # Save detailed results
        self.save_results(backtester, performance)
        
        return performance
    
    def generate_equity_curve(self, backtester):
        """Generate equity curve chart"""
        
        print("📈 Generating equity curve...")
        
        if not backtester.trades:
            print("⚠️ No trades to plot")
            return
            
        # Create equity curve data
        trades_df = pd.DataFrame(backtester.trades)
        trades_df['date'] = pd.to_datetime(trades_df['entry_date'])
        trades_df = trades_df.sort_values('date')
        
        # Calculate cumulative P&L
        trades_df['cumulative_pnl'] = trades_df['trade_pnl'].cumsum()
        trades_df['equity'] = STARTING_CAPITAL + trades_df['cumulative_pnl']
        
        # Create the plot
        plt.figure(figsize=(15, 10))
        
        # Main equity curve
        plt.subplot(2, 1, 1)
        plt.plot(trades_df['date'], trades_df['equity'], linewidth=2, color='blue', label='Strategy Equity')
        plt.axhline(y=STARTING_CAPITAL, color='gray', linestyle='--', alpha=0.7, label='Starting Capital')
        plt.title(f'Real Options Strategy Equity Curve\n{self.start_date} to {self.end_date}', fontsize=14, fontweight='bold')
        plt.ylabel('Portfolio Value ($)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Format y-axis as currency
        ax = plt.gca()
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # Trade P&L distribution
        plt.subplot(2, 1, 2)
        plt.bar(trades_df['date'], trades_df['trade_pnl'], 
                color=['green' if x > 0 else 'red' for x in trades_df['trade_pnl']], 
                alpha=0.7, width=1)
        plt.title('Individual Trade P&L', fontsize=12, fontweight='bold')
        plt.ylabel('Trade P&L ($)', fontsize=12)
        plt.xlabel('Date', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Format y-axis as currency
        ax = plt.gca()
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        plt.tight_layout()
        
        # Save the chart
        chart_filename = f'reports/equity_curve_{self.start_date}_to_{self.end_date.replace("-", "")}.png'
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"📊 Equity curve saved to: {chart_filename}")
        
        plt.show()
    
    def print_results(self, performance, backtester):
        """Print strategy results"""
        
        print(f"\n✅ STRATEGY RESULTS")
        print("=" * 50)
        print(f"📊 Total Trades: {performance['total_trades']}")
        print(f"📊 Win Rate: {performance['win_rate']:.1f}%")
        print(f"📊 Total Return: {performance['total_return']:.1f}%")
        print(f"📊 Total P&L: ${performance['total_pnl']:,.0f}")
        print(f"📊 Final Capital: ${performance['final_capital']:,.0f}")
        print(f"📊 Max Drawdown: {performance['max_drawdown']:.1f}%")
        print(f"📊 Average Win: ${performance['avg_win']:,.0f}")
        print(f"📊 Average Loss: ${performance['avg_loss']:,.0f}")
        print(f"📊 Profit Factor: {performance['profit_factor']:.2f}")
        
        if backtester.trades:
            # Calculate additional metrics
            trades_df = pd.DataFrame(backtester.trades)
            winning_trades = len(trades_df[trades_df['trade_pnl'] > 0])
            losing_trades = len(trades_df[trades_df['trade_pnl'] < 0])
            
            print(f"📊 Winning Trades: {winning_trades}")
            print(f"📊 Losing Trades: {losing_trades}")
            
            if len(trades_df) > 0:
                best_trade = trades_df['trade_pnl'].max()
                worst_trade = trades_df['trade_pnl'].min()
                print(f"📊 Best Trade: ${best_trade:,.0f}")
                print(f"📊 Worst Trade: ${worst_trade:,.0f}")
    
    def save_results(self, backtester, performance):
        """Save detailed results to files"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save trades
        if backtester.trades:
            trades_df = pd.DataFrame(backtester.trades)
            trades_filename = f'reports/real_strategy_trades_{timestamp}.csv'
            trades_df.to_csv(trades_filename, index=False)
            print(f"💾 Trades saved to: {trades_filename}")
        
        # Save performance summary
        performance_filename = f'reports/real_strategy_performance_{timestamp}.csv'
        performance_df = pd.DataFrame([performance])
        performance_df.to_csv(performance_filename, index=False)
        print(f"💾 Performance saved to: {performance_filename}")

def main():
    """Main execution function"""
    
    print("🔧 REAL OPTIONS STRATEGY - NO SYNTHETIC DATA")
    print("✅ Using actual VIX data from your files")
    print("✅ Using real historical options data")
    print("✅ 1-day holding period (optimal)")
    print("=" * 70)
    
    # Create and run strategy
    strategy = RealStrategy2023Present()
    results = strategy.run_strategy()
    
    if results:
        print("\n🎉 STRATEGY EXECUTION COMPLETED!")
        print("📁 Results and equity curve saved to reports/ directory")
    else:
        print("\n❌ Strategy execution failed")
    
    return results

if __name__ == "__main__":
    results = main()
