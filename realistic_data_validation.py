#!/usr/bin/env python3
"""
Realistic Data Validation for VRP Strategy
Validates that we're using real market data and realistic option prices
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class RealisticDataValidator:
    """Validate real market data and realistic option pricing"""
    
    def __init__(self, start_date=DEFAULT_START_DATE, end_date=DEFAULT_END_DATE):
        self.start_date = start_date
        self.end_date = end_date
        
        # Realistic option pricing parameters
        self.realistic_option_price_range = (0.50, 50.00)  # $0.50 to $50.00
        self.realistic_vix_range = (9.0, 80.0)  # VIX range 9-80
        self.realistic_spx_range = (2000, 6000)  # SPX range 2000-6000
        
        # Market data validation flags
        self.using_real_vix_data = False
        self.using_real_spx_data = False
        self.using_real_options_data = False
        
    def validate_vix_data_source(self):
        """Validate VIX data source and quality"""
        
        print("🔍 VALIDATING VIX DATA SOURCE")
        print("=" * 50)
        
        try:
            # Check if VIX data files exist
            vix_file_exists = os.path.exists(VIX_DATA_FILES['VIX'])
            vix9d_file_exists = os.path.exists(VIX_DATA_FILES['VIX9D'])
            
            print(f"📁 VIX file exists: {vix_file_exists}")
            print(f"📁 VIX9D file exists: {vix9d_file_exists}")
            
            if vix_file_exists and vix9d_file_exists:
                # Load and validate VIX data
                vix_df = pd.read_csv(VIX_DATA_FILES['VIX'], 
                                   names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                   parse_dates=['date'])
                
                vix9d_df = pd.read_csv(VIX_DATA_FILES['VIX9D'], 
                                     names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                     parse_dates=['date'])
                
                # Validate VIX data quality
                vix_values = vix_df['close']
                vix9d_values = vix9d_df['close']
                
                print(f"\n📊 VIX DATA QUALITY:")
                print(f"   VIX records: {len(vix_df)}")
                print(f"   VIX9D records: {len(vix9d_df)}")
                print(f"   VIX range: {vix_values.min():.2f} - {vix_values.max():.2f}")
                print(f"   VIX9D range: {vix9d_values.min():.2f} - {vix9d_values.max():.2f}")
                print(f"   Date range: {vix_df['date'].min()} to {vix_df['date'].max()}")
                
                # Check for realistic VIX values
                realistic_vix = (vix_values >= self.realistic_vix_range[0]) & (vix_values <= self.realistic_vix_range[1])
                realistic_vix9d = (vix9d_values >= self.realistic_vix_range[0]) & (vix9d_values <= self.realistic_vix_range[1])
                
                print(f"\n✅ VALIDATION RESULTS:")
                print(f"   Realistic VIX values: {realistic_vix.sum()}/{len(vix_values)} ({realistic_vix.mean()*100:.1f}%)")
                print(f"   Realistic VIX9D values: {realistic_vix9d.sum()}/{len(vix9d_values)} ({realistic_vix9d.mean()*100:.1f}%)")
                
                if realistic_vix.mean() > 0.95 and realistic_vix9d.mean() > 0.95:
                    self.using_real_vix_data = True
                    print(f"   🎯 REAL VIX DATA CONFIRMED")
                else:
                    print(f"   ⚠️ VIX data may contain unrealistic values")
                
                return vix_df, vix9d_df
                
            else:
                print(f"❌ VIX data files not found")
                print(f"   VIX file: {VIX_DATA_FILES['VIX']}")
                print(f"   VIX9D file: {VIX_DATA_FILES['VIX9D']}")
                return None, None
                
        except Exception as e:
            print(f"❌ Error validating VIX data: {e}")
            return None, None
    
    def validate_spx_data_source(self):
        """Validate SPX data source for VRP calculation"""
        
        print(f"\n🔍 VALIDATING SPX DATA SOURCE")
        print("=" * 50)
        
        # Check for real SPX data files
        spx_files = [
            'data/spx_data.csv',
            'data/SPX.csv',
            'data/spx.csv',
            '../data/spx_data.csv',
            '/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/SPX.txt'
        ]
        
        real_spx_file = None
        for file_path in spx_files:
            if os.path.exists(file_path):
                real_spx_file = file_path
                break
        
        if real_spx_file:
            try:
                print(f"📁 Found SPX data file: {real_spx_file}")
                
                # Try different file formats
                if file_path.endswith('.txt'):
                    spx_df = pd.read_csv(file_path, sep='\t', parse_dates=[0])
                    spx_df.columns = ['date', 'open', 'high', 'low', 'close', 'volume']
                else:
                    spx_df = pd.read_csv(file_path, parse_dates=['Date'])
                    spx_df = spx_df.rename(columns={'Date': 'date', 'Close': 'close'})
                
                # Validate SPX data quality
                spx_values = spx_df['close']
                
                print(f"\n📊 SPX DATA QUALITY:")
                print(f"   SPX records: {len(spx_df)}")
                print(f"   SPX range: {spx_values.min():.2f} - {spx_values.max():.2f}")
                print(f"   Date range: {spx_df['date'].min()} to {spx_df['date'].max()}")
                
                # Check for realistic SPX values
                realistic_spx = (spx_values >= self.realistic_spx_range[0]) & (spx_values <= self.realistic_spx_range[1])
                
                print(f"\n✅ VALIDATION RESULTS:")
                print(f"   Realistic SPX values: {realistic_spx.sum()}/{len(spx_values)} ({realistic_spx.mean()*100:.1f}%)")
                
                if realistic_spx.mean() > 0.95:
                    self.using_real_spx_data = True
                    print(f"   🎯 REAL SPX DATA CONFIRMED")
                    return spx_df
                else:
                    print(f"   ⚠️ SPX data may contain unrealistic values")
                    return spx_df
                    
            except Exception as e:
                print(f"❌ Error loading SPX data: {e}")
        
        print(f"⚠️ No real SPX data found - will use synthetic data")
        print(f"   This affects VRP calculation accuracy")
        return None
    
    def validate_options_data_source(self):
        """Check for real historical options data"""
        
        print(f"\n🔍 VALIDATING OPTIONS DATA SOURCE")
        print("=" * 50)
        
        # Check for real options data
        options_dirs = [
            'optionhistory',
            '../optionhistory',
            '/Users/<USER>/Downloads/jpm_collar_strategy/optionhistory'
        ]
        
        real_options_data = False
        options_files_found = []
        
        for options_dir in options_dirs:
            if os.path.exists(options_dir):
                print(f"📁 Found options directory: {options_dir}")
                
                # Look for options files
                for root, dirs, files in os.walk(options_dir):
                    for file in files:
                        if 'spx_complete' in file.lower() and file.endswith('.csv'):
                            options_files_found.append(os.path.join(root, file))
                
                if options_files_found:
                    real_options_data = True
                    break
        
        if real_options_data:
            print(f"✅ REAL OPTIONS DATA FOUND:")
            print(f"   Files found: {len(options_files_found)}")
            for file in options_files_found[:5]:  # Show first 5
                print(f"   📄 {file}")
            if len(options_files_found) > 5:
                print(f"   ... and {len(options_files_found) - 5} more files")
            
            # Try to load and validate one options file
            try:
                sample_file = options_files_found[0]
                options_df = pd.read_csv(sample_file)
                
                print(f"\n📊 SAMPLE OPTIONS DATA QUALITY:")
                print(f"   Records in sample file: {len(options_df)}")
                print(f"   Columns: {list(options_df.columns)}")
                
                # Check for realistic option prices
                if 'option_price' in options_df.columns or 'price' in options_df.columns:
                    price_col = 'option_price' if 'option_price' in options_df.columns else 'price'
                    option_prices = options_df[price_col]
                    
                    realistic_prices = (option_prices >= self.realistic_option_price_range[0]) & \
                                     (option_prices <= self.realistic_option_price_range[1])
                    
                    print(f"   Option price range: ${option_prices.min():.2f} - ${option_prices.max():.2f}")
                    print(f"   Realistic prices: {realistic_prices.sum()}/{len(option_prices)} ({realistic_prices.mean()*100:.1f}%)")
                    
                    if realistic_prices.mean() > 0.8:
                        self.using_real_options_data = True
                        print(f"   🎯 REAL OPTIONS DATA CONFIRMED")
                    else:
                        print(f"   ⚠️ Options data may contain unrealistic prices")
                
            except Exception as e:
                print(f"⚠️ Error validating options data: {e}")
        else:
            print(f"❌ NO REAL OPTIONS DATA FOUND")
            print(f"   Strategy is using simulated option pricing")
            print(f"   This significantly affects result realism")
        
        return real_options_data
    
    def validate_realistic_option_pricing(self):
        """Validate that option pricing is realistic"""
        
        print(f"\n🔍 VALIDATING OPTION PRICING METHODOLOGY")
        print("=" * 50)
        
        # Check current option pricing method
        print(f"📊 CURRENT PRICING METHOD ANALYSIS:")
        
        # Simulate some option prices using current method
        test_vix_values = [15, 20, 25, 30, 40]
        test_prices = []
        
        for vix in test_vix_values:
            # Using current pricing formula from constants
            entry_price = ENTRY_PRICE_BASE + (vix - ENTRY_PRICE_VIX_OFFSET) * ENTRY_PRICE_VIX_MULTIPLIER
            test_prices.append(entry_price)
        
        print(f"   Test VIX values: {test_vix_values}")
        print(f"   Generated option prices: {[f'${p:.2f}' for p in test_prices]}")
        
        # Check if prices are realistic
        realistic_count = sum(1 for p in test_prices 
                            if self.realistic_option_price_range[0] <= p <= self.realistic_option_price_range[1])
        
        print(f"   Realistic prices: {realistic_count}/{len(test_prices)} ({realistic_count/len(test_prices)*100:.1f}%)")
        
        if realistic_count == len(test_prices):
            print(f"   ✅ OPTION PRICING APPEARS REALISTIC")
        else:
            print(f"   ⚠️ OPTION PRICING MAY BE UNREALISTIC")
            print(f"   Recommended range: ${self.realistic_option_price_range[0]:.2f} - ${self.realistic_option_price_range[1]:.2f}")
        
        # Analyze pricing formula
        print(f"\n📊 PRICING FORMULA ANALYSIS:")
        print(f"   Base price: ${ENTRY_PRICE_BASE:.2f}")
        print(f"   VIX offset: {ENTRY_PRICE_VIX_OFFSET}")
        print(f"   VIX multiplier: {ENTRY_PRICE_VIX_MULTIPLIER}")
        print(f"   Formula: ${ENTRY_PRICE_BASE:.2f} + (VIX - {ENTRY_PRICE_VIX_OFFSET}) * {ENTRY_PRICE_VIX_MULTIPLIER}")
        
        return realistic_count == len(test_prices)
    
    def create_data_validation_report(self):
        """Create comprehensive data validation report"""
        
        print(f"\n📋 COMPREHENSIVE DATA VALIDATION REPORT")
        print("=" * 60)
        
        # Validate all data sources
        vix_df, vix9d_df = self.validate_vix_data_source()
        spx_df = self.validate_spx_data_source()
        options_data_exists = self.validate_options_data_source()
        realistic_pricing = self.validate_realistic_option_pricing()
        
        # Generate overall assessment
        print(f"\n🎯 OVERALL DATA VALIDATION SUMMARY:")
        print("=" * 60)
        
        validation_score = 0
        max_score = 4
        
        print(f"📊 DATA SOURCE VALIDATION:")
        if self.using_real_vix_data:
            print(f"   ✅ VIX Data: REAL market data confirmed")
            validation_score += 1
        else:
            print(f"   ❌ VIX Data: Not validated or missing")
        
        if self.using_real_spx_data:
            print(f"   ✅ SPX Data: REAL market data confirmed")
            validation_score += 1
        else:
            print(f"   ⚠️ SPX Data: Using synthetic data for VRP calculation")
        
        if self.using_real_options_data:
            print(f"   ✅ Options Data: REAL historical options data found")
            validation_score += 1
        else:
            print(f"   ❌ Options Data: Using simulated option pricing")
        
        if realistic_pricing:
            print(f"   ✅ Option Pricing: Realistic price ranges")
            validation_score += 1
        else:
            print(f"   ⚠️ Option Pricing: May contain unrealistic values")
        
        # Calculate realism score
        realism_percentage = (validation_score / max_score) * 100
        
        print(f"\n🎯 REALISM ASSESSMENT:")
        print(f"   Validation Score: {validation_score}/{max_score}")
        print(f"   Realism Percentage: {realism_percentage:.1f}%")
        
        if realism_percentage >= 75:
            assessment = "HIGH REALISM"
            color = "🟢"
        elif realism_percentage >= 50:
            assessment = "MODERATE REALISM"
            color = "🟡"
        else:
            assessment = "LOW REALISM"
            color = "🔴"
        
        print(f"   Overall Assessment: {color} {assessment}")
        
        # Provide recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if not self.using_real_vix_data:
            print(f"   🔴 CRITICAL: Obtain real VIX historical data")
            print(f"      - Download from CBOE or financial data provider")
            print(f"      - Ensure daily VIX and VIX9D data coverage")
        
        if not self.using_real_spx_data:
            print(f"   🟡 IMPORTANT: Obtain real SPX historical data")
            print(f"      - Needed for accurate VRP calculation")
            print(f"      - Download from Yahoo Finance or similar")
        
        if not self.using_real_options_data:
            print(f"   🔴 CRITICAL: Strategy uses simulated option pricing")
            print(f"      - Results may be significantly overstated")
            print(f"      - Consider using historical options data")
            print(f"      - Implement Black-Scholes pricing with real Greeks")
        
        if not realistic_pricing:
            print(f"   🟡 MODERATE: Review option pricing methodology")
            print(f"      - Ensure prices are within realistic ranges")
            print(f"      - Consider market microstructure effects")
        
        # Impact on results
        print(f"\n📊 IMPACT ON STRATEGY RESULTS:")
        
        if realism_percentage < 50:
            print(f"   🔴 HIGH IMPACT: Results may be significantly overstated")
            print(f"      - 15,936% returns are likely unrealistic")
            print(f"      - Real-world performance would be much lower")
            print(f"      - Strategy needs validation with real data")
        elif realism_percentage < 75:
            print(f"   🟡 MODERATE IMPACT: Results may be somewhat overstated")
            print(f"      - Performance likely inflated by 20-50%")
            print(f"      - Strategy shows promise but needs refinement")
        else:
            print(f"   🟢 LOW IMPACT: Results are likely realistic")
            print(f"      - High confidence in strategy performance")
            print(f"      - Ready for live trading consideration")
        
        return {
            'vix_data_real': self.using_real_vix_data,
            'spx_data_real': self.using_real_spx_data,
            'options_data_real': self.using_real_options_data,
            'pricing_realistic': realistic_pricing,
            'validation_score': validation_score,
            'realism_percentage': realism_percentage,
            'assessment': assessment
        }

def main():
    """Main execution function"""
    
    print("🔍 REALISTIC DATA VALIDATION FOR VRP STRATEGY")
    print("=" * 80)
    print("Validating market data sources and option pricing realism")
    print("=" * 80)
    
    # Create validator
    validator = RealisticDataValidator()
    
    # Run comprehensive validation
    validation_results = validator.create_data_validation_report()
    
    print(f"\n🎉 DATA VALIDATION COMPLETED!")
    print(f"📊 Realism Assessment: {validation_results['assessment']}")
    print(f"📈 Confidence Level: {validation_results['realism_percentage']:.1f}%")
    
    return validation_results

if __name__ == "__main__":
    results = main()
