#!/usr/bin/env python3
"""
Run Enhanced Reverse Strategy Backtest with Comprehensive PDF Report
This script runs the complete backtesting process and automatically generates
a professional PDF report with ChatGPT narratives.
"""

import os
import sys
from datetime import datetime

def setup_environment():
    """Setup environment for backtesting with reporting"""
    
    print("🚀 ENHANCED REVERSE STRATEGY BACKTEST WITH COMPREHENSIVE REPORTING")
    print("=" * 80)
    print("This will:")
    print("• Run the Enhanced Reverse Signal Strategy backtest")
    print("• Generate equity curves and trade analysis")
    print("• Create comprehensive PDF report with ChatGPT narratives")
    print("• Provide current signal analysis and next trading recommendation")
    print("=" * 80)
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print("✅ OpenAI API key found - ChatGPT narratives will be generated")
    else:
        print("⚠️ No OpenAI API key found - static narratives will be used")
        print("   Set with: export OPENAI_API_KEY='your-api-key'")
    
    return api_key is not None

def run_enhanced_backtest():
    """Run the enhanced reverse strategy backtest"""
    
    print("\n📊 Running Enhanced Reverse Strategy Backtest...")
    
    try:
        # Import and run the enhanced strategy
        from enhanced_reverse_strategy import EnhancedReverseStrategy
        
        # Create strategy instance
        strategy = EnhancedReverseStrategy()
        
        # Run the strategy (this will automatically generate the comprehensive report)
        results = strategy.run_enhanced_strategy()
        
        if results:
            print(f"\n✅ BACKTEST COMPLETED SUCCESSFULLY!")
            print(f"📊 Performance Summary:")
            print(f"   • Total Return: {results['total_return']:.1f}%")
            print(f"   • Win Rate: {results['win_rate']:.1f}%")
            print(f"   • Total Trades: {results['total_trades']}")
            print(f"   • Profit Factor: {results['profit_factor']:.2f}")
            print(f"   • Max Drawdown: {results['max_drawdown']:.1f}%")
            
            return results
        else:
            print("\n❌ Backtest failed")
            return None
            
    except Exception as e:
        print(f"\n❌ Backtest error: {e}")
        return None

def run_reverse_signal_backtest():
    """Run the basic reverse signal strategy backtest"""
    
    print("\n📊 Running Basic Reverse Signal Strategy Backtest...")
    
    try:
        # Import and run the reverse signal strategy
        from reverse_signal_strategy import ReverseSignalStrategy
        
        # Create strategy instance
        strategy = ReverseSignalStrategy()
        
        # Run the strategy (this will automatically generate the comprehensive report)
        results = strategy.run_reverse_strategy()
        
        if results:
            print(f"\n✅ REVERSE SIGNAL BACKTEST COMPLETED!")
            print(f"📊 Performance Summary:")
            print(f"   • Total Return: {results['total_return']:.1f}%")
            print(f"   • Win Rate: {results['win_rate']:.1f}%")
            print(f"   • Total Trades: {results['total_trades']}")
            print(f"   • Profit Factor: {results['profit_factor']:.2f}")
            print(f"   • Max Drawdown: {results['max_drawdown']:.1f}%")
            
            return results
        else:
            print("\n❌ Reverse signal backtest failed")
            return None
            
    except Exception as e:
        print(f"\n❌ Reverse signal backtest error: {e}")
        return None

def main():
    """Main execution function"""
    
    # Setup environment
    has_chatgpt = setup_environment()
    
    print(f"\n🎯 Select Strategy to Run:")
    print("1. Enhanced Reverse Strategy (Recommended - 609% return)")
    print("2. Basic Reverse Signal Strategy (210% return)")
    print("3. Run Both Strategies")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 Running Enhanced Reverse Strategy...")
        results = run_enhanced_backtest()
        
    elif choice == "2":
        print("\n🚀 Running Basic Reverse Signal Strategy...")
        results = run_reverse_signal_backtest()
        
    elif choice == "3":
        print("\n🚀 Running Both Strategies...")
        print("\n" + "="*50)
        print("ENHANCED REVERSE STRATEGY")
        print("="*50)
        enhanced_results = run_enhanced_backtest()
        
        print("\n" + "="*50)
        print("BASIC REVERSE SIGNAL STRATEGY")
        print("="*50)
        basic_results = run_reverse_signal_backtest()
        
        # Compare results
        if enhanced_results and basic_results:
            print(f"\n📊 STRATEGY COMPARISON:")
            print(f"Enhanced Strategy: {enhanced_results['total_return']:.1f}% return, {enhanced_results['win_rate']:.1f}% win rate")
            print(f"Basic Strategy: {basic_results['total_return']:.1f}% return, {basic_results['win_rate']:.1f}% win rate")
            print(f"Enhancement Improvement: +{enhanced_results['total_return'] - basic_results['total_return']:.1f}% return")
        
        results = enhanced_results
        
    else:
        print("❌ Invalid choice")
        return None
    
    if results:
        print(f"\n🎉 BACKTESTING WITH COMPREHENSIVE REPORTING COMPLETED!")
        print(f"📁 Check the reports/ directory for:")
        print(f"   • Comprehensive PDF report with ChatGPT narratives")
        print(f"   • Equity curve charts")
        print(f"   • Detailed trade history CSV files")
        print(f"   • Current signal analysis and recommendations")
        
        if has_chatgpt:
            print(f"🤖 Reports include professional ChatGPT-generated narratives")
        else:
            print(f"📝 Reports use static narratives (set OPENAI_API_KEY for ChatGPT)")
    
    return results

if __name__ == "__main__":
    results = main()
