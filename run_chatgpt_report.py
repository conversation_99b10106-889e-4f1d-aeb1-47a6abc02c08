#!/usr/bin/env python3
"""
Run Comprehensive PDF Report with ChatGPT Narratives
This script sets up the OpenAI API key and generates a professional PDF report
with ChatGPT-generated narratives for institutional distribution.
"""

import os
import sys
from comprehensive_pdf_report import ComprehensivePDFReport

def setup_openai_key():
    """Setup OpenAI API key"""
    
    # Check if API key is already set
    if os.getenv('OPENAI_API_KEY'):
        print("✅ OpenAI API key found in environment")
        return True
    
    # Prompt user for API key
    print("🔑 OpenAI API Key Setup")
    print("=" * 40)
    print("To generate professional narratives with ChatGPT, please provide your OpenAI API key.")
    print("You can get an API key from: https://platform.openai.com/api-keys")
    print()
    
    api_key = input("Enter your OpenAI API key (or press Enter to skip): ").strip()
    
    if not api_key:
        print("⚠️ No API key provided - will use static narratives")
        return False
    
    # Set the API key for this session
    os.environ['OPENAI_API_KEY'] = api_key
    print("✅ OpenAI API key set for this session")
    return True

def main():
    """Main execution function"""
    
    print("🚀 COMPREHENSIVE PDF REPORT WITH CHATGPT NARRATIVES")
    print("=" * 70)
    print("This will generate a professional PDF report with:")
    print("• Executive Summary (ChatGPT generated)")
    print("• Strategy Overview (ChatGPT generated)")
    print("• Signal Analysis (ChatGPT generated)")
    print("• Performance Analysis (ChatGPT generated)")
    print("• Complete methodology and results")
    print("=" * 70)
    
    # Setup OpenAI API key
    has_openai = setup_openai_key()
    
    if has_openai:
        print("\n🤖 ChatGPT narratives will be generated")
    else:
        print("\n📝 Static narratives will be used")
    
    print("\n📊 Generating comprehensive PDF report...")
    
    # Create report generator
    report_generator = ComprehensivePDFReport()
    
    # Generate comprehensive report
    try:
        report_file = report_generator.generate_comprehensive_report()
        
        print(f"\n🎉 COMPREHENSIVE REPORT COMPLETED!")
        print(f"📁 Report saved to: {report_file}")
        
        if has_openai:
            print("🤖 Report includes ChatGPT-generated professional narratives")
        
        return report_file
        
    except Exception as e:
        print(f"\n❌ Report generation failed: {e}")
        print("💡 Try running without ChatGPT narratives if API issues occur")
        return None

if __name__ == "__main__":
    report_file = main()
