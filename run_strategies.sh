#!/bin/bash

# JPM Call Spread Strategy - Automated Execution Script
# Loads environment variables and runs call spread strategy only

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}============================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}📊 $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to load environment variables
load_environment() {
    print_header "LOADING ENVIRONMENT VARIABLES"
    
    # Check if .env file exists
    if [ -f ".env" ]; then
        print_success "Found .env file"
        
        # Load environment variables
        set -a  # Automatically export all variables
        source .env
        set +a  # Stop automatically exporting
        
        print_success "Environment variables loaded from .env"
        
        # Check for OpenAI API key
        if [ -n "$OPENAI_API_KEY" ]; then
            print_success "OpenAI API key found (for ChatGPT narratives)"
        else
            print_warning "OpenAI API key not found - will use default narratives"
        fi
        
    else
        print_warning ".env file not found - creating template"
        
        # Create template .env file
        cat > .env << 'EOF'
# JPM Options Trading Strategies Environment Variables

# OpenAI API Key (optional - for enhanced PDF narratives)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Strategy Configuration
STRATEGY_START_DATE=2023-05-01
STRATEGY_END_DATE=auto
STARTING_CAPITAL=100000

# Risk Management
MAX_POSITION_SIZE=60
MIN_POSITION_SIZE=5
MAX_DRAWDOWN_THRESHOLD=2.0

# Data Paths (adjust if needed)
VIX_DATA_PATH=/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/VIX.csv
OPTIONS_DATA_PATH=../optionshistory

# Reporting
GENERATE_PDF_REPORTS=true
INCLUDE_CHARTS=true
INCLUDE_NARRATIVES=true
EOF
        
        print_info "Template .env file created - please edit with your settings"
        print_info "You can run the script again after updating the .env file"
    fi
}

# Function to activate virtual environment
activate_virtual_environment() {
    print_header "CHECKING VIRTUAL ENVIRONMENT"

    # Check for virtual environment
    if [ -d ".venv" ]; then
        print_success "Found .venv directory"

        # Activate virtual environment
        if [ -f ".venv/bin/activate" ]; then
            print_info "Activating Python virtual environment..."
            source .venv/bin/activate
            print_success "Virtual environment activated"
        elif [ -f ".venv/Scripts/activate" ]; then
            # Windows path
            print_info "Activating Python virtual environment (Windows)..."
            source .venv/Scripts/activate
            print_success "Virtual environment activated"
        else
            print_warning "Virtual environment found but activation script missing"
        fi
    elif [ -d "venv" ]; then
        print_success "Found venv directory"

        # Activate virtual environment
        if [ -f "venv/bin/activate" ]; then
            print_info "Activating Python virtual environment..."
            source venv/bin/activate
            print_success "Virtual environment activated"
        elif [ -f "venv/Scripts/activate" ]; then
            # Windows path
            print_info "Activating Python virtual environment (Windows)..."
            source venv/Scripts/activate
            print_success "Virtual environment activated"
        else
            print_warning "Virtual environment found but activation script missing"
        fi
    else
        print_warning "No virtual environment found (.venv or venv)"

        # Ask user if they want to create one
        read -p "Would you like to create a virtual environment? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "Creating virtual environment..."
            if python3 -m venv .venv; then
                print_success "Virtual environment created in .venv"

                # Activate it
                if [ -f ".venv/bin/activate" ]; then
                    source .venv/bin/activate
                    print_success "Virtual environment activated"
                elif [ -f ".venv/Scripts/activate" ]; then
                    source .venv/Scripts/activate
                    print_success "Virtual environment activated"
                fi
            else
                print_error "Failed to create virtual environment"
            fi
        else
            print_info "Continuing without virtual environment"
        fi
    fi
}

# Function to check Python environment
check_python_environment() {
    print_header "CHECKING PYTHON ENVIRONMENT"

    # Check Python version
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version 2>&1)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3 not found - please install Python 3.7+"
        exit 1
    fi

    # Check if we're in a virtual environment
    if [ -n "$VIRTUAL_ENV" ]; then
        print_success "Running in virtual environment: $VIRTUAL_ENV"
    else
        print_warning "Not running in a virtual environment"
    fi
    
    # Check required packages
    print_info "Checking required Python packages..."
    
    REQUIRED_PACKAGES=(
        "pandas"
        "numpy"
        "matplotlib"
        "seaborn"
        "reportlab"
        "openai"
    )
    
    MISSING_PACKAGES=()
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            print_success "$package installed"
        else
            MISSING_PACKAGES+=("$package")
            print_warning "$package not found"
        fi
    done
    
    # Use pip or pip3 depending on what's available and if we're in venv
    PIP_CMD="pip3"
    if [ -n "$VIRTUAL_ENV" ] && command_exists pip; then
        PIP_CMD="pip"
    fi

    # Check if requirements.txt exists and install from it
    if [ -f "requirements.txt" ] && [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
        print_info "Found requirements.txt - installing all dependencies..."

        # Upgrade pip first
        print_info "Upgrading pip..."
        $PIP_CMD install --upgrade pip

        # Install from requirements.txt
        if $PIP_CMD install -r requirements.txt; then
            print_success "All packages installed from requirements.txt"
        else
            print_warning "Failed to install from requirements.txt - trying individual packages"

            # Fall back to individual package installation
            if $PIP_CMD install "${MISSING_PACKAGES[@]}"; then
                print_success "Missing packages installed individually"
            else
                print_error "Failed to install packages - please install manually"
                print_info "Try: $PIP_CMD install -r requirements.txt"
                exit 1
            fi
        fi
    elif [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
        print_info "Installing missing packages: ${MISSING_PACKAGES[*]}"

        # Upgrade pip first
        print_info "Upgrading pip..."
        $PIP_CMD install --upgrade pip

        # Install packages
        if $PIP_CMD install "${MISSING_PACKAGES[@]}"; then
            print_success "All packages installed successfully"
        else
            print_error "Failed to install packages - please install manually"
            print_info "Try: $PIP_CMD install ${MISSING_PACKAGES[*]}"
            exit 1
        fi
    else
        print_success "All required packages are installed"
    fi
}

# Function to run call spread strategy
run_call_spread_strategy() {
    print_header "EXECUTING CALL SPREAD STRATEGY"
    
    if [ -f "call_spread_strategy.py" ]; then
        print_info "Running two-leg call spread strategy..."
        
        # Run the strategy
        if python3 call_spread_strategy.py; then
            print_success "Call spread strategy executed successfully"
            
            # Check if trades were generated
            if [ -f "trades/call_spread_trades.csv" ]; then
                TRADE_COUNT=$(tail -n +2 trades/call_spread_trades.csv | wc -l)
                print_success "Generated $TRADE_COUNT call spread trades"
            fi
        else
            print_error "Call spread strategy execution failed"
            return 1
        fi
    else
        print_error "call_spread_strategy.py not found"
        return 1
    fi
}

# Function removed - single options strategy no longer used

# Function to generate PDF reports
generate_pdf_reports() {
    print_header "GENERATING PDF REPORTS"
    
    # Generate call spread PDF
    if [ -f "call_spread_pdf_generator.py" ] && [ -f "trades/call_spread_trades.csv" ]; then
        print_info "Generating call spread PDF report..."
        
        if python3 call_spread_pdf_generator.py; then
            print_success "Call spread PDF report generated"
        else
            print_warning "Call spread PDF generation failed"
        fi
    fi
    
    # Single options PDF generation removed - no longer used
}

# Function to display summary
display_summary() {
    print_header "EXECUTION SUMMARY"
    
    # Call spread results
    if [ -f "trades/call_spread_trades.csv" ]; then
        print_info "Call Spread Strategy Results:"
        python3 -c "
import pandas as pd
trades_df = pd.read_csv('trades/call_spread_trades.csv')
total_return = (trades_df['net_pnl'].sum() / 100000) * 100
win_rate = trades_df['win_loss_flag'].mean() * 100
print(f'  📈 Total Return: {total_return:.1f}%')
print(f'  🎯 Win Rate: {win_rate:.1f}%')
print(f'  📊 Total Trades: {len(trades_df)}')
print(f'  💰 Total P&L: \${trades_df[\"net_pnl\"].sum():,.0f}')
"
    fi
    
    # Single options results removed - no longer used
    
    # PDF reports
    print_info "Generated Reports:"
    if ls reports/*.pdf 1> /dev/null 2>&1; then
        for pdf in reports/*.pdf; do
            if [ -f "$pdf" ]; then
                FILENAME=$(basename "$pdf")
                FILESIZE=$(du -h "$pdf" | cut -f1)
                print_success "  📄 $FILENAME ($FILESIZE)"
            fi
        done
    else
        print_warning "  No PDF reports found"
    fi
}

# Main execution function
main() {
    print_header "JPM CALL SPREAD STRATEGY - AUTOMATED EXECUTION"
    echo -e "${PURPLE}🚀 Two-Leg Call Spreads Strategy${NC}"
    echo -e "${PURPLE}📊 Real SPX Market Data with Balanced Risk Management${NC}"
    echo ""
    
    # Create necessary directories
    mkdir -p trades reports backups
    
    # Load environment
    load_environment

    # Activate virtual environment
    activate_virtual_environment

    # Check Python environment
    check_python_environment
    
    # Run strategy
    print_header "EXECUTING CALL SPREAD STRATEGY"

    # Run call spread strategy
    if run_call_spread_strategy; then
        print_success "Call spread strategy completed"
    else
        print_error "Call spread strategy failed"
        exit 1
    fi
    
    echo ""
    
    # Generate PDF reports
    if [ "${GENERATE_PDF_REPORTS:-true}" = "true" ]; then
        generate_pdf_reports
    else
        print_info "PDF generation skipped (GENERATE_PDF_REPORTS=false)"
    fi
    
    echo ""
    
    # Display summary
    display_summary
    
    print_header "EXECUTION COMPLETED SUCCESSFULLY"
    print_success "All strategies executed and reports generated"
    print_info "Check the 'reports/' directory for PDF files"
    print_info "Check the 'trades/' directory for CSV data"
}

# Run main function
main "$@"
