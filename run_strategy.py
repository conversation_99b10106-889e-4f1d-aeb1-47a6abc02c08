#!/usr/bin/env python3
"""
Main script to run the optimized JPM Collar Strategy
Refactored and cleaned up version with extracted constants
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *
from test_holding_periods_comprehensive import HoldingPeriodTester
from pure_vix_options_strategy import EnhancedVIXOptionsStrategyV3

def run_holding_period_analysis():
    """Run comprehensive holding period analysis with 2024 data"""
    
    print("🚀 JPM COLLAR STRATEGY - HOLDING PERIOD ANALYSIS")
    print("=" * 60)
    print(f"Strategy Version: {STRATEGY_NAME} v{STRATEGY_VERSION}")
    print(f"Using 2024 real options data with VIX filtering")
    print("=" * 60)
    
    # Create and run holding period tester
    tester = HoldingPeriodTester()
    results = tester.run_comprehensive_test()
    
    # Save results
    tester.save_results()
    
    return results

def run_vix_strategy():
    """Run the enhanced VIX strategy with optimal 1-day holding period"""
    
    print("\n🎯 ENHANCED VIX STRATEGY - OPTIMIZED RUN")
    print("=" * 60)
    print("Using optimal 1-day holding period based on analysis")
    print("Real VIX data integration with 2024 options data")
    print("=" * 60)
    
    # Initialize strategy
    strategy = EnhancedVIXOptionsStrategyV3()
    
    # Run backtest with 2024 data
    results = strategy.run_backtest(
        start_date='2024-01-03',
        end_date='2024-06-30'  # 6 months of 2024 data
    )
    
    print(f"\n✅ VIX Strategy completed!")
    print(f"📊 Total Trades: {len(strategy.trades)}")
    
    if strategy.trades:
        total_pnl = sum(trade.get('trade_pnl', 0) for trade in strategy.trades)
        win_rate = len([t for t in strategy.trades if t.get('trade_pnl', 0) > 0]) / len(strategy.trades) * 100
        print(f"📊 Total P&L: ${total_pnl:,.0f}")
        print(f"📊 Win Rate: {win_rate:.1f}%")
        print(f"📊 Final Capital: ${strategy.current_capital:,.0f}")
        
        return_pct = (strategy.current_capital - STARTING_CAPITAL) / STARTING_CAPITAL * 100
        print(f"📊 Total Return: {return_pct:.1f}%")
    
    return results

def main():
    """Main execution function"""
    
    print("🔧 REFACTORED JPM COLLAR STRATEGY")
    print("✅ Constants extracted and code cleaned up")
    print("✅ Using real 2024 VIX and options data")
    print("✅ Optimized for 1-day holding periods")
    print("=" * 70)
    
    # Run holding period analysis
    print("\n1️⃣ Running holding period analysis...")
    holding_results = run_holding_period_analysis()
    
    # Run optimized VIX strategy
    print("\n2️⃣ Running optimized VIX strategy...")
    vix_results = run_vix_strategy()
    
    print("\n🎉 STRATEGY EXECUTION COMPLETED!")
    print("📁 Results saved to reports/ directory")
    print("📊 Check CSV files for detailed trade analysis")
    
    return {
        'holding_period_results': holding_results,
        'vix_strategy_results': vix_results
    }

if __name__ == "__main__":
    results = main()
