#!/bin/bash

# =============================================================================
# Final Real Data Strategy Automation Script
# =============================================================================

set -e  # Exit on any error

# Script configuration
SCRIPT_NAME="Final Real Data Strategy Runner"
SCRIPT_VERSION="1.0"
LOG_FILE="strategy_execution.log"
STRATEGY_SCRIPT="final_strategy_clean.py"
VENV_PATH=".venv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

log_info() {
    log_message "INFO" "${BLUE}$1${NC}"
}

log_success() {
    log_message "SUCCESS" "${GREEN}$1${NC}"
}

log_warning() {
    log_message "WARNING" "${YELLOW}$1${NC}"
}

log_error() {
    log_message "ERROR" "${RED}$1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "============================================================================="
    echo "  ${SCRIPT_NAME} v${SCRIPT_VERSION}"
    echo "  Execution started: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "============================================================================="
    echo -e "${NC}"
}

print_footer() {
    local exit_code=$1
    echo -e "${BLUE}"
    echo "============================================================================="
    if [ $exit_code -eq 0 ]; then
        echo -e "  ${GREEN}Strategy execution completed successfully${BLUE}"
    else
        echo -e "  ${RED}Strategy execution failed with exit code: $exit_code${BLUE}"
    fi
    echo "  Execution ended: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "============================================================================="
    echo -e "${NC}"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Python strategy script exists
    if [ ! -f "${STRATEGY_SCRIPT}" ]; then
        log_error "Strategy script not found: ${STRATEGY_SCRIPT}"
        return 1
    fi
    
    # Check if virtual environment exists
    if [ ! -d "${VENV_PATH}" ]; then
        log_warning "Virtual environment not found at ${VENV_PATH}"
        log_info "Creating virtual environment..."
        python3 -m venv "${VENV_PATH}"
        if [ $? -ne 0 ]; then
            log_error "Failed to create virtual environment"
            return 1
        fi
    fi
    
    # Check if required directories exist
    if [ ! -d "reports" ]; then
        log_info "Creating reports directory..."
        mkdir -p reports
    fi
    
    if [ ! -d "trades" ]; then
        log_info "Creating trades directory..."
        mkdir -p trades
    fi
    
    log_success "Prerequisites check completed"
    return 0
}

activate_virtual_environment() {
    log_info "Activating virtual environment..."
    
    if [ -f "${VENV_PATH}/bin/activate" ]; then
        source "${VENV_PATH}/bin/activate"
        log_success "Virtual environment activated"
        
        # Display Python version
        python_version=$(python --version 2>&1)
        log_info "Using Python: ${python_version}"
        
        return 0
    else
        log_error "Virtual environment activation script not found"
        return 1
    fi
}

install_dependencies() {
    log_info "Installing/updating Python dependencies..."
    
    # Install required packages
    pip install --upgrade pip > /dev/null 2>&1
    pip install pandas numpy matplotlib reportlab > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Dependencies installed successfully"
        return 0
    else
        log_error "Failed to install dependencies"
        return 1
    fi
}

clean_reports_directory() {
    log_info "Cleaning reports directory (preserving latest PDF)..."
    
    if [ -d "reports" ]; then
        # Find the most recent PDF file
        latest_pdf=$(ls -t reports/*.pdf 2>/dev/null | head -n 1)
        
        if [ -n "$latest_pdf" ]; then
            # Create temporary backup
            temp_pdf="/tmp/$(basename "$latest_pdf")"
            cp "$latest_pdf" "$temp_pdf"
            log_info "Backed up latest PDF: $(basename "$latest_pdf")"
        fi
        
        # Clean the directory
        rm -f reports/*
        
        # Restore the latest PDF if it existed
        if [ -n "$latest_pdf" ] && [ -f "$temp_pdf" ]; then
            cp "$temp_pdf" "$latest_pdf"
            rm "$temp_pdf"
            log_info "Restored latest PDF: $(basename "$latest_pdf")"
        fi
        
        log_success "Reports directory cleaned"
    fi
}

run_strategy() {
    log_info "Executing Final Real Data Strategy..."
    
    # Set Python path to include current directory
    export PYTHONPATH="${PYTHONPATH}:$(pwd)"
    
    # Run the strategy with error handling
    python "${STRATEGY_SCRIPT}" 2>&1 | tee -a "${LOG_FILE}"
    local exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
        log_success "Strategy execution completed successfully"
        return 0
    else
        log_error "Strategy execution failed with exit code: $exit_code"
        return $exit_code
    fi
}

commit_changes() {
    log_info "Committing changes to Git repository..."
    
    # Check if we're in a git repository
    if [ ! -d ".git" ]; then
        log_warning "Not in a Git repository - skipping commit"
        return 0
    fi
    
    # Add all changes
    git add .
    
    # Check if there are changes to commit
    if git diff --staged --quiet; then
        log_info "No changes to commit"
        return 0
    fi
    
    # Create commit message with timestamp
    commit_message="Strategy execution: $(date '+%Y-%m-%d %H:%M:%S')"
    
    # Commit changes
    git commit -m "$commit_message" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Changes committed to Git"
        return 0
    else
        log_warning "Failed to commit changes to Git"
        return 1
    fi
}

display_results_summary() {
    log_info "Displaying results summary..."
    
    # Check for generated files
    if [ -f "trades/trades_analysis.csv" ]; then
        trade_count=$(tail -n +2 "trades/trades_analysis.csv" | wc -l)
        log_success "Generated trades analysis: ${trade_count} trades"
    fi
    
    # Check for PDF report
    latest_pdf=$(ls -t reports/*.pdf 2>/dev/null | head -n 1)
    if [ -n "$latest_pdf" ]; then
        log_success "Generated PDF report: $(basename "$latest_pdf")"
    fi
    
    # Display log file location
    log_info "Execution log saved to: ${LOG_FILE}"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    local exit_code=0
    
    # Initialize log file
    echo "Strategy execution started: $(date '+%Y-%m-%d %H:%M:%S')" > "${LOG_FILE}"
    
    print_header
    
    # Execute all steps
    check_prerequisites || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        activate_virtual_environment || exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        install_dependencies || exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        clean_reports_directory || exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        run_strategy || exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        commit_changes || true  # Don't fail on git commit issues
    fi
    
    if [ $exit_code -eq 0 ]; then
        display_results_summary
    fi
    
    print_footer $exit_code
    
    # Deactivate virtual environment if it was activated
    if [ -n "$VIRTUAL_ENV" ]; then
        deactivate
    fi
    
    exit $exit_code
}

# Handle script interruption
trap 'log_error "Script interrupted"; exit 130' INT TERM

# Run main function
main "$@"
