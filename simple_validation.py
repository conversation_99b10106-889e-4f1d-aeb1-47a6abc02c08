#!/usr/bin/env python3

import pandas as pd
import os

print("🎯 SIMPLE VALIDATION - CHECKING TRADE PRICES vs OPTIONS DATA")
print("=" * 70)

# Load trades
trades_df = pd.read_csv('trades/call_spread_trades.csv')
print(f"✅ Loaded {len(trades_df)} trades")

# Load first trade details
first_trade = trades_df.iloc[0]
print(f"\n📊 FIRST TRADE:")
print(f"   Entry Date: {first_trade['entry_date']}")
print(f"   Short Strike: {first_trade['short_strike']:.0f}, Price: ${first_trade['short_entry_price']:.2f}")
print(f"   Long Strike: {first_trade['long_strike']:.0f}, Price: ${first_trade['long_entry_price']:.2f}")

# Load options data for that specific date
entry_date = pd.to_datetime(first_trade['entry_date'])
print(f"\n🔍 LOADING OPTIONS DATA FOR {entry_date.date()}...")

# Find the correct options file
options_base_path = "/Users/<USER>/Downloads/optionhistory"
target_file = None

# Look for 2023 Q2 file (since entry date is 2023-06-15)
for root, _, files in os.walk(options_base_path):
    for file in files:
        if 'spx_complete_2023_q2' in file and file.endswith('.csv'):
            target_file = os.path.join(root, file)
            break
    if target_file:
        break

if not target_file:
    print("❌ Could not find 2023 Q2 options file")
    exit(1)

print(f"📁 Loading: {os.path.basename(target_file)}")

# Load just the data for the specific date
options_df = pd.read_csv(target_file)
options_df['date'] = pd.to_datetime(options_df['date'])

# Filter for the exact entry date
day_options = options_df[options_df['date'] == entry_date].copy()
print(f"✅ Found {len(day_options)} options for {entry_date.date()}")

if len(day_options) == 0:
    print("❌ No options data found for entry date")
    exit(1)

# Filter for call options only
call_options = day_options[day_options['Call/Put'] == 'c'].copy()
print(f"✅ Found {len(call_options)} call options")

# Check short strike
print(f"\n🔍 CHECKING SHORT STRIKE {first_trade['short_strike']:.0f}:")
short_options = call_options[call_options['Strike'] == first_trade['short_strike']].copy()
print(f"   Found {len(short_options)} options with this strike")

if len(short_options) > 0:
    print(f"   Available prices (Last Trade Price):")
    for _, opt in short_options.iterrows():
        last_price = opt['Last Trade Price']
        bid_price = opt.get('Bid Price', 'N/A')
        ask_price = opt.get('Ask Price', 'N/A')
        expiry = pd.to_datetime(opt['Expiry Date'])
        dte = (expiry - entry_date).days
        
        # Check if this matches the trade price
        match = "🎯 EXACT MATCH!" if abs(last_price - first_trade['short_entry_price']) < 0.01 else ""
        
        print(f"      Expiry: {expiry.strftime('%Y-%m-%d')} ({dte} DTE)")
        print(f"      Last: ${last_price:.2f}, Bid: ${bid_price}, Ask: ${ask_price} {match}")
        print()

# Check long strike
print(f"🔍 CHECKING LONG STRIKE {first_trade['long_strike']:.0f}:")
long_options = call_options[call_options['Strike'] == first_trade['long_strike']].copy()
print(f"   Found {len(long_options)} options with this strike")

if len(long_options) > 0:
    print(f"   Available prices (Last Trade Price):")
    for _, opt in long_options.iterrows():
        last_price = opt['Last Trade Price']
        bid_price = opt.get('Bid Price', 'N/A')
        ask_price = opt.get('Ask Price', 'N/A')
        expiry = pd.to_datetime(opt['Expiry Date'])
        dte = (expiry - entry_date).days
        
        # Check if this matches the trade price
        match = "🎯 EXACT MATCH!" if abs(last_price - first_trade['long_entry_price']) < 0.01 else ""
        
        print(f"      Expiry: {expiry.strftime('%Y-%m-%d')} ({dte} DTE)")
        print(f"      Last: ${last_price:.2f}, Bid: ${bid_price}, Ask: ${ask_price} {match}")
        print()

# Summary
short_matches = 0
long_matches = 0

if len(short_options) > 0:
    short_matches = len(short_options[abs(short_options['Last Trade Price'] - first_trade['short_entry_price']) < 0.01])

if len(long_options) > 0:
    long_matches = len(long_options[abs(long_options['Last Trade Price'] - first_trade['long_entry_price']) < 0.01])

print(f"🎯 VALIDATION RESULTS:")
print(f"   Short strike exact matches: {short_matches}")
print(f"   Long strike exact matches: {long_matches}")

if short_matches > 0 and long_matches > 0:
    print(f"\n✅ VALIDATION SUCCESSFUL!")
    print(f"   Both trade prices have exact matches in the options data")
    print(f"   Strategy is using real Last Trade Price data")
elif short_matches > 0 or long_matches > 0:
    print(f"\n⚠️ PARTIAL VALIDATION!")
    print(f"   Some trade prices match, others don't")
else:
    print(f"\n❌ VALIDATION FAILED!")
    print(f"   No exact matches found for trade prices")
    print(f"   Strategy may not be using real market data")

print(f"\n📊 This proves whether the strategy uses real options data or not.")
