#!/usr/bin/env python3
"""
Simple Options Verification Script
Shows exactly what options are available for each trade
"""

import pandas as pd
import os

def load_trade_data():
    """Load the call spread trades"""
    trades_df = pd.read_csv("trades/call_spread_trades.csv")
    
    # Parse dates
    date_columns = ['signal_date', 'entry_date', 'exit_date']
    for col in date_columns:
        if col in trades_df.columns:
            trades_df[col] = pd.to_datetime(trades_df[col])
    
    return trades_df

def get_options_for_trade(entry_date, short_strike, long_strike):
    """Get all available options for a trade"""
    
    # Find the relevant quarterly file
    year = entry_date.year
    quarter = (entry_date.month - 1) // 3 + 1
    subdir = f"{year}_q{quarter}_option_chain"
    filename = f"spx_complete_{year}_q{quarter}.csv"
    target_file = f"/Users/<USER>/Downloads/optionhistory/{subdir}/{filename}"
    
    if not os.path.exists(target_file):
        return None, None
    
    try:
        # Load the full file
        df = pd.read_csv(target_file)
        df['date'] = pd.to_datetime(df['date'])
        df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
        df['days_to_expiry'] = (df['expiry_date'] - df['date']).dt.days
        
        # Filter for the specific date and strikes
        entry_date_norm = entry_date.normalize()
        
        short_options = df[
            (df['date'] == entry_date_norm) &
            (df['Strike'] == short_strike) &
            (df['Call/Put'] == 'c')
        ].copy()
        
        long_options = df[
            (df['date'] == entry_date_norm) &
            (df['Strike'] == long_strike) &
            (df['Call/Put'] == 'c')
        ].copy()
        
        return short_options, long_options
        
    except Exception as e:
        print(f"Error loading {target_file}: {e}")
        return None, None

def verify_trade(trade_row, trade_num):
    """Verify a single trade"""
    
    print(f"\n🔍 TRADE #{trade_num}")
    print("=" * 50)
    print(f"📅 Entry: {trade_row['entry_date'].strftime('%Y-%m-%d')}")
    print(f"📅 Exit: {trade_row['exit_date'].strftime('%Y-%m-%d')}")
    print(f"🎯 Strikes: {trade_row['short_strike']:.0f} / {trade_row['long_strike']:.0f}")
    print(f"💰 Trade Prices: ${trade_row['short_entry_price']:.2f} / ${trade_row['long_entry_price']:.2f}")
    print(f"💰 Net Credit: ${trade_row['net_credit']:.2f}")
    
    # Get available options
    short_options, long_options = get_options_for_trade(
        trade_row['entry_date'], 
        trade_row['short_strike'], 
        trade_row['long_strike']
    )
    
    if short_options is None or long_options is None:
        print("❌ Could not load options data")
        return
    
    print(f"\n📊 SHORT LEG ({trade_row['short_strike']:.0f} strike) - Available Options:")
    print("   Expiry        DTE   Price    Bid     Ask     Volume   OI")
    print("   " + "-" * 55)
    
    if len(short_options) > 0:
        for _, opt in short_options.head(10).iterrows():
            expiry_str = opt['expiry_date'].strftime('%Y-%m-%d')
            dte = opt['days_to_expiry']
            price = opt['Last Trade Price']
            bid = opt.get('Bid Price', 0)
            ask = opt.get('Ask Price', 0)
            volume = opt.get('Volume', 0)
            oi = opt.get('Open Interest', 0)
            
            # Highlight if close to trade price
            marker = "⭐" if abs(price - trade_row['short_entry_price']) < 5 else "  "
            
            print(f"{marker} {expiry_str}  {dte:3d}  ${price:6.2f}  ${bid:6.2f}  ${ask:6.2f}  {volume:6.0f}  {oi:6.0f}")
    else:
        print("   No options found")
    
    print(f"\n📊 LONG LEG ({trade_row['long_strike']:.0f} strike) - Available Options:")
    print("   Expiry        DTE   Price    Bid     Ask     Volume   OI")
    print("   " + "-" * 55)
    
    if len(long_options) > 0:
        for _, opt in long_options.head(10).iterrows():
            expiry_str = opt['expiry_date'].strftime('%Y-%m-%d')
            dte = opt['days_to_expiry']
            price = opt['Last Trade Price']
            bid = opt.get('Bid Price', 0)
            ask = opt.get('Ask Price', 0)
            volume = opt.get('Volume', 0)
            oi = opt.get('Open Interest', 0)
            
            # Highlight if close to trade price
            marker = "⭐" if abs(price - trade_row['long_entry_price']) < 5 else "  "
            
            print(f"{marker} {expiry_str}  {dte:3d}  ${price:6.2f}  ${bid:6.2f}  ${ask:6.2f}  {volume:6.0f}  {oi:6.0f}")
    else:
        print("   No options found")
    
    # Find 30-day options
    print(f"\n🎯 30-DAY OPTIONS (25-35 DTE):")
    
    short_30day = short_options[
        (short_options['days_to_expiry'] >= 25) &
        (short_options['days_to_expiry'] <= 35)
    ] if len(short_options) > 0 else pd.DataFrame()
    
    long_30day = long_options[
        (long_options['days_to_expiry'] >= 25) &
        (long_options['days_to_expiry'] <= 35)
    ] if len(long_options) > 0 else pd.DataFrame()
    
    if len(short_30day) > 0:
        best_short = short_30day.iloc[0]  # First 30-day option
        print(f"   SHORT: {best_short['expiry_date'].strftime('%Y-%m-%d')} ({best_short['days_to_expiry']} DTE) - ${best_short['Last Trade Price']:.2f}")
    else:
        print("   SHORT: No 30-day options found")
    
    if len(long_30day) > 0:
        best_long = long_30day.iloc[0]  # First 30-day option
        print(f"   LONG:  {best_long['expiry_date'].strftime('%Y-%m-%d')} ({best_long['days_to_expiry']} DTE) - ${best_long['Last Trade Price']:.2f}")
    else:
        print("   LONG: No 30-day options found")

def main():
    """Main function"""
    print("🔍 SIMPLE OPTIONS VERIFICATION")
    print("=" * 60)
    print("Shows exactly what options are available for each trade")
    
    # Load trades
    trades_df = load_trade_data()
    print(f"\n✅ Loaded {len(trades_df)} trades")
    
    # Verify first 5 trades
    print(f"\n🔍 Verifying first 5 trades...")
    
    for i in range(min(5, len(trades_df))):
        trade = trades_df.iloc[i]
        verify_trade(trade, i + 1)
    
    print(f"\n🎉 Verification complete!")

if __name__ == "__main__":
    main()
