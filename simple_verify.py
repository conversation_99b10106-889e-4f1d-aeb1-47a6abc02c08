#!/usr/bin/env python3
"""
Simple Options Verification Script
Shows exactly what options are available for each trade
"""

import pandas as pd
import os

def load_trade_data():
    """Load the call spread trades"""
    trades_df = pd.read_csv("trades/call_spread_trades.csv")
    
    # Parse dates
    date_columns = ['signal_date', 'entry_date', 'exit_date']
    for col in date_columns:
        if col in trades_df.columns:
            trades_df[col] = pd.to_datetime(trades_df[col])
    
    return trades_df

def get_options_for_trade(entry_date, short_strike, long_strike):
    """Get all available options for a trade"""
    
    # Find the relevant quarterly file
    year = entry_date.year
    quarter = (entry_date.month - 1) // 3 + 1
    subdir = f"{year}_q{quarter}_option_chain"
    filename = f"spx_complete_{year}_q{quarter}.csv"
    target_file = f"/Users/<USER>/Downloads/optionhistory/{subdir}/{filename}"
    
    if not os.path.exists(target_file):
        return None, None
    
    try:
        # Load the full file
        df = pd.read_csv(target_file)
        df['date'] = pd.to_datetime(df['date'])
        df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
        df['days_to_expiry'] = (df['expiry_date'] - df['date']).dt.days

        # Calculate mid_price exactly like the strategy does
        df['mid_price'] = (df['Bid Price'] + df['Ask Price']) / 2

        # Filter for the specific date and strikes
        entry_date_norm = entry_date.normalize()

        short_options = df[
            (df['date'] == entry_date_norm) &
            (df['Strike'] == short_strike) &
            (df['Call/Put'] == 'c')
        ].copy()

        long_options = df[
            (df['date'] == entry_date_norm) &
            (df['Strike'] == long_strike) &
            (df['Call/Put'] == 'c')
        ].copy()
        
        return short_options, long_options
        
    except Exception as e:
        print(f"Error loading {target_file}: {e}")
        return None, None

def verify_trade(trade_row, trade_num):
    """Verify a single trade"""
    
    print(f"\n🔍 TRADE #{trade_num}")
    print("=" * 50)
    print(f"📅 Entry: {trade_row['entry_date'].strftime('%Y-%m-%d')}")
    print(f"📅 Exit: {trade_row['exit_date'].strftime('%Y-%m-%d')}")
    print(f"🎯 Strikes: {trade_row['short_strike']:.0f} / {trade_row['long_strike']:.0f}")
    print(f"💰 Trade Prices: ${trade_row['short_entry_price']:.2f} / ${trade_row['long_entry_price']:.2f}")
    print(f"💰 Net Credit: ${trade_row['net_credit']:.2f}")
    
    # Get available options
    short_options, long_options = get_options_for_trade(
        trade_row['entry_date'], 
        trade_row['short_strike'], 
        trade_row['long_strike']
    )
    
    if short_options is None or long_options is None:
        print("❌ Could not load options data")
        return
    
    print(f"\n📊 SHORT LEG ({trade_row['short_strike']:.0f} strike) - Available Options:")
    print("   Expiry        DTE   Mid$     Bid     Ask     Volume   OI")
    print("   " + "-" * 55)

    if len(short_options) > 0:
        for _, opt in short_options.head(10).iterrows():
            expiry_str = opt['expiry_date'].strftime('%Y-%m-%d')
            dte = opt['days_to_expiry']
            mid_price = opt['mid_price']
            bid = opt.get('Bid Price', 0)
            ask = opt.get('Ask Price', 0)
            volume = opt.get('Volume', 0)
            oi = opt.get('Open Interest', 0)

            # Highlight if close to trade price (should be EXACT match)
            marker = "🎯" if abs(mid_price - trade_row['short_entry_price']) < 0.01 else "⭐" if abs(mid_price - trade_row['short_entry_price']) < 1 else "  "

            print(f"{marker} {expiry_str}  {dte:3d}  ${mid_price:6.2f}  ${bid:6.2f}  ${ask:6.2f}  {volume:6.0f}  {oi:6.0f}")
    else:
        print("   No options found")
    
    print(f"\n📊 LONG LEG ({trade_row['long_strike']:.0f} strike) - Available Options:")
    print("   Expiry        DTE   Mid$     Bid     Ask     Volume   OI")
    print("   " + "-" * 55)

    if len(long_options) > 0:
        for _, opt in long_options.head(10).iterrows():
            expiry_str = opt['expiry_date'].strftime('%Y-%m-%d')
            dte = opt['days_to_expiry']
            mid_price = opt['mid_price']
            bid = opt.get('Bid Price', 0)
            ask = opt.get('Ask Price', 0)
            volume = opt.get('Volume', 0)
            oi = opt.get('Open Interest', 0)

            # Highlight if close to trade price (should be EXACT match)
            marker = "🎯" if abs(mid_price - trade_row['long_entry_price']) < 0.01 else "⭐" if abs(mid_price - trade_row['long_entry_price']) < 1 else "  "

            print(f"{marker} {expiry_str}  {dte:3d}  ${mid_price:6.2f}  ${bid:6.2f}  ${ask:6.2f}  {volume:6.0f}  {oi:6.0f}")
    else:
        print("   No options found")
    
    # Find 30-day options (25-35 DTE as per strategy)
    print(f"\n🎯 STRATEGY OPTIONS (25-35 DTE - EXACT STRATEGY CRITERIA):")

    short_30day = short_options[
        (short_options['days_to_expiry'] >= 25) &
        (short_options['days_to_expiry'] <= 35)
    ] if len(short_options) > 0 else pd.DataFrame()

    long_30day = long_options[
        (long_options['days_to_expiry'] >= 25) &
        (long_options['days_to_expiry'] <= 35)
    ] if len(long_options) > 0 else pd.DataFrame()

    print(f"   Found {len(short_30day)} SHORT options and {len(long_30day)} LONG options in 25-35 DTE range")

    if len(short_30day) > 0:
        print(f"   SHORT OPTIONS (25-35 DTE):")
        for _, opt in short_30day.iterrows():
            price_diff = abs(opt['Last Trade Price'] - trade_row['short_entry_price'])
            marker = "🎯" if price_diff < 10 else "  "
            print(f"   {marker} {opt['expiry_date'].strftime('%Y-%m-%d')} ({opt['days_to_expiry']} DTE) - ${opt['Last Trade Price']:.2f} (diff: ${price_diff:.2f})")
    else:
        print("   SHORT: No 25-35 DTE options found")

    if len(long_30day) > 0:
        print(f"   LONG OPTIONS (25-35 DTE):")
        for _, opt in long_30day.iterrows():
            price_diff = abs(opt['Last Trade Price'] - trade_row['long_entry_price'])
            marker = "🎯" if price_diff < 10 else "  "
            print(f"   {marker} {opt['expiry_date'].strftime('%Y-%m-%d')} ({opt['days_to_expiry']} DTE) - ${opt['Last Trade Price']:.2f} (diff: ${price_diff:.2f})")
    else:
        print("   LONG: No 25-35 DTE options found")

    # Find best matches within strategy criteria
    if len(short_30day) > 0 and len(long_30day) > 0:
        print(f"\n🎯 BEST STRATEGY MATCHES:")

        # Find closest price matches within 25-35 DTE
        short_30day['price_diff'] = abs(short_30day['Last Trade Price'] - trade_row['short_entry_price'])
        long_30day['price_diff'] = abs(long_30day['Last Trade Price'] - trade_row['long_entry_price'])

        best_short = short_30day.loc[short_30day['price_diff'].idxmin()]
        best_long = long_30day.loc[long_30day['price_diff'].idxmin()]

        print(f"   BEST SHORT: {best_short['expiry_date'].strftime('%Y-%m-%d')} ({best_short['days_to_expiry']} DTE)")
        print(f"      Trade Price: ${trade_row['short_entry_price']:.2f}")
        print(f"      Market Price: ${best_short['Last Trade Price']:.2f}")
        print(f"      Difference: ${best_short['price_diff']:.2f}")

        print(f"   BEST LONG: {best_long['expiry_date'].strftime('%Y-%m-%d')} ({best_long['days_to_expiry']} DTE)")
        print(f"      Trade Price: ${trade_row['long_entry_price']:.2f}")
        print(f"      Market Price: ${best_long['Last Trade Price']:.2f}")
        print(f"      Difference: ${best_long['price_diff']:.2f}")

        # Calculate theoretical spread
        market_credit = best_short['Last Trade Price'] - best_long['Last Trade Price']
        trade_credit = trade_row['net_credit']
        credit_diff = abs(market_credit - trade_credit)

        print(f"   SPREAD ANALYSIS:")
        print(f"      Market Credit: ${market_credit:.2f}")
        print(f"      Trade Credit: ${trade_credit:.2f}")
        print(f"      Difference: ${credit_diff:.2f}")

        if credit_diff < 5:
            print(f"      ✅ EXCELLENT MATCH!")
        elif credit_diff < 15:
            print(f"      ✅ GOOD MATCH!")
        else:
            print(f"      ⚠️ Price difference may indicate different expiry or data timing")

def main():
    """Main function"""
    print("🔍 SIMPLE OPTIONS VERIFICATION")
    print("=" * 60)
    print("Shows exactly what options are available for each trade")
    
    # Load trades
    trades_df = load_trade_data()
    print(f"\n✅ Loaded {len(trades_df)} trades")
    
    # Ask user what to verify
    print(f"\n❓ What would you like to verify?")
    print(f"   1. First 5 trades (quick)")
    print(f"   2. First 20 trades")
    print(f"   3. All 189 trades (writes to file)")

    choice = input("Enter choice (1-3): ").strip()

    if choice == "1":
        num_trades = 5
        output_file = None
        print(f"\n🔍 Verifying first 5 trades...")
    elif choice == "2":
        num_trades = 20
        output_file = None
        print(f"\n🔍 Verifying first 20 trades...")
    elif choice == "3":
        num_trades = len(trades_df)
        output_file = "all_trades_validation.txt"
        print(f"\n🔍 Verifying all {num_trades} trades...")
        print(f"📝 Writing results to: {output_file}")
    else:
        print("❌ Invalid choice")
        return

    # Track validation results
    excellent_matches = 0
    good_matches = 0
    poor_matches = 0

    # Open output file if needed
    if output_file:
        f = open(output_file, 'w', encoding='utf-8')
        f.write("🔍 COMPLETE CALL SPREAD TRADES VALIDATION\n")
        f.write("=" * 60 + "\n")
        f.write(f"Generated: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total trades: {num_trades}\n\n")
    else:
        f = None

    try:
        for i in range(num_trades):
            trade = trades_df.iloc[i]

            # Print progress for large batches
            if num_trades > 10 and (i + 1) % 20 == 0:
                print(f"📊 Progress: {i + 1}/{num_trades} trades processed")

            # Verify trade and capture result
            if f:
                # Redirect output to file
                import sys
                from io import StringIO
                old_stdout = sys.stdout
                sys.stdout = captured_output = StringIO()

                verify_trade(trade, i + 1)

                # Get the output and restore stdout
                output = captured_output.getvalue()
                sys.stdout = old_stdout

                # Write to file
                f.write(output)
                f.flush()

                # Analyze the result for summary
                if "✅ EXCELLENT MATCH!" in output:
                    excellent_matches += 1
                elif "✅ GOOD MATCH!" in output:
                    good_matches += 1
                else:
                    poor_matches += 1

                # Print brief progress
                print(f"Trade {i+1:3d}: ", end="")
                if "✅ EXCELLENT MATCH!" in output:
                    print("✅ EXCELLENT")
                elif "✅ GOOD MATCH!" in output:
                    print("✅ GOOD")
                else:
                    print("⚠️ POOR")
            else:
                # Print to console
                verify_trade(trade, i + 1)

        # Write summary
        if f:
            f.write(f"\n🎉 VALIDATION SUMMARY\n")
            f.write("=" * 40 + "\n")
            f.write(f"Total trades validated: {num_trades}\n")
            f.write(f"✅ Excellent matches: {excellent_matches} ({excellent_matches/num_trades*100:.1f}%)\n")
            f.write(f"✅ Good matches: {good_matches} ({good_matches/num_trades*100:.1f}%)\n")
            f.write(f"⚠️ Poor matches: {poor_matches} ({poor_matches/num_trades*100:.1f}%)\n")
            f.write(f"📊 Success rate: {(excellent_matches + good_matches)/num_trades*100:.1f}%\n")

            print(f"\n🎉 VALIDATION SUMMARY")
            print("=" * 40)
            print(f"Total trades validated: {num_trades}")
            print(f"✅ Excellent matches: {excellent_matches} ({excellent_matches/num_trades*100:.1f}%)")
            print(f"✅ Good matches: {good_matches} ({good_matches/num_trades*100:.1f}%)")
            print(f"⚠️ Poor matches: {poor_matches} ({poor_matches/num_trades*100:.1f}%)")
            print(f"📊 Success rate: {(excellent_matches + good_matches)/num_trades*100:.1f}%")
            print(f"📝 Full results written to: {output_file}")

    finally:
        if f:
            f.close()

    print(f"\n🎉 Verification complete!")

if __name__ == "__main__":
    main()
