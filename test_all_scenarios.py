#!/usr/bin/env python3
"""
Comprehensive Testing Script for Signal Reversal and Holding Periods
Tests both original and reversed signals with 1-5 day holding periods
"""

import pandas as pd
from datetime import datetime
import os
from final_strategy_clean import FinalRealDataStrategy

def test_all_scenarios():
    """
    Test all combinations of signal direction and holding periods
    ORIGINAL STRATEGY ONLY (NO VRP TRADES)
    """
    print("🧪 COMPREHENSIVE ORIGINAL STRATEGY TESTING")
    print("=" * 70)
    print("Testing ORIGINAL strategy (NO VRP) with both signal directions")
    print("Far OTM strikes (10-20% OTM) and adjusted VIX levels")
    print("1-5 day holding periods for complete analysis")
    print("=" * 70)
    
    results = []
    
    # Test configurations
    signal_configs = [
        {"reverse": False, "name": "ORIGINAL"},
        {"reverse": True, "name": "REVERSED"}
    ]
    
    holding_periods = [1, 2, 3, 4, 5]
    
    for signal_config in signal_configs:
        for holding_days in holding_periods:
            print(f"\n🔬 Testing {signal_config['name']} signals with {holding_days}-day holding...")
            
            try:
                # Initialize strategy with specific configuration
                strategy = FinalRealDataStrategy(
                    reverse_signals=signal_config['reverse'],
                    holding_days=holding_days
                )
                
                # Run strategy
                result = strategy.run_final_real_data_strategy()
                
                if result:
                    # Extract key metrics
                    test_result = {
                        'signal_direction': signal_config['name'],
                        'holding_days': holding_days,
                        'total_return': result['total_return'],
                        'win_rate': result['win_rate'],
                        'total_trades': result['total_trades'],
                        'max_drawdown': result['max_drawdown'],
                        'profit_factor': result['profit_factor'],
                        'final_capital': result['final_capital'],
                        'avg_win': result['avg_win'],
                        'avg_loss': result['avg_loss']
                    }
                    
                    results.append(test_result)
                    
                    print(f"   ✅ Return: {result['total_return']:.1f}%")
                    print(f"   ✅ Win Rate: {result['win_rate']:.1f}%")
                    print(f"   ✅ Trades: {result['total_trades']}")
                    print(f"   ✅ Max DD: {result['max_drawdown']:.1f}%")
                    print(f"   ✅ Profit Factor: {result['profit_factor']:.2f}")
                    
                else:
                    print(f"   ❌ Failed to run strategy")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
    
    # Create comprehensive results DataFrame
    if results:
        results_df = pd.DataFrame(results)
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"comprehensive_test_results_{timestamp}.csv"
        results_df.to_csv(results_file, index=False)
        
        print(f"\n📊 COMPREHENSIVE TEST RESULTS")
        print("=" * 60)
        
        # Display results table
        print("\nDetailed Results:")
        print(results_df.to_string(index=False))
        
        # Find best performing configurations
        print(f"\n🏆 BEST PERFORMING CONFIGURATIONS:")
        print("-" * 40)
        
        # Best by total return
        best_return = results_df.loc[results_df['total_return'].idxmax()]
        print(f"📈 Best Return: {best_return['signal_direction']} signals, {best_return['holding_days']} days")
        print(f"   Return: {best_return['total_return']:.1f}%, Win Rate: {best_return['win_rate']:.1f}%")
        
        # Best by win rate
        best_winrate = results_df.loc[results_df['win_rate'].idxmax()]
        print(f"🎯 Best Win Rate: {best_winrate['signal_direction']} signals, {best_winrate['holding_days']} days")
        print(f"   Win Rate: {best_winrate['win_rate']:.1f}%, Return: {best_winrate['total_return']:.1f}%")
        
        # Best by profit factor
        best_pf = results_df.loc[results_df['profit_factor'].idxmax()]
        print(f"⚖️ Best Profit Factor: {best_pf['signal_direction']} signals, {best_pf['holding_days']} days")
        print(f"   Profit Factor: {best_pf['profit_factor']:.2f}, Return: {best_pf['total_return']:.1f}%")
        
        # Best by Sharpe-like ratio (return/drawdown)
        results_df['return_dd_ratio'] = results_df['total_return'] / results_df['max_drawdown'].abs()
        best_sharpe = results_df.loc[results_df['return_dd_ratio'].idxmax()]
        print(f"📊 Best Risk-Adjusted: {best_sharpe['signal_direction']} signals, {best_sharpe['holding_days']} days")
        print(f"   Return/DD Ratio: {best_sharpe['return_dd_ratio']:.2f}, Return: {best_sharpe['total_return']:.1f}%")
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        return results_df
    
    else:
        print("❌ No successful test results")
        return None

if __name__ == "__main__":
    results = test_all_scenarios()
