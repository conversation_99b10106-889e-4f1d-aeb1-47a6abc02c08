#!/usr/bin/env python3
"""
Comprehensive Holding Period Testing for Real Options Strategy
Tests 1-5 day holding periods using real historical options data
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from real_options_backtester import RealOptionsBacktester

class HoldingPeriodTester:
    """Test different holding periods with real options data"""
    
    def __init__(self):
        self.results = {}
        
    def test_holding_period(self, holding_days: int) -> dict:
        """Test a specific holding period"""
        
        print(f"\n🔧 Testing {holding_days}-day holding period")
        print("=" * 50)
        
        try:
            # Create backtester with specific holding period using 2024 data
            backtester = RealOptionsBacktester(
                start_date="2024-01-03",
                end_date="2024-06-30",  # 6 months of 2024 data for better testing
                holding_days=holding_days
            )
            
            # Run backtest
            performance = backtester.run_backtest()
            
            if performance:
                result = {
                    'holding_days': holding_days,
                    'total_trades': performance['total_trades'],
                    'win_rate': performance['win_rate'],
                    'total_return': performance['total_return'],
                    'total_pnl': performance['total_pnl'],
                    'final_capital': performance['final_capital'],
                    'max_drawdown': performance['max_drawdown'],
                    'avg_win': performance['avg_win'],
                    'avg_loss': performance['avg_loss'],
                    'profit_factor': performance['profit_factor'],
                    'success': True
                }
                
                print(f"✅ {holding_days}-day test completed successfully")
                print(f"📊 Total Trades: {result['total_trades']}")
                print(f"📊 Win Rate: {result['win_rate']:.1f}%")
                print(f"📊 Total Return: {result['total_return']:.1f}%")
                print(f"📊 Max Drawdown: {result['max_drawdown']:.1f}%")
                print(f"📊 Profit Factor: {result['profit_factor']:.2f}")
                
                return result
            else:
                print(f"❌ {holding_days}-day test failed - no performance data")
                return {'holding_days': holding_days, 'success': False, 'error': 'no_performance_data'}
                
        except Exception as e:
            print(f"❌ {holding_days}-day test failed with error: {str(e)}")
            return {'holding_days': holding_days, 'success': False, 'error': str(e)}
    
    def run_comprehensive_test(self):
        """Run comprehensive holding period testing"""
        
        print("🚀 COMPREHENSIVE HOLDING PERIOD TESTING")
        print("=" * 60)
        print("Testing holding periods: 1, 2, 3, 4, 5 days")
        print("Using REAL historical SPX options data")
        print("Period: 2024-01-03 to 2024-06-30 (6 months of 2024 data)")
        print("=" * 60)
        
        holding_periods = [1, 2, 3, 4, 5]
        
        for holding_days in holding_periods:
            result = self.test_holding_period(holding_days)
            self.results[holding_days] = result
        
        # Analyze results
        self.analyze_results()
        
        return self.results
    
    def analyze_results(self):
        """Analyze and display results"""
        
        print(f"\n📊 HOLDING PERIOD TESTING RESULTS")
        print("=" * 70)
        
        # Create results table
        successful_results = {k: v for k, v in self.results.items() if v.get('success', False)}
        
        if successful_results:
            print(f"{'Days':<6} {'Trades':<8} {'Win Rate':<10} {'Return':<10} {'Drawdown':<12} {'P&L':<12} {'PF':<6}")
            print("-" * 70)
            
            for holding_days in sorted(successful_results.keys()):
                result = successful_results[holding_days]
                print(f"{holding_days:<6} {result['total_trades']:<8} "
                      f"{result['win_rate']:<10.1f}% {result['total_return']:<10.1f}% "
                      f"{result['max_drawdown']:<12.1f}% ${result['total_pnl']:<11,.0f} "
                      f"{result['profit_factor']:<6.2f}")
            
            # Find best performance
            best_return = max(successful_results.values(), key=lambda x: x['total_return'])
            best_profit_factor = max(successful_results.values(), key=lambda x: x['profit_factor'])
            best_win_rate = max(successful_results.values(), key=lambda x: x['win_rate'])
            
            print(f"\n🏆 BEST PERFORMANCE METRICS:")
            print(f"📈 Best Return: {best_return['holding_days']} days ({best_return['total_return']:.1f}%)")
            print(f"📊 Best Profit Factor: {best_profit_factor['holding_days']} days ({best_profit_factor['profit_factor']:.2f})")
            print(f"🎯 Best Win Rate: {best_win_rate['holding_days']} days ({best_win_rate['win_rate']:.1f}%)")
            
        else:
            print("❌ No successful tests completed")
        
        # Show failed tests
        failed_results = {k: v for k, v in self.results.items() if not v.get('success', False)}
        if failed_results:
            print(f"\n❌ FAILED TESTS:")
            for holding_days, result in failed_results.items():
                print(f"   {holding_days} days: {result.get('error', 'unknown error')}")
    
    def save_results(self):
        """Save results to file"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'reports/holding_period_test_results_{timestamp}.csv'
        
        # Convert results to DataFrame
        results_list = []
        for holding_days, result in self.results.items():
            if result.get('success', False):
                results_list.append(result)
        
        if results_list:
            df = pd.DataFrame(results_list)
            df.to_csv(filename, index=False)
            print(f"\n💾 Results saved to: {filename}")
        else:
            print(f"\n⚠️ No successful results to save")

def main():
    """Main execution function"""
    
    print("🔧 Real Options Holding Period Comprehensive Testing")
    print("🎯 Using actual historical SPX options data")
    print("=" * 70)
    
    # Create tester
    tester = HoldingPeriodTester()
    
    # Run comprehensive test
    results = tester.run_comprehensive_test()
    
    # Save results
    tester.save_results()
    
    print("\n✅ Comprehensive holding period testing completed!")
    
    return results

if __name__ == "__main__":
    main()
