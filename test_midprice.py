#!/usr/bin/env python3

import pandas as pd

print("🔍 TESTING MID-PRICE CALCULATION")
print("=" * 50)

# Load first trade
trades_df = pd.read_csv('trades/call_spread_trades.csv')
first_trade = trades_df.iloc[0]

print(f"📊 FIRST TRADE:")
print(f"   Entry Date: {first_trade['entry_date']}")
print(f"   Short Strike: {first_trade['short_strike']}, Entry Price: ${first_trade['short_entry_price']:.2f}")
print(f"   Long Strike: {first_trade['long_strike']}, Entry Price: ${first_trade['long_entry_price']:.2f}")

# Load options data for that date
df = pd.read_csv('/Users/<USER>/Downloads/optionhistory/2023_q2_option_chain/spx_complete_2023_q2.csv')
df['date'] = pd.to_datetime(df['date'])
df['expiry_date'] = pd.to_datetime(df['Expiry Date'])

# Filter for the entry date
entry_date = pd.to_datetime(first_trade['entry_date'])
day_options = df[df['date'] == entry_date].copy()

print(f"\n📊 OPTIONS DATA FOR {entry_date.date()}:")
print(f"   Total options: {len(day_options)}")

# Check short strike options
short_options = day_options[
    (day_options['Strike'] == first_trade['short_strike']) &
    (day_options['Call/Put'] == 'c')
].copy()

print(f"\n📊 SHORT STRIKE {first_trade['short_strike']:.0f} OPTIONS:")
print(f"   Found {len(short_options)} options")

if len(short_options) > 0:
    # Calculate mid_price for each
    short_options['mid_price'] = (short_options['Bid Price'] + short_options['Ask Price']) / 2
    short_options['days_to_expiry'] = (short_options['expiry_date'] - short_options['date']).dt.days
    
    print(f"   Expiry        DTE   Last    Bid     Ask     Mid     Match?")
    print(f"   " + "-" * 60)
    
    for _, opt in short_options.iterrows():
        last_price = opt['Last Trade Price']
        bid_price = opt['Bid Price']
        ask_price = opt['Ask Price']
        mid_price = opt['mid_price']
        dte = opt['days_to_expiry']
        
        # Check if mid_price matches trade price
        match = "🎯" if abs(mid_price - first_trade['short_entry_price']) < 0.01 else "  "
        
        print(f"   {opt['expiry_date'].strftime('%Y-%m-%d')}  {dte:3d}  ${last_price:6.2f}  ${bid_price:6.2f}  ${ask_price:6.2f}  ${mid_price:6.2f}  {match}")

# Check long strike options
long_options = day_options[
    (day_options['Strike'] == first_trade['long_strike']) &
    (day_options['Call/Put'] == 'c')
].copy()

print(f"\n📊 LONG STRIKE {first_trade['long_strike']:.0f} OPTIONS:")
print(f"   Found {len(long_options)} options")

if len(long_options) > 0:
    # Calculate mid_price for each
    long_options['mid_price'] = (long_options['Bid Price'] + long_options['Ask Price']) / 2
    long_options['days_to_expiry'] = (long_options['expiry_date'] - long_options['date']).dt.days
    
    print(f"   Expiry        DTE   Last    Bid     Ask     Mid     Match?")
    print(f"   " + "-" * 60)
    
    for _, opt in long_options.iterrows():
        last_price = opt['Last Trade Price']
        bid_price = opt['Bid Price']
        ask_price = opt['Ask Price']
        mid_price = opt['mid_price']
        dte = opt['days_to_expiry']
        
        # Check if mid_price matches trade price
        match = "🎯" if abs(mid_price - first_trade['long_entry_price']) < 0.01 else "  "
        
        print(f"   {opt['expiry_date'].strftime('%Y-%m-%d')}  {dte:3d}  ${last_price:6.2f}  ${bid_price:6.2f}  ${ask_price:6.2f}  ${mid_price:6.2f}  {match}")

print(f"\n🎯 ANALYSIS:")
print("If we see 🎯 matches, the strategy is working correctly.")
print("If no matches, there's a bug in the strategy's option selection or price calculation.")
print("The strategy should be using mid_price = (Bid + Ask) / 2 for entry prices.")
