#!/usr/bin/env python3

import pandas as pd
import sys

print("🔍 TESTING PRICE SOURCE - MID_PRICE vs LAST TRADE PRICE")
print("=" * 60)

# Load the trade data
trades_df = pd.read_csv('trades/call_spread_trades.csv')
first_trade = trades_df.iloc[0]

print(f"📊 FIRST TRADE FROM CSV:")
print(f"   Entry Date: {first_trade['entry_date']}")
print(f"   Short: Strike {first_trade['short_strike']:.0f}, Price ${first_trade['short_entry_price']:.2f}")
print(f"   Long:  Strike {first_trade['long_strike']:.0f}, Price ${first_trade['long_entry_price']:.2f}")

# Load options data
sys.path.append('.')
from final_strategy_clean import FinalRealDataStrategy

strategy = FinalRealDataStrategy()
market_data = strategy.load_market_data_with_real_vrp()
options_data = strategy.spx_options_data

options_data['days_to_expiry'] = (options_data['expiry_date'] - options_data['date']).dt.days
options_data['mid_price'] = (options_data['Bid Price'] + options_data['Ask Price']) / 2

# Check ALL options for the entry date and strikes
entry_date = pd.to_datetime(first_trade['entry_date'])

print(f"\n🔍 COMPARING MID_PRICE vs LAST TRADE PRICE:")
print(f"Looking for options on {entry_date.date()}")

# Check long strike (since we found 6 options for this)
long_all = options_data[
    (options_data['date'] == entry_date) &
    (options_data['Strike'] == first_trade['long_strike']) &
    (options_data['Call/Put'] == 'c')
].copy()

print(f"\n📊 LONG STRIKE {first_trade['long_strike']:.0f} OPTIONS:")
print(f"   Trade Price: ${first_trade['long_entry_price']:.2f}")
print(f"   Expiry        DTE   Last$   Bid$    Ask$    Mid$    Last Match?  Mid Match?")
print(f"   " + "-" * 75)

for _, opt in long_all.iterrows():
    dte = opt['days_to_expiry']
    last_price = opt['Last Trade Price']
    bid_price = opt['Bid Price']
    ask_price = opt['Ask Price']
    mid_price = opt['mid_price']
    
    # Check matches
    last_match = "🎯" if abs(last_price - first_trade['long_entry_price']) < 0.01 else "  "
    mid_match = "🎯" if abs(mid_price - first_trade['long_entry_price']) < 0.01 else "  "
    
    print(f"   {opt['expiry_date'].strftime('%Y-%m-%d')}  {dte:2d}  ${last_price:6.2f}  ${bid_price:6.2f}  ${ask_price:6.2f}  ${mid_price:6.2f}     {last_match}        {mid_match}")

# Check if there are exact matches with Last Trade Price
long_last_exact = long_all[abs(long_all['Last Trade Price'] - first_trade['long_entry_price']) < 0.01]
long_mid_exact = long_all[abs(long_all['mid_price'] - first_trade['long_entry_price']) < 0.01]

print(f"\n🎯 EXACT MATCH ANALYSIS:")
print(f"   Last Trade Price matches: {len(long_last_exact)}")
if len(long_last_exact) > 0:
    for _, opt in long_last_exact.iterrows():
        print(f"      {opt['expiry_date'].strftime('%Y-%m-%d')} ({opt['days_to_expiry']} DTE) - Last: ${opt['Last Trade Price']:.2f}")

print(f"   Mid Price matches: {len(long_mid_exact)}")
if len(long_mid_exact) > 0:
    for _, opt in long_mid_exact.iterrows():
        print(f"      {opt['expiry_date'].strftime('%Y-%m-%d')} ({opt['days_to_expiry']} DTE) - Mid: ${opt['mid_price']:.2f}")

print(f"\n🎯 CONCLUSION:")
if len(long_last_exact) > 0:
    print("✅ Strategy is using LAST TRADE PRICE")
    print("   The trade prices match Last Trade Price exactly")
elif len(long_mid_exact) > 0:
    print("✅ Strategy is using MID PRICE")
    print("   The trade prices match Mid Price exactly")
else:
    print("❌ No exact matches found with either pricing method")
    print("   This suggests a different issue (timing, data source, etc.)")

# Also check if there are close matches
print(f"\n📊 CLOSE MATCHES (within $1.00):")
long_last_close = long_all[abs(long_all['Last Trade Price'] - first_trade['long_entry_price']) < 1.0]
long_mid_close = long_all[abs(long_all['mid_price'] - first_trade['long_entry_price']) < 1.0]

print(f"   Last Trade Price close matches: {len(long_last_close)}")
print(f"   Mid Price close matches: {len(long_mid_close)}")

if len(long_last_close) > len(long_mid_close):
    print("   → Strategy likely uses Last Trade Price")
elif len(long_mid_close) > len(long_last_close):
    print("   → Strategy likely uses Mid Price")
else:
    print("   → Inconclusive - need more investigation")
