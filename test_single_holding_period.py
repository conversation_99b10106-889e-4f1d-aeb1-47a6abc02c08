#!/usr/bin/env python3
"""
Test single holding period for Enhanced VIX Options Strategy
Quick test with reduced data to verify fixes
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *
from pure_vix_options_strategy import EnhancedVIXOptionsStrategyV3

def test_single_holding_period():
    """Test a single holding period with minimal data"""
    
    print("🔧 Testing Enhanced VIX Strategy with 2-day holding period")
    print("📊 Using minimal data for quick testing")
    print("=" * 60)
    
    try:
        # Initialize strategy
        strategy = EnhancedVIXOptionsStrategyV3()
        
        # Run backtest with 2024 data
        print("🚀 Running backtest...")
        results = strategy.run_backtest(
            start_date='2024-01-03',
            end_date='2024-01-31'  # Just 1 month of 2024 data for quick test
        )
        
        # Print basic results
        print(f"\n✅ Test completed successfully!")
        print(f"📊 Total Trades: {len(strategy.trades)}")
        print(f"📊 Final Capital: ${strategy.current_capital:,.0f}")
        
        if strategy.trades:
            total_pnl = sum(trade.get('trade_pnl', 0) for trade in strategy.trades)
            win_rate = len([t for t in strategy.trades if t.get('trade_pnl', 0) > 0]) / len(strategy.trades) * 100
            print(f"📊 Total P&L: ${total_pnl:,.0f}")
            print(f"📊 Win Rate: {win_rate:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_single_holding_period()
    if success:
        print("\n✅ Single holding period test PASSED!")
    else:
        print("\n❌ Single holding period test FAILED!")
