#!/usr/bin/env python3
"""
Options Data Verification Script
Loads call spread trades and shows exact options data used for entry and exit
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_spx_options_data_efficiently():
    """Load SPX options data efficiently with memory management"""
    print("📊 Loading SPX options dataset efficiently...")

    options_base_path = "/Users/<USER>/Downloads/optionhistory"

    if not os.path.exists(options_base_path):
        print(f"❌ Options directory not found: {options_base_path}")
        return None

    # Find relevant options files (2023-2025 only)
    options_files = []
    for root, _, files in os.walk(options_base_path):
        for file in files:
            if 'spx_complete' in file.lower() and file.endswith('.csv'):
                # Only load files from 2023-2025 to save memory
                if any(year in file for year in ['2023', '2024', '2025']):
                    file_path = os.path.join(root, file)
                    options_files.append(file_path)

    if not options_files:
        print("❌ No relevant SPX options files found")
        return None

    print(f"📁 Found {len(options_files)} relevant SPX options files")

    # Load and combine options data efficiently
    combined_options = []
    files_loaded = 0
    total_records = 0

    for file_path in sorted(options_files):
        try:
            print(f"   Loading: {os.path.basename(file_path)}", end=" ... ")

            # Load with specific columns only to save memory
            df = pd.read_csv(file_path, usecols=[
                'date', 'Expiry Date', 'Strike', 'Call/Put',
                'Last Trade Price', 'Bid Price', 'Ask Price',
                'Volume', 'Open Interest', 'spx_close'
            ])

            # Parse dates properly
            df['date'] = pd.to_datetime(df['date'])
            df['expiry_date'] = pd.to_datetime(df['Expiry Date'])

            # Filter for 2023-2025 date range
            start_date = pd.to_datetime('2023-05-01')
            end_date = pd.to_datetime('2025-07-14')

            df = df[
                (df['date'] >= start_date) &
                (df['date'] <= end_date)
            ].copy()

            if len(df) > 0:
                # Calculate days to expiry
                df['days_to_expiry'] = (df['expiry_date'] - df['date']).dt.days

                # Rename for consistency
                df = df.rename(columns={
                    'expiry_date': 'expiry',
                    'Last Trade Price': 'price',
                    'Bid Price': 'bid',
                    'Ask Price': 'ask'
                })

                # Drop the original column
                df = df.drop('Expiry Date', axis=1)

                combined_options.append(df)
                files_loaded += 1
                total_records += len(df)
                print(f"{len(df):,} records")

                # Clear memory periodically
                if files_loaded % 5 == 0:
                    print(f"   💾 Memory checkpoint: {total_records:,} total records loaded")

        except Exception as e:
            print(f"ERROR: {e}")
            continue

    if combined_options:
        print("📊 Combining all data...")
        options_df = pd.concat(combined_options, ignore_index=True)

        # Clear the list to free memory
        del combined_options

        print("🔄 Sorting data...")
        options_df = options_df.sort_values(['date', 'Strike', 'Call/Put'])

        print(f"✅ Loaded SPX options data: {len(options_df):,} records from {files_loaded} files")
        print(f"   Date range: {options_df['date'].min()} to {options_df['date'].max()}")
        print(f"   Price range: ${options_df['price'].min():.2f} - ${options_df['price'].max():.2f}")
        print(f"   SPX range: {options_df['spx_close'].min():.0f} - {options_df['spx_close'].max():.0f}")

        return options_df
    else:
        print("❌ No valid SPX options data loaded")
        return None

def load_call_spread_trades():
    """Load the call spread trades CSV"""
    print("\n📈 Loading call spread trades...")
    
    trades_file = "trades/call_spread_trades.csv"
    
    if not os.path.exists(trades_file):
        print(f"❌ Trades file not found: {trades_file}")
        return None
    
    trades_df = pd.read_csv(trades_file)
    
    # Parse dates
    date_columns = ['signal_date', 'entry_date', 'exit_date']
    for col in date_columns:
        if col in trades_df.columns:
            trades_df[col] = pd.to_datetime(trades_df[col])
    
    print(f"✅ Loaded {len(trades_df)} call spread trades")
    print(f"   Date range: {trades_df['entry_date'].min()} to {trades_df['exit_date'].max()}")
    
    return trades_df

def verify_trade_options_data_streaming(trade_row, options_data, trade_num, output_file):
    """Verify the options data for a specific trade and write to file"""
    result = f"\n🔍 VERIFYING TRADE #{trade_num}\n"
    result += "=" * 60 + "\n"

    # Also print to console for real-time feedback
    print(f"🔍 Processing Trade #{trade_num}...", end=" ")

    # Write header immediately
    output_file.write(result)
    output_file.flush()
    
    entry_date = trade_row['entry_date']
    exit_date = trade_row['exit_date']
    short_strike = trade_row['short_strike']
    long_strike = trade_row['long_strike']
    
    print(f"📅 Entry Date: {entry_date.strftime('%Y-%m-%d')}")
    print(f"📅 Exit Date: {exit_date.strftime('%Y-%m-%d')}")
    print(f"🎯 Short Strike: {short_strike}")
    print(f"🎯 Long Strike: {long_strike}")
    print(f"💰 Short Entry Price: ${trade_row['short_entry_price']:.2f}")
    print(f"💰 Long Entry Price: ${trade_row['long_entry_price']:.2f}")
    print(f"💰 Net Credit: ${trade_row['net_credit']:.2f}")
    
    # Find entry options
    print(f"\n📊 ENTRY OPTIONS DATA ({entry_date.strftime('%Y-%m-%d')}):")
    print("-" * 40)
    
    entry_options = options_data[
        (options_data['date'] == entry_date) &
        (options_data['Call/Put'] == 'c') &
        (options_data['Strike'].isin([short_strike, long_strike]))
    ].copy()
    
    if len(entry_options) == 0:
        print(f"❌ No entry options found for {entry_date}")
        return False
    
    # Sort by strike for display
    entry_options = entry_options.sort_values('Strike')
    
    for _, option in entry_options.iterrows():
        leg_type = "SHORT" if option['Strike'] == short_strike else "LONG"
        print(f"   {leg_type} LEG - Strike {option['Strike']:.0f}:")
        print(f"      Price: ${option['price']:.2f}")
        print(f"      Bid: ${option.get('bid', 'N/A')}")
        print(f"      Ask: ${option.get('ask', 'N/A')}")
        print(f"      Volume: {option.get('Volume', 'N/A')}")
        print(f"      Open Interest: {option.get('Open Interest', 'N/A')}")
        print(f"      Days to Expiry: {option['days_to_expiry']}")
        print(f"      SPX Close: ${option['spx_close']:.2f}")
        print(f"      Expiry: {option['expiry'].strftime('%Y-%m-%d')}")
        print()
    
    # Find exit options
    print(f"📊 EXIT OPTIONS DATA ({exit_date.strftime('%Y-%m-%d')}):")
    print("-" * 40)
    
    exit_options = options_data[
        (options_data['date'] == exit_date) &
        (options_data['Call/Put'] == 'c') &
        (options_data['Strike'].isin([short_strike, long_strike]))
    ].copy()
    
    if len(exit_options) == 0:
        print(f"❌ No exit options found for {exit_date}")
        return False
    
    # Sort by strike for display
    exit_options = exit_options.sort_values('Strike')
    
    for _, option in exit_options.iterrows():
        leg_type = "SHORT" if option['Strike'] == short_strike else "LONG"
        print(f"   {leg_type} LEG - Strike {option['Strike']:.0f}:")
        print(f"      Price: ${option['price']:.2f}")
        print(f"      Bid: ${option.get('bid', 'N/A')}")
        print(f"      Ask: ${option.get('ask', 'N/A')}")
        print(f"      Volume: {option.get('Volume', 'N/A')}")
        print(f"      Open Interest: {option.get('Open Interest', 'N/A')}")
        print(f"      Days to Expiry: {option['days_to_expiry']}")
        print(f"      SPX Close: ${option['spx_close']:.2f}")
        print(f"      Expiry: {option['expiry'].strftime('%Y-%m-%d')}")
        print()
    
    # Verify P&L calculation
    print("💰 P&L VERIFICATION:")
    print("-" * 20)
    
    short_entry = trade_row['short_entry_price']
    long_entry = trade_row['long_entry_price']
    short_exit = trade_row['short_exit_price']
    long_exit = trade_row['long_exit_price']
    contracts = trade_row['contracts']
    
    # Calculate P&L
    short_pnl = (short_entry - short_exit) * contracts * 100
    long_pnl = (long_exit - long_entry) * contracts * 100
    total_pnl = short_pnl + long_pnl
    commission = contracts * 4  # 2 legs * 2 transactions
    net_pnl = total_pnl - commission
    
    print(f"   Short Leg P&L: ${short_pnl:,.2f}")
    print(f"   Long Leg P&L: ${long_pnl:,.2f}")
    print(f"   Gross P&L: ${total_pnl:,.2f}")
    print(f"   Commission: ${commission:,.2f}")
    print(f"   Net P&L: ${net_pnl:,.2f}")
    print(f"   Trade Net P&L: ${trade_row['net_pnl']:,.2f}")
    print(f"   Match: {'✅' if abs(net_pnl - trade_row['net_pnl']) < 1 else '❌'}")
    
    return True

def main():
    """Main verification function"""
    print("🔍 OPTIONS DATA VERIFICATION SCRIPT")
    print("=" * 60)
    print("This script verifies the exact options data used in call spread trades")
    print()
    
    # Load options data efficiently
    options_data = load_spx_options_data_efficiently()
    if options_data is None:
        return
    
    # Load trades
    trades_data = load_call_spread_trades()
    if trades_data is None:
        return
    
    print(f"\n📊 VERIFICATION SUMMARY:")
    print(f"   Options Records: {len(options_data):,}")
    print(f"   Trades to Verify: {len(trades_data)}")
    
    # Ask user how many trades to verify
    print(f"\n❓ How many trades would you like to verify?")
    print(f"   1. First 5 trades")
    print(f"   2. Last 5 trades") 
    print(f"   3. Random 5 trades")
    print(f"   4. Specific trade number")
    print(f"   5. All trades (warning: very long output)")
    
    choice = input("Enter choice (1-5): ").strip()
    
    if choice == "1":
        trades_to_verify = trades_data.head(5)
        print(f"\n🔍 Verifying FIRST 5 trades...")
    elif choice == "2":
        trades_to_verify = trades_data.tail(5)
        print(f"\n🔍 Verifying LAST 5 trades...")
    elif choice == "3":
        trades_to_verify = trades_data.sample(5)
        print(f"\n🔍 Verifying RANDOM 5 trades...")
    elif choice == "4":
        trade_num = int(input("Enter trade number (1-{}): ".format(len(trades_data))))
        trades_to_verify = trades_data.iloc[trade_num-1:trade_num]
        print(f"\n🔍 Verifying trade #{trade_num}...")
    elif choice == "5":
        trades_to_verify = trades_data
        print(f"\n🔍 Verifying ALL {len(trades_data)} trades...")
    else:
        print("❌ Invalid choice")
        return
    
    # Verify selected trades
    verified_count = 0
    failed_count = 0
    
    for idx, (_, trade) in enumerate(trades_to_verify.iterrows()):
        trade_num = trade.name + 1  # 1-based numbering
        
        try:
            success = verify_trade_options_data(trade, options_data, trade_num)
            if success:
                verified_count += 1
            else:
                failed_count += 1
        except Exception as e:
            print(f"❌ Error verifying trade #{trade_num}: {e}")
            failed_count += 1
    
    print(f"\n🎉 VERIFICATION COMPLETE!")
    print("=" * 40)
    print(f"✅ Successfully verified: {verified_count} trades")
    print(f"❌ Failed to verify: {failed_count} trades")
    print(f"📊 Success rate: {verified_count/(verified_count+failed_count)*100:.1f}%")

if __name__ == "__main__":
    main()
