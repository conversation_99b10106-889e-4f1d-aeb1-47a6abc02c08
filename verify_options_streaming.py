#!/usr/bin/env python3
"""
Efficient Options Data Verification Script
Processes trades one by one and writes results immediately to avoid memory issues
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def find_nearest_trading_day(date, options_base_path, max_days=5):
    """Find the nearest trading day with available options data"""
    target_date = pd.to_datetime(date).normalize()

    # Try the exact date first, then nearby dates
    for offset in range(max_days):
        # Try the exact date first (offset 0)
        if offset == 0:
            test_date = target_date
        else:
            # Try previous days (markets usually close on weekends)
            test_date = target_date - timedelta(days=offset)

        # Check if we have data for this date
        year = test_date.year
        quarter = (test_date.month - 1) // 3 + 1

        # Construct the full path based on the directory structure
        subdir = f"{year}_q{quarter}_option_chain"
        filename = f"spx_complete_{year}_q{quarter}.csv"
        target_file = os.path.join(options_base_path, subdir, filename)

        if os.path.exists(target_file):
            try:
                # Quick check if this date has data
                df = pd.read_csv(target_file, nrows=1000)  # Just check first 1000 rows
                df['date'] = pd.to_datetime(df['date'])
                if test_date in df['date'].values:
                    return test_date
            except:
                continue

    return None

def get_30day_option_used(date, strike, options_base_path):
    """Get the 30-day expiry option that the strategy would select"""
    # First find the nearest trading day
    trading_date = find_nearest_trading_day(date, options_base_path)

    if trading_date is None:
        return None

    target_date = trading_date

    # Find the relevant quarterly file for this date
    year = target_date.year
    quarter = (target_date.month - 1) // 3 + 1

    # Construct the full path based on the directory structure
    subdir = f"{year}_q{quarter}_option_chain"
    filename = f"spx_complete_{year}_q{quarter}.csv"
    target_file = os.path.join(options_base_path, subdir, filename)

    if not os.path.exists(target_file):
        return None

    try:
        # Load only the specific date and strike we need
        df = pd.read_csv(target_file)
        df['date'] = pd.to_datetime(df['date'])

        # Filter for exact date, strike, and call options
        filtered_df = df[
            (df['date'] == target_date) &
            (df['Strike'] == strike) &
            (df['Call/Put'] == 'c')  # Only calls for call spreads
        ].copy()

        if len(filtered_df) > 0:
            # Add calculated fields
            filtered_df['expiry_date'] = pd.to_datetime(filtered_df['Expiry Date'])
            filtered_df['days_to_expiry'] = (filtered_df['expiry_date'] - filtered_df['date']).dt.days

            # Rename columns for consistency
            filtered_df = filtered_df.rename(columns={
                'Last Trade Price': 'option_price',
                'Bid Price': 'bid',
                'Ask Price': 'ask'
            })

            # Filter for 25-35 day expiry (30-day target with tolerance)
            thirty_day_options = filtered_df[
                (filtered_df['days_to_expiry'] >= 25) &
                (filtered_df['days_to_expiry'] <= 35)
            ].copy()

            if len(thirty_day_options) > 0:
                # Find the option closest to 30 days
                thirty_day_options['dte_diff'] = abs(thirty_day_options['days_to_expiry'] - 30)
                best_match = thirty_day_options.loc[thirty_day_options['dte_diff'].idxmin()]
                return best_match
            else:
                # If no 30-day options, return the closest available
                filtered_df['dte_diff'] = abs(filtered_df['days_to_expiry'] - 30)
                best_match = filtered_df.loc[filtered_df['dte_diff'].idxmin()]
                return best_match

        return None

    except Exception as e:
        print(f"Error loading {target_file}: {e}")
        return None

def verify_single_trade(trade_row, trade_num, output_file, options_base_path):
    """Verify a single trade and write results immediately"""
    
    # Write trade header
    output_file.write(f"\n🔍 VERIFYING TRADE #{trade_num}\n")
    output_file.write("=" * 60 + "\n")
    
    entry_date = trade_row['entry_date']
    exit_date = trade_row['exit_date']
    short_strike = trade_row['short_strike']
    long_strike = trade_row['long_strike']
    
    # Write trade details
    output_file.write(f"📅 Entry Date: {entry_date.strftime('%Y-%m-%d')}\n")
    output_file.write(f"📅 Exit Date: {exit_date.strftime('%Y-%m-%d')}\n")
    output_file.write(f"🎯 Short Strike: {short_strike}\n")
    output_file.write(f"🎯 Long Strike: {long_strike}\n")
    output_file.write(f"💰 Short Entry Price: ${trade_row['short_entry_price']:.2f}\n")
    output_file.write(f"💰 Long Entry Price: ${trade_row['long_entry_price']:.2f}\n")
    output_file.write(f"💰 Net Credit: ${trade_row['net_credit']:.2f}\n")
    
    # Get exact entry options used
    output_file.write(f"\n📊 EXACT ENTRY OPTIONS USED ({entry_date.strftime('%Y-%m-%d')}):\n")
    output_file.write("-" * 40 + "\n")

    # Get the 30-day expiry options that the strategy would select
    short_option = get_30day_option_used(
        entry_date, short_strike, options_base_path
    )

    # Get the 30-day expiry long leg option
    long_option = get_30day_option_used(
        entry_date, long_strike, options_base_path
    )

    if short_option is None or long_option is None:
        output_file.write(f"❌ Could not find exact entry options for {entry_date}\n")
        if short_option is None:
            output_file.write(f"   Missing SHORT leg: Strike {short_strike}, Price ${trade_row['short_entry_price']:.2f}\n")
        if long_option is None:
            output_file.write(f"   Missing LONG leg: Strike {long_strike}, Price ${trade_row['long_entry_price']:.2f}\n")
        print("❌ Missing entry data")
        return False

    # Display SHORT leg
    output_file.write(f"   SHORT LEG - Strike {short_strike:.0f}:\n")
    output_file.write(f"      Trade Price: ${trade_row['short_entry_price']:.2f}\n")
    output_file.write(f"      Market Price: ${short_option['option_price']:.2f}\n")
    output_file.write(f"      Bid: ${short_option.get('bid', 'N/A')}\n")
    output_file.write(f"      Ask: ${short_option.get('ask', 'N/A')}\n")
    output_file.write(f"      Volume: {short_option.get('Volume', 'N/A')}\n")
    output_file.write(f"      Open Interest: {short_option.get('Open Interest', 'N/A')}\n")
    output_file.write(f"      Days to Expiry: {short_option['days_to_expiry']}\n")
    output_file.write(f"      SPX Close: ${short_option['spx_close']:.2f}\n")
    output_file.write(f"      Expiry: {short_option['expiry_date'].strftime('%Y-%m-%d')}\n")
    output_file.write("\n")

    # Display LONG leg
    output_file.write(f"   LONG LEG - Strike {long_strike:.0f}:\n")
    output_file.write(f"      Trade Price: ${trade_row['long_entry_price']:.2f}\n")
    output_file.write(f"      Market Price: ${long_option['option_price']:.2f}\n")
    output_file.write(f"      Bid: ${long_option.get('bid', 'N/A')}\n")
    output_file.write(f"      Ask: ${long_option.get('ask', 'N/A')}\n")
    output_file.write(f"      Volume: {long_option.get('Volume', 'N/A')}\n")
    output_file.write(f"      Open Interest: {long_option.get('Open Interest', 'N/A')}\n")
    output_file.write(f"      Days to Expiry: {long_option['days_to_expiry']}\n")
    output_file.write(f"      SPX Close: ${long_option['spx_close']:.2f}\n")
    output_file.write(f"      Expiry: {long_option['expiry_date'].strftime('%Y-%m-%d')}\n")
    output_file.write("\n")
    
    # Get exact exit options used
    output_file.write(f"📊 EXACT EXIT OPTIONS USED ({exit_date.strftime('%Y-%m-%d')}):\n")
    output_file.write("-" * 40 + "\n")

    # Check if exit date is a trading day
    actual_exit_date = find_nearest_trading_day(exit_date, options_base_path)
    if actual_exit_date != pd.to_datetime(exit_date).normalize():
        output_file.write(f"⚠️ Exit date {exit_date.strftime('%Y-%m-%d')} is not a trading day\n")
        output_file.write(f"   Using nearest trading day: {actual_exit_date.strftime('%Y-%m-%d')}\n\n")

    # Get the 30-day expiry options for exit (same expiry as entry)
    short_exit_option = get_30day_option_used(
        exit_date, short_strike, options_base_path
    )

    # Get the 30-day expiry long leg option for exit
    long_exit_option = get_30day_option_used(
        exit_date, long_strike, options_base_path
    )

    if short_exit_option is None or long_exit_option is None:
        output_file.write(f"❌ Could not find exact exit options for {exit_date}\n")
        if short_exit_option is None:
            output_file.write(f"   Missing SHORT leg: Strike {short_strike}, Price ${trade_row['short_exit_price']:.2f}\n")
        if long_exit_option is None:
            output_file.write(f"   Missing LONG leg: Strike {long_strike}, Price ${trade_row['long_exit_price']:.2f}\n")
        print("❌ Missing exit data")
    else:
        # Display SHORT leg exit
        output_file.write(f"   SHORT LEG - Strike {short_strike:.0f}:\n")
        output_file.write(f"      Trade Price: ${trade_row['short_exit_price']:.2f}\n")
        output_file.write(f"      Market Price: ${short_exit_option['option_price']:.2f}\n")
        output_file.write(f"      Bid: ${short_exit_option.get('bid', 'N/A')}\n")
        output_file.write(f"      Ask: ${short_exit_option.get('ask', 'N/A')}\n")
        output_file.write(f"      Volume: {short_exit_option.get('Volume', 'N/A')}\n")
        output_file.write(f"      Open Interest: {short_exit_option.get('Open Interest', 'N/A')}\n")
        output_file.write(f"      Days to Expiry: {short_exit_option['days_to_expiry']}\n")
        output_file.write(f"      SPX Close: ${short_exit_option['spx_close']:.2f}\n")
        output_file.write(f"      Expiry: {short_exit_option['expiry_date'].strftime('%Y-%m-%d')}\n")
        output_file.write("\n")

        # Display LONG leg exit
        output_file.write(f"   LONG LEG - Strike {long_strike:.0f}:\n")
        output_file.write(f"      Trade Price: ${trade_row['long_exit_price']:.2f}\n")
        output_file.write(f"      Market Price: ${long_exit_option['option_price']:.2f}\n")
        output_file.write(f"      Bid: ${long_exit_option.get('bid', 'N/A')}\n")
        output_file.write(f"      Ask: ${long_exit_option.get('ask', 'N/A')}\n")
        output_file.write(f"      Volume: {long_exit_option.get('Volume', 'N/A')}\n")
        output_file.write(f"      Open Interest: {long_exit_option.get('Open Interest', 'N/A')}\n")
        output_file.write(f"      Days to Expiry: {long_exit_option['days_to_expiry']}\n")
        output_file.write(f"      SPX Close: ${long_exit_option['spx_close']:.2f}\n")
        output_file.write(f"      Expiry: {long_exit_option['expiry_date'].strftime('%Y-%m-%d')}\n")
        output_file.write("\n")
    
    # Verify P&L calculation
    output_file.write("💰 P&L VERIFICATION:\n")
    output_file.write("-" * 20 + "\n")
    
    short_entry = trade_row['short_entry_price']
    long_entry = trade_row['long_entry_price']
    short_exit = trade_row['short_exit_price']
    long_exit = trade_row['long_exit_price']
    contracts = trade_row['contracts']
    
    # Calculate P&L
    short_pnl = (short_entry - short_exit) * contracts * 100
    long_pnl = (long_exit - long_entry) * contracts * 100
    total_pnl = short_pnl + long_pnl
    commission = contracts * 4  # 2 legs * 2 transactions
    net_pnl = total_pnl - commission
    
    output_file.write(f"   Short Leg P&L: ${short_pnl:,.2f}\n")
    output_file.write(f"   Long Leg P&L: ${long_pnl:,.2f}\n")
    output_file.write(f"   Gross P&L: ${total_pnl:,.2f}\n")
    output_file.write(f"   Commission: ${commission:,.2f}\n")
    output_file.write(f"   Net P&L: ${net_pnl:,.2f}\n")
    output_file.write(f"   Trade Net P&L: ${trade_row['net_pnl']:,.2f}\n")
    
    pnl_match = abs(net_pnl - trade_row['net_pnl']) < 1
    output_file.write(f"   Match: {'✅' if pnl_match else '❌'}\n")
    
    # Flush to ensure data is written immediately
    output_file.flush()
    
    print("✅ Verified" if pnl_match else "❌ Failed")
    return pnl_match

def main():
    """Main verification function with streaming processing"""
    print("🔍 EFFICIENT OPTIONS DATA VERIFICATION")
    print("=" * 60)
    print("Processing trades one by one and writing results immediately")
    print()
    
    # Load trades
    trades_file = "trades/call_spread_trades.csv"
    
    if not os.path.exists(trades_file):
        print(f"❌ Trades file not found: {trades_file}")
        return
    
    trades_df = pd.read_csv(trades_file)
    
    # Parse dates
    date_columns = ['signal_date', 'entry_date', 'exit_date']
    for col in date_columns:
        if col in trades_df.columns:
            trades_df[col] = pd.to_datetime(trades_df[col])
    
    print(f"✅ Loaded {len(trades_df)} call spread trades")
    print(f"   Date range: {trades_df['entry_date'].min()} to {trades_df['exit_date'].max()}")
    
    # Options data path
    options_base_path = "/Users/<USER>/Downloads/optionhistory"
    
    if not os.path.exists(options_base_path):
        print(f"❌ Options directory not found: {options_base_path}")
        return
    
    # Ask user how many trades to verify
    print(f"\n❓ How many trades would you like to verify?")
    print(f"   1. First 10 trades")
    print(f"   2. Last 10 trades") 
    print(f"   3. Random 10 trades")
    print(f"   4. All trades (writes to file)")
    
    choice = input("Enter choice (1-4): ").strip()
    
    if choice == "1":
        trades_to_verify = trades_df.head(10)
        output_filename = "verification_first_10_trades.txt"
    elif choice == "2":
        trades_to_verify = trades_df.tail(10)
        output_filename = "verification_last_10_trades.txt"
    elif choice == "3":
        trades_to_verify = trades_df.sample(10)
        output_filename = "verification_random_10_trades.txt"
    elif choice == "4":
        trades_to_verify = trades_df
        output_filename = "verification_all_trades.txt"
    else:
        print("❌ Invalid choice")
        return
    
    print(f"\n🔍 Verifying {len(trades_to_verify)} trades...")
    print(f"📝 Writing results to: {output_filename}")
    
    # Process trades one by one
    verified_count = 0
    failed_count = 0
    
    with open(output_filename, 'w', encoding='utf-8') as output_file:
        # Write header
        output_file.write("🔍 OPTIONS DATA VERIFICATION RESULTS\n")
        output_file.write("=" * 60 + "\n")
        output_file.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        output_file.write(f"Total trades verified: {len(trades_to_verify)}\n")
        output_file.flush()
        
        for i, (_, trade) in enumerate(trades_to_verify.iterrows()):
            trade_num = trade.name + 1  # 1-based numbering
            
            try:
                success = verify_single_trade(trade, trade_num, output_file, options_base_path)
                if success:
                    verified_count += 1
                else:
                    failed_count += 1
                    
                # Progress update every 10 trades
                if (i + 1) % 10 == 0:
                    print(f"📊 Progress: {i + 1}/{len(trades_to_verify)} trades processed")
                    
            except Exception as e:
                print(f"❌ Error verifying trade #{trade_num}: {e}")
                output_file.write(f"\n❌ ERROR verifying trade #{trade_num}: {e}\n")
                failed_count += 1
        
        # Write summary
        output_file.write(f"\n🎉 VERIFICATION COMPLETE!\n")
        output_file.write("=" * 40 + "\n")
        output_file.write(f"✅ Successfully verified: {verified_count} trades\n")
        output_file.write(f"❌ Failed to verify: {failed_count} trades\n")
        output_file.write(f"📊 Success rate: {verified_count/(verified_count+failed_count)*100:.1f}%\n")
    
    print(f"\n🎉 VERIFICATION COMPLETE!")
    print("=" * 40)
    print(f"✅ Successfully verified: {verified_count} trades")
    print(f"❌ Failed to verify: {failed_count} trades")
    print(f"📊 Success rate: {verified_count/(verified_count+failed_count)*100:.1f}%")
    print(f"📝 Full results written to: {output_filename}")

if __name__ == "__main__":
    main()
