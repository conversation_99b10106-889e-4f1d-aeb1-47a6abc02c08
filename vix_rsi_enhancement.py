#!/usr/bin/env python3
"""
VIX RSI Enhancement Module
Implements 2-day RSI overbought/oversold analysis for VIX
"""

import pandas as pd
import numpy as np

def calculate_rsi(prices, period=2):
    """
    Calculate RSI (Relative Strength Index) for given prices
    
    Args:
        prices (pd.Series): Price series
        period (int): RSI period (default: 2 for short-term signals)
    
    Returns:
        pd.Series: RSI values
    """
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def add_vix_rsi_analysis(market_data):
    """
    Add VIX RSI analysis to market data
    
    Args:
        market_data (pd.DataFrame): Market data with VIX column
    
    Returns:
        pd.DataFrame: Enhanced market data with RSI signals
    """
    # Calculate 2-day VIX RSI
    market_data['vix_rsi_2d'] = calculate_rsi(market_data['vix'], period=2)
    
    # RSI thresholds for 2-day period (more sensitive than traditional 14-day)
    RSI_OVERSOLD_THRESHOLD = 20  # VIX oversold (market complacent)
    RSI_OVERBOUGHT_THRESHOLD = 80  # VIX overbought (market fearful)
    
    # RSI signal classification
    market_data['vix_rsi_signal'] = 'NEUTRAL'
    
    # VIX RSI oversold (< 20) = Market complacent = Potential for VIX spike = BEARISH for market
    oversold_mask = market_data['vix_rsi_2d'] < RSI_OVERSOLD_THRESHOLD
    market_data.loc[oversold_mask, 'vix_rsi_signal'] = 'VIX_OVERSOLD'
    
    # VIX RSI overbought (> 80) = Market fearful = Potential for VIX decline = BULLISH for market
    overbought_mask = market_data['vix_rsi_2d'] > RSI_OVERBOUGHT_THRESHOLD
    market_data.loc[overbought_mask, 'vix_rsi_signal'] = 'VIX_OVERBOUGHT'
    
    # Additional RSI levels for fine-tuning
    extreme_oversold_mask = market_data['vix_rsi_2d'] < 10
    market_data.loc[extreme_oversold_mask, 'vix_rsi_signal'] = 'VIX_EXTREME_OVERSOLD'
    
    extreme_overbought_mask = market_data['vix_rsi_2d'] > 90
    market_data.loc[extreme_overbought_mask, 'vix_rsi_signal'] = 'VIX_EXTREME_OVERBOUGHT'
    
    # RSI momentum (rate of change)
    market_data['vix_rsi_momentum'] = market_data['vix_rsi_2d'].diff()
    market_data['vix_rsi_rising'] = market_data['vix_rsi_momentum'] > 0
    
    return market_data

def enhance_signal_with_vix_rsi(signal_direction, vix_rsi_signal, confidence_score):
    """
    Enhance trading signal with VIX RSI analysis
    
    Args:
        signal_direction (str): Original signal direction (BULLISH/BEARISH)
        vix_rsi_signal (str): VIX RSI signal
        confidence_score (float): Original confidence score
    
    Returns:
        tuple: (enhanced_confidence, rsi_confirmation, rsi_multiplier)
    """
    
    # RSI-based signal enhancement for CALL-only strategy
    if vix_rsi_signal == 'VIX_EXTREME_OVERSOLD':
        # VIX extremely oversold = Market very complacent = High probability of VIX spike
        # In our CALL strategy, this suggests potential market volatility = Good for CALL buying
        rsi_multiplier = 1.3
        rsi_confirmation = True
        
    elif vix_rsi_signal == 'VIX_OVERSOLD':
        # VIX oversold = Market complacent = Moderate probability of VIX increase
        rsi_multiplier = 1.2
        rsi_confirmation = True
        
    elif vix_rsi_signal == 'VIX_EXTREME_OVERBOUGHT':
        # VIX extremely overbought = Market very fearful = High probability of VIX decline
        # This could be good for CALL buying as fear subsides
        rsi_multiplier = 1.25
        rsi_confirmation = True
        
    elif vix_rsi_signal == 'VIX_OVERBOUGHT':
        # VIX overbought = Market fearful = Moderate probability of VIX decline
        rsi_multiplier = 1.15
        rsi_confirmation = True
        
    else:
        # Neutral RSI = No strong directional bias
        rsi_multiplier = 1.0
        rsi_confirmation = False
    
    # Enhanced confidence score (capped at 0.95)
    enhanced_confidence = min(0.95, confidence_score * rsi_multiplier)
    
    return enhanced_confidence, rsi_confirmation, rsi_multiplier

def get_vix_rsi_trading_recommendation(vix_rsi_signal, vix_rsi_value):
    """
    Get trading recommendation based on VIX RSI
    
    Args:
        vix_rsi_signal (str): VIX RSI signal category
        vix_rsi_value (float): Current VIX RSI value
    
    Returns:
        str: Trading recommendation
    """
    
    if vix_rsi_signal == 'VIX_EXTREME_OVERSOLD':
        return f"🔥 EXTREME SIGNAL - VIX RSI Extreme Oversold ({vix_rsi_value:.1f}) - Market Complacency Peak - BUY CALLS"
    
    elif vix_rsi_signal == 'VIX_OVERSOLD':
        return f"⚡ HIGH SIGNAL - VIX RSI Oversold ({vix_rsi_value:.1f}) - Market Complacency - BUY CALLS"
    
    elif vix_rsi_signal == 'VIX_EXTREME_OVERBOUGHT':
        return f"🔥 EXTREME SIGNAL - VIX RSI Extreme Overbought ({vix_rsi_value:.1f}) - Market Fear Peak - BUY CALLS"
    
    elif vix_rsi_signal == 'VIX_OVERBOUGHT':
        return f"⚡ HIGH SIGNAL - VIX RSI Overbought ({vix_rsi_value:.1f}) - Market Fear - BUY CALLS"
    
    else:
        return f"📊 NEUTRAL - VIX RSI Neutral ({vix_rsi_value:.1f}) - No Strong RSI Signal"

def analyze_vix_rsi_performance(market_data):
    """
    Analyze VIX RSI signal distribution and effectiveness
    
    Args:
        market_data (pd.DataFrame): Market data with VIX RSI signals
    
    Returns:
        dict: RSI analysis results
    """
    
    # Count RSI signals
    rsi_counts = market_data['vix_rsi_signal'].value_counts()
    
    # RSI value statistics
    rsi_stats = {
        'mean_rsi': market_data['vix_rsi_2d'].mean(),
        'median_rsi': market_data['vix_rsi_2d'].median(),
        'min_rsi': market_data['vix_rsi_2d'].min(),
        'max_rsi': market_data['vix_rsi_2d'].max(),
        'std_rsi': market_data['vix_rsi_2d'].std()
    }
    
    # Signal distribution
    signal_distribution = {
        'extreme_oversold': rsi_counts.get('VIX_EXTREME_OVERSOLD', 0),
        'oversold': rsi_counts.get('VIX_OVERSOLD', 0),
        'neutral': rsi_counts.get('NEUTRAL', 0),
        'overbought': rsi_counts.get('VIX_OVERBOUGHT', 0),
        'extreme_overbought': rsi_counts.get('VIX_EXTREME_OVERBOUGHT', 0)
    }
    
    return {
        'rsi_stats': rsi_stats,
        'signal_distribution': signal_distribution,
        'total_signals': len(market_data),
        'rsi_coverage': (len(market_data) - rsi_counts.get('NEUTRAL', 0)) / len(market_data) * 100
    }

if __name__ == "__main__":
    print("VIX RSI Enhancement Module")
    print("2-day RSI analysis for VIX overbought/oversold conditions")
    print("RSI < 20: VIX Oversold (Market Complacent)")
    print("RSI > 80: VIX Overbought (Market Fearful)")
    print("Both conditions favor CALL buying in our strategy")
