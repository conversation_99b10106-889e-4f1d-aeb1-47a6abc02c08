#!/usr/bin/env python3
"""
VRP (Volatility Risk Premium) Filter Analysis
Analyzes VRP opportunities in the Low-Normal VIX range (15-20) that we currently skip
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constants import *

class VRPFilterAnalysis:
    """Analyze VRP opportunities in Low-Normal VIX range"""
    
    def __init__(self, start_date=DEFAULT_START_DATE, end_date=DEFAULT_END_DATE):
        self.start_date = start_date
        self.end_date = end_date
        
        # VRP thresholds for filtering
        self.vrp_high_threshold = 5.0    # High VRP: VIX significantly > realized vol
        self.vrp_low_threshold = -2.0    # Low VRP: VIX < realized vol
        self.vrp_extreme_high = 8.0      # Extreme high VRP
        self.vrp_extreme_low = -5.0      # Extreme low VRP
        
        # Realized volatility calculation periods
        self.rv_periods = [10, 20, 30]   # 10, 20, 30-day realized volatility
        
    def load_spx_data_for_rv(self):
        """Load SPX data to calculate realized volatility"""
        
        print("📊 Loading SPX data for realized volatility calculation...")
        
        try:
            # Try to load SPX data from multiple possible sources
            spx_files = [
                'data/spx_data.csv',
                'data/SPX.csv',
                'data/spx.csv',
                '../data/spx_data.csv'
            ]
            
            spx_data = None
            for file_path in spx_files:
                if os.path.exists(file_path):
                    try:
                        spx_data = pd.read_csv(file_path, parse_dates=['Date'])
                        spx_data = spx_data.rename(columns={'Date': 'date'})
                        break
                    except:
                        continue
            
            if spx_data is None:
                # Generate synthetic SPX data for demonstration
                print("⚠️ SPX data not found, generating synthetic data for VRP analysis...")
                return self.generate_synthetic_spx_data()
            
            # Ensure we have the required columns
            if 'Close' in spx_data.columns:
                spx_data = spx_data.rename(columns={'Close': 'close'})
            
            # Filter date range
            spx_data = spx_data[
                (spx_data['date'] >= self.start_date) & 
                (spx_data['date'] <= self.end_date)
            ].copy()
            
            print(f"✅ Loaded {len(spx_data)} SPX records from {spx_data['date'].min()} to {spx_data['date'].max()}")
            return spx_data.set_index('date')
            
        except Exception as e:
            print(f"⚠️ Error loading SPX data: {e}")
            print("📊 Generating synthetic SPX data for VRP analysis...")
            return self.generate_synthetic_spx_data()
    
    def generate_synthetic_spx_data(self):
        """Generate synthetic SPX data for VRP analysis"""
        
        # Create date range
        date_range = pd.date_range(start=self.start_date, end=self.end_date, freq='D')
        
        # Generate synthetic SPX prices with realistic volatility
        np.random.seed(42)  # For reproducible results
        
        initial_price = 4200  # Starting SPX level
        returns = np.random.normal(0.0005, 0.015, len(date_range))  # Daily returns
        
        prices = [initial_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        spx_data = pd.DataFrame({
            'date': date_range,
            'close': prices
        })
        
        print(f"✅ Generated {len(spx_data)} synthetic SPX records")
        return spx_data.set_index('date')
    
    def calculate_realized_volatility(self, spx_data):
        """Calculate realized volatility for different periods"""
        
        print("📊 Calculating realized volatility...")
        
        # Calculate daily returns
        spx_data['returns'] = spx_data['close'].pct_change()
        
        # Calculate realized volatility for different periods
        for period in self.rv_periods:
            # Rolling standard deviation of returns, annualized
            spx_data[f'rv_{period}d'] = spx_data['returns'].rolling(window=period).std() * np.sqrt(252) * 100
        
        print(f"✅ Calculated realized volatility for periods: {self.rv_periods}")
        return spx_data
    
    def load_vix_data_with_vrp(self):
        """Load VIX data and calculate VRP"""
        
        print("📊 Loading VIX data for VRP calculation...")
        
        try:
            # Load VIX data
            vix_df = pd.read_csv(VIX_DATA_FILES['VIX'], 
                               names=['date', 'open', 'high', 'low', 'close', 'volume'],
                               parse_dates=['date'])
            vix_df = vix_df.rename(columns={'close': 'vix'})
            
            # Load VIX9D data
            vix9d_df = pd.read_csv(VIX_DATA_FILES['VIX9D'], 
                                 names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                 parse_dates=['date'])
            vix9d_df = vix9d_df.rename(columns={'close': 'vix9d'})
            
            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'vix']], 
                              vix9d_df[['date', 'vix9d']], 
                              on='date', how='inner')
            
            # Filter date range
            vix_data = vix_data[
                (vix_data['date'] >= self.start_date) & 
                (vix_data['date'] <= self.end_date)
            ].copy()
            
            # Calculate VIX momentum
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']
            vix_data['vix_momentum_direction'] = np.where(
                vix_data['vix_momentum'] > 0, 'RISING', 'FALLING'
            )
            
            print(f"✅ Loaded {len(vix_data)} VIX records for VRP analysis")
            return vix_data.set_index('date')
            
        except Exception as e:
            print(f"❌ Error loading VIX data: {e}")
            return None
    
    def calculate_vrp(self, vix_data, spx_data):
        """Calculate VRP (Volatility Risk Premium)"""
        
        print("📊 Calculating VRP (Volatility Risk Premium)...")
        
        # Merge VIX and SPX data
        combined_data = pd.merge(vix_data, spx_data, left_index=True, right_index=True, how='inner')
        
        # Calculate VRP for different realized volatility periods
        for period in self.rv_periods:
            rv_col = f'rv_{period}d'
            vrp_col = f'vrp_{period}d'
            
            if rv_col in combined_data.columns:
                # VRP = Implied Vol (VIX) - Realized Vol
                combined_data[vrp_col] = combined_data['vix'] - combined_data[rv_col]
        
        # Calculate average VRP across periods
        vrp_columns = [f'vrp_{period}d' for period in self.rv_periods]
        combined_data['vrp_avg'] = combined_data[vrp_columns].mean(axis=1)
        
        # Classify VRP conditions
        combined_data['vrp_condition'] = 'NORMAL'
        combined_data.loc[combined_data['vrp_avg'] >= self.vrp_extreme_high, 'vrp_condition'] = 'EXTREME_HIGH'
        combined_data.loc[combined_data['vrp_avg'] >= self.vrp_high_threshold, 'vrp_condition'] = 'HIGH'
        combined_data.loc[combined_data['vrp_avg'] <= self.vrp_extreme_low, 'vrp_condition'] = 'EXTREME_LOW'
        combined_data.loc[combined_data['vrp_avg'] <= self.vrp_low_threshold, 'vrp_condition'] = 'LOW'
        
        print(f"✅ Calculated VRP for {len(combined_data)} observations")
        print(f"📊 VRP condition distribution:")
        print(combined_data['vrp_condition'].value_counts())
        
        return combined_data
    
    def analyze_low_normal_vix_with_vrp(self, combined_data):
        """Analyze Low-Normal VIX range (15-20) with VRP filter"""
        
        print("\n📊 ANALYZING LOW-NORMAL VIX RANGE WITH VRP FILTER")
        print("=" * 60)
        
        # Filter for Low-Normal VIX range
        low_normal_data = combined_data[
            (combined_data['vix'] >= VIX_LOW_NORMAL_LOW) & 
            (combined_data['vix'] < VIX_LOW_NORMAL_HIGH)
        ].copy()
        
        print(f"📅 Low-Normal VIX observations: {len(low_normal_data)}")
        print(f"📊 Date range: {low_normal_data.index.min()} to {low_normal_data.index.max()}")
        
        if len(low_normal_data) == 0:
            print("⚠️ No Low-Normal VIX observations found")
            return None
        
        # Analyze VRP distribution in Low-Normal VIX range
        print(f"\n📈 VRP DISTRIBUTION IN LOW-NORMAL VIX RANGE:")
        vrp_stats = low_normal_data['vrp_avg'].describe()
        print(f"   Mean VRP: {vrp_stats['mean']:.2f}")
        print(f"   Median VRP: {vrp_stats['50%']:.2f}")
        print(f"   Std VRP: {vrp_stats['std']:.2f}")
        print(f"   Min VRP: {vrp_stats['min']:.2f}")
        print(f"   Max VRP: {vrp_stats['max']:.2f}")
        
        # VRP condition breakdown
        print(f"\n🎯 VRP CONDITION BREAKDOWN:")
        vrp_breakdown = low_normal_data['vrp_condition'].value_counts()
        for condition, count in vrp_breakdown.items():
            percentage = (count / len(low_normal_data)) * 100
            print(f"   {condition}: {count} observations ({percentage:.1f}%)")
        
        # Identify potential trading opportunities
        print(f"\n💡 POTENTIAL TRADING OPPORTUNITIES:")
        
        # High VRP opportunities (VIX > Realized Vol)
        high_vrp = low_normal_data[low_normal_data['vrp_avg'] >= self.vrp_high_threshold]
        extreme_high_vrp = low_normal_data[low_normal_data['vrp_avg'] >= self.vrp_extreme_high]
        
        # Low VRP opportunities (VIX < Realized Vol)
        low_vrp = low_normal_data[low_normal_data['vrp_avg'] <= self.vrp_low_threshold]
        extreme_low_vrp = low_normal_data[low_normal_data['vrp_avg'] <= self.vrp_extreme_low]
        
        print(f"   🔴 High VRP (≥{self.vrp_high_threshold}): {len(high_vrp)} opportunities ({len(high_vrp)/len(low_normal_data)*100:.1f}%)")
        print(f"   🔴 Extreme High VRP (≥{self.vrp_extreme_high}): {len(extreme_high_vrp)} opportunities ({len(extreme_high_vrp)/len(low_normal_data)*100:.1f}%)")
        print(f"   🟢 Low VRP (≤{self.vrp_low_threshold}): {len(low_vrp)} opportunities ({len(low_vrp)/len(low_normal_data)*100:.1f}%)")
        print(f"   🟢 Extreme Low VRP (≤{self.vrp_extreme_low}): {len(extreme_low_vrp)} opportunities ({len(extreme_low_vrp)/len(low_normal_data)*100:.1f}%)")
        
        return {
            'low_normal_data': low_normal_data,
            'high_vrp': high_vrp,
            'extreme_high_vrp': extreme_high_vrp,
            'low_vrp': low_vrp,
            'extreme_low_vrp': extreme_low_vrp,
            'vrp_stats': vrp_stats,
            'vrp_breakdown': vrp_breakdown
        }
    
    def generate_vrp_trading_signals(self, vrp_analysis):
        """Generate trading signals based on VRP in Low-Normal VIX range"""
        
        if vrp_analysis is None:
            return None
        
        print("\n🎯 GENERATING VRP-BASED TRADING SIGNALS")
        print("=" * 60)
        
        signals = []
        
        # High VRP signals (sell volatility - bearish options)
        high_vrp_data = vrp_analysis['high_vrp']
        for date, row in high_vrp_data.iterrows():
            signals.append({
                'date': date,
                'signal_type': 'VRP_HIGH',
                'signal_direction': 'BEARISH',  # Sell volatility
                'vix': row['vix'],
                'vrp': row['vrp_avg'],
                'confidence': min(0.8, 0.5 + (row['vrp_avg'] - self.vrp_high_threshold) * 0.05),
                'reasoning': f"High VRP ({row['vrp_avg']:.2f}): VIX overpriced vs realized vol"
            })
        
        # Extreme High VRP signals (strong sell volatility)
        extreme_high_vrp_data = vrp_analysis['extreme_high_vrp']
        for date, row in extreme_high_vrp_data.iterrows():
            signals.append({
                'date': date,
                'signal_type': 'VRP_EXTREME_HIGH',
                'signal_direction': 'BEARISH',  # Strong sell volatility
                'vix': row['vix'],
                'vrp': row['vrp_avg'],
                'confidence': min(0.95, 0.7 + (row['vrp_avg'] - self.vrp_extreme_high) * 0.03),
                'reasoning': f"Extreme High VRP ({row['vrp_avg']:.2f}): VIX severely overpriced"
            })
        
        # Low VRP signals (buy volatility - bullish options)
        low_vrp_data = vrp_analysis['low_vrp']
        for date, row in low_vrp_data.iterrows():
            signals.append({
                'date': date,
                'signal_type': 'VRP_LOW',
                'signal_direction': 'BULLISH',  # Buy volatility
                'vix': row['vix'],
                'vrp': row['vrp_avg'],
                'confidence': min(0.8, 0.5 + abs(row['vrp_avg'] - self.vrp_low_threshold) * 0.05),
                'reasoning': f"Low VRP ({row['vrp_avg']:.2f}): VIX underpriced vs realized vol"
            })
        
        # Extreme Low VRP signals (strong buy volatility)
        extreme_low_vrp_data = vrp_analysis['extreme_low_vrp']
        for date, row in extreme_low_vrp_data.iterrows():
            signals.append({
                'date': date,
                'signal_type': 'VRP_EXTREME_LOW',
                'signal_direction': 'BULLISH',  # Strong buy volatility
                'vix': row['vix'],
                'vrp': row['vrp_avg'],
                'confidence': min(0.95, 0.7 + abs(row['vrp_avg'] - self.vrp_extreme_low) * 0.03),
                'reasoning': f"Extreme Low VRP ({row['vrp_avg']:.2f}): VIX severely underpriced"
            })
        
        signals_df = pd.DataFrame(signals)
        
        if len(signals_df) > 0:
            # Remove duplicates (extreme signals override regular signals)
            signals_df = signals_df.sort_values(['date', 'confidence'], ascending=[True, False])
            signals_df = signals_df.drop_duplicates(subset=['date'], keep='first')
            signals_df = signals_df.sort_values('date')
        
        print(f"✅ Generated {len(signals_df)} VRP-based signals for Low-Normal VIX range")
        
        if len(signals_df) > 0:
            print(f"\n📊 SIGNAL BREAKDOWN:")
            signal_breakdown = signals_df['signal_type'].value_counts()
            for signal_type, count in signal_breakdown.items():
                avg_confidence = signals_df[signals_df['signal_type'] == signal_type]['confidence'].mean()
                print(f"   {signal_type}: {count} signals (avg confidence: {avg_confidence:.2f})")
        
        return signals_df

    def create_vrp_visualizations(self, combined_data, vrp_analysis, signals_df):
        """Create comprehensive VRP analysis visualizations"""

        print("\n📊 Creating VRP analysis visualizations...")

        fig, axes = plt.subplots(2, 2, figsize=(CHART_FIGURE_WIDTH, CHART_FIGURE_HEIGHT))
        fig.suptitle('VRP Analysis for Low-Normal VIX Range (15-20)',
                    fontsize=CHART_TITLE_FONTSIZE_LARGE, fontweight='bold')

        # 1. VIX vs VRP scatter plot
        ax1 = axes[0, 0]
        low_normal_data = vrp_analysis['low_normal_data']

        scatter = ax1.scatter(low_normal_data['vix'], low_normal_data['vrp_avg'],
                            c=low_normal_data['vrp_avg'], cmap='RdYlBu_r',
                            alpha=CHART_SCATTER_ALPHA, s=CHART_SCATTER_SIZE)

        ax1.axhline(y=self.vrp_high_threshold, color=CHART_COLOR_LOSS, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'High VRP ({self.vrp_high_threshold})')
        ax1.axhline(y=self.vrp_low_threshold, color=CHART_COLOR_SUCCESS, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'Low VRP ({self.vrp_low_threshold})')
        ax1.axhline(y=0, color=CHART_COLOR_NEUTRAL, linestyle='-', alpha=CHART_AXHLINE_ALPHA, label='VRP = 0')

        ax1.set_title('VIX vs VRP in Low-Normal Range', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax1.set_xlabel('VIX Level', fontsize=CHART_XLABEL_FONTSIZE)
        ax1.set_ylabel('VRP (VIX - Realized Vol)', fontsize=CHART_YLABEL_FONTSIZE)
        ax1.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax1.grid(True, alpha=CHART_GRID_ALPHA)

        plt.colorbar(scatter, ax=ax1, label='VRP Level')

        # 2. VRP distribution histogram
        ax2 = axes[0, 1]
        ax2.hist(low_normal_data['vrp_avg'], bins=20, alpha=CHART_BAR_ALPHA,
                color=CHART_COLOR_PRIMARY, edgecolor='black')
        ax2.axvline(x=self.vrp_high_threshold, color=CHART_COLOR_LOSS, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'High VRP ({self.vrp_high_threshold})')
        ax2.axvline(x=self.vrp_low_threshold, color=CHART_COLOR_SUCCESS, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'Low VRP ({self.vrp_low_threshold})')
        ax2.axvline(x=0, color=CHART_COLOR_NEUTRAL, linestyle='-', alpha=CHART_AXHLINE_ALPHA, label='VRP = 0')

        ax2.set_title('VRP Distribution', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax2.set_xlabel('VRP Level', fontsize=CHART_XLABEL_FONTSIZE)
        ax2.set_ylabel('Frequency', fontsize=CHART_YLABEL_FONTSIZE)
        ax2.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax2.grid(True, alpha=CHART_GRID_ALPHA)

        # 3. VRP time series
        ax3 = axes[1, 0]
        ax3.plot(low_normal_data.index, low_normal_data['vrp_avg'],
                linewidth=CHART_LINE_WIDTH, color=CHART_COLOR_PRIMARY, label='VRP')
        ax3.axhline(y=self.vrp_high_threshold, color=CHART_COLOR_LOSS, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'High VRP ({self.vrp_high_threshold})')
        ax3.axhline(y=self.vrp_low_threshold, color=CHART_COLOR_SUCCESS, linestyle='--',
                   alpha=CHART_AXHLINE_ALPHA, label=f'Low VRP ({self.vrp_low_threshold})')
        ax3.axhline(y=0, color=CHART_COLOR_NEUTRAL, linestyle='-', alpha=CHART_AXHLINE_ALPHA, label='VRP = 0')

        # Add signal markers if available
        if signals_df is not None and len(signals_df) > 0:
            for _, signal in signals_df.iterrows():
                if signal['date'] in low_normal_data.index:
                    color = CHART_COLOR_LOSS if signal['signal_direction'] == 'BEARISH' else CHART_COLOR_SUCCESS
                    ax3.scatter(signal['date'], signal['vrp'], color=color, s=CHART_SCATTER_SIZE_LARGE,
                              alpha=0.8, marker='o', edgecolor='black', linewidth=1)

        ax3.set_title('VRP Time Series with Signals', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
        ax3.set_xlabel('Date', fontsize=CHART_XLABEL_FONTSIZE)
        ax3.set_ylabel('VRP Level', fontsize=CHART_YLABEL_FONTSIZE)
        ax3.legend(fontsize=CHART_LEGEND_FONTSIZE)
        ax3.grid(True, alpha=CHART_GRID_ALPHA)

        # 4. Signal opportunity analysis
        ax4 = axes[1, 1]
        if signals_df is not None and len(signals_df) > 0:
            signal_counts = signals_df['signal_type'].value_counts()
            bars = ax4.bar(range(len(signal_counts)), signal_counts.values,
                          color=[CHART_COLOR_LOSS, CHART_COLOR_WARNING, CHART_COLOR_SUCCESS, CHART_COLOR_ACCENT][:len(signal_counts)],
                          alpha=CHART_BAR_ALPHA)

            ax4.set_title('VRP Signal Opportunities', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')
            ax4.set_xlabel('Signal Type', fontsize=CHART_XLABEL_FONTSIZE)
            ax4.set_ylabel('Number of Signals', fontsize=CHART_YLABEL_FONTSIZE)
            ax4.set_xticks(range(len(signal_counts)))
            ax4.set_xticklabels(signal_counts.index, rotation=45, ha='right', fontsize=CHART_TICK_FONTSIZE)
            ax4.grid(True, alpha=CHART_GRID_ALPHA)

            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{int(height)}', ha='center', va='bottom', fontsize=CHART_TICK_FONTSIZE)
        else:
            ax4.text(0.5, 0.5, 'No VRP signals generated', ha='center', va='center',
                    transform=ax4.transAxes, fontsize=CHART_TITLE_FONTSIZE)
            ax4.set_title('VRP Signal Opportunities', fontsize=CHART_TITLE_FONTSIZE, fontweight='bold')

        plt.tight_layout()

        # Save chart
        chart_filename = f'{REPORTS_DIR}/vrp_analysis_low_normal_vix.png'
        plt.savefig(chart_filename, dpi=CHART_DPI, bbox_inches='tight')
        print(f"📊 VRP analysis chart saved to: {chart_filename}")

        plt.show()

        return chart_filename

    def generate_vrp_recommendations(self, vrp_analysis, signals_df):
        """Generate recommendations for VRP-based trading in Low-Normal VIX range"""

        print("\n💡 VRP-BASED TRADING RECOMMENDATIONS")
        print("=" * 60)

        if vrp_analysis is None or signals_df is None or len(signals_df) == 0:
            print("⚠️ Insufficient data for VRP recommendations")
            return

        low_normal_data = vrp_analysis['low_normal_data']
        total_observations = len(low_normal_data)
        total_signals = len(signals_df)

        print(f"📊 OPPORTUNITY ASSESSMENT:")
        print(f"   Total Low-Normal VIX observations: {total_observations}")
        print(f"   VRP-based trading opportunities: {total_signals}")
        print(f"   Opportunity rate: {total_signals/total_observations*100:.1f}%")

        # Signal type analysis
        signal_breakdown = signals_df['signal_type'].value_counts()
        avg_confidence = signals_df['confidence'].mean()

        print(f"\n🎯 SIGNAL QUALITY:")
        print(f"   Average confidence: {avg_confidence:.2f}")
        print(f"   High confidence signals (≥0.8): {len(signals_df[signals_df['confidence'] >= 0.8])}")

        # Recommendations by signal type
        print(f"\n📈 TRADING STRATEGY RECOMMENDATIONS:")

        for signal_type, count in signal_breakdown.items():
            subset = signals_df[signals_df['signal_type'] == signal_type]
            avg_conf = subset['confidence'].mean()
            avg_vrp = subset['vrp'].mean()

            if 'HIGH' in signal_type:
                strategy = "SELL volatility (buy puts on VIX, sell calls on SPX)"
                reasoning = "VIX overpriced relative to realized volatility"
            else:
                strategy = "BUY volatility (buy calls on VIX, buy puts on SPX)"
                reasoning = "VIX underpriced relative to realized volatility"

            print(f"\n   {signal_type} ({count} signals):")
            print(f"     Strategy: {strategy}")
            print(f"     Reasoning: {reasoning}")
            print(f"     Avg Confidence: {avg_conf:.2f}")
            print(f"     Avg VRP: {avg_vrp:.2f}")

        # Implementation recommendations
        print(f"\n🔧 IMPLEMENTATION RECOMMENDATIONS:")

        recommendations = [
            "1. INTEGRATE VRP FILTER INTO MAIN STRATEGY:",
            "   • Add VRP calculation to existing VIX analysis",
            "   • Use VRP thresholds to identify opportunities in 15-20 VIX range",
            "   • Combine with existing confidence scoring system",
            "",
            "2. POSITION SIZING FOR VRP SIGNALS:",
            "   • High VRP signals: 10-20 contracts (medium confidence)",
            "   • Extreme VRP signals: 15-25 contracts (high confidence)",
            "   • Scale position size by VRP magnitude and confidence",
            "",
            "3. TIMING AND EXECUTION:",
            "   • Use same 5-day Open->Open timing as main strategy",
            "   • Apply non-overlapping constraint for VRP signals",
            "   • Monitor VRP changes during holding period",
            "",
            "4. RISK MANAGEMENT:",
            "   • Set VRP-specific stop losses (e.g., VRP reversal)",
            "   • Limit VRP trades to 20% of total strategy allocation",
            "   • Monitor realized volatility changes during trades",
            "",
            "5. BACKTESTING VALIDATION:",
            "   • Test VRP signals with historical data",
            "   • Compare VRP strategy performance vs main strategy",
            "   • Validate VRP thresholds with out-of-sample data"
        ]

        for rec in recommendations:
            print(rec)

        # Expected impact
        print(f"\n📊 EXPECTED IMPACT:")
        opportunity_rate = total_signals / total_observations * 100

        if opportunity_rate > 20:
            impact = "SIGNIFICANT"
            description = "VRP filter could substantially increase trading opportunities"
        elif opportunity_rate > 10:
            impact = "MODERATE"
            description = "VRP filter provides meaningful additional opportunities"
        elif opportunity_rate > 5:
            impact = "MINOR"
            description = "VRP filter adds some additional trading opportunities"
        else:
            impact = "MINIMAL"
            description = "VRP filter provides limited additional opportunities"

        print(f"   Impact Level: {impact}")
        print(f"   Description: {description}")
        print(f"   Additional Trades: ~{total_signals} per period")
        print(f"   Strategy Enhancement: {opportunity_rate:.1f}% more opportunities")

def main():
    """Main execution function"""

    print("🔍 VRP FILTER ANALYSIS FOR LOW-NORMAL VIX RANGE")
    print("=" * 80)
    print("Analyzing Volatility Risk Premium opportunities in VIX 15-20 range")
    print("=" * 80)

    # Create analyzer
    analyzer = VRPFilterAnalysis()

    # Load and prepare data
    spx_data = analyzer.load_spx_data_for_rv()
    if spx_data is None:
        print("❌ Failed to load SPX data")
        return None

    # Calculate realized volatility
    spx_data = analyzer.calculate_realized_volatility(spx_data)

    # Load VIX data
    vix_data = analyzer.load_vix_data_with_vrp()
    if vix_data is None:
        print("❌ Failed to load VIX data")
        return None

    # Calculate VRP
    combined_data = analyzer.calculate_vrp(vix_data, spx_data)

    # Analyze Low-Normal VIX range with VRP
    vrp_analysis = analyzer.analyze_low_normal_vix_with_vrp(combined_data)

    # Generate VRP-based trading signals
    signals_df = analyzer.generate_vrp_trading_signals(vrp_analysis)

    # Create visualizations
    if vrp_analysis is not None:
        analyzer.create_vrp_visualizations(combined_data, vrp_analysis, signals_df)

    # Generate recommendations
    analyzer.generate_vrp_recommendations(vrp_analysis, signals_df)

    print(f"\n🎉 VRP ANALYSIS COMPLETED!")
    print(f"📊 Check visualizations and recommendations above")

    return {
        'combined_data': combined_data,
        'vrp_analysis': vrp_analysis,
        'signals_df': signals_df
    }

if __name__ == "__main__":
    results = main()
