#!/usr/bin/env python3
"""
VRP Technical Analysis Enhancement Module
Implements RSI and moving average analysis for VRP (Volatility Risk Premium)
"""

import pandas as pd
import numpy as np

def calculate_rsi(prices, period=14):
    """
    Calculate RSI (Relative Strength Index) for given prices
    
    Args:
        prices (pd.Series): Price series
        period (int): RSI period (default: 14)
    
    Returns:
        pd.Series: RSI values
    """
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def add_vrp_technical_analysis(market_data):
    """
    Add VRP technical analysis to market data

    Args:
        market_data (pd.DataFrame): Market data with VRP column

    Returns:
        pd.DataFrame: Enhanced market data with VRP technical signals
    """
    # Check if VRP data is available
    if 'vrp_avg' not in market_data.columns:
        print("⚠️ VRP data not available, skipping VRP technical analysis")
        # Add empty columns for compatibility
        market_data['vrp_rsi_14d'] = np.nan
        market_data['vrp_tech_signal'] = 'NEUTRAL'
        market_data['vrp_rsi_divergence'] = 'NONE'
        return market_data

    # Calculate VRP moving averages
    market_data['vrp_ma5'] = market_data['vrp_avg'].rolling(window=5, min_periods=3).mean()
    market_data['vrp_ma10'] = market_data['vrp_avg'].rolling(window=10, min_periods=5).mean()
    market_data['vrp_ma20'] = market_data['vrp_avg'].rolling(window=20, min_periods=10).mean()
    
    # Calculate VRP RSI (14-day and 7-day)
    market_data['vrp_rsi_14d'] = calculate_rsi(market_data['vrp_avg'], period=14)
    market_data['vrp_rsi_7d'] = calculate_rsi(market_data['vrp_avg'], period=7)
    
    # VRP position relative to moving averages
    market_data['vrp_above_ma5'] = market_data['vrp_avg'] > market_data['vrp_ma5']
    market_data['vrp_above_ma10'] = market_data['vrp_avg'] > market_data['vrp_ma10']
    market_data['vrp_above_ma20'] = market_data['vrp_avg'] > market_data['vrp_ma20']
    
    # VRP momentum (short-term vs long-term MA)
    market_data['vrp_ma_momentum'] = market_data['vrp_ma5'] - market_data['vrp_ma20']
    market_data['vrp_ma_momentum_positive'] = market_data['vrp_ma_momentum'] > 0
    
    # VRP RSI thresholds
    VRP_RSI_OVERSOLD = 30  # VRP RSI oversold
    VRP_RSI_OVERBOUGHT = 70  # VRP RSI overbought
    
    # VRP technical signal classification
    market_data['vrp_tech_signal'] = 'NEUTRAL'
    
    # Strong VRP signals (RSI + MA confirmation)
    strong_negative_vrp = (
        (market_data['vrp_rsi_14d'] < VRP_RSI_OVERSOLD) & 
        (~market_data['vrp_above_ma10']) &
        (~market_data['vrp_ma_momentum_positive'])
    )
    market_data.loc[strong_negative_vrp, 'vrp_tech_signal'] = 'STRONG_NEGATIVE_VRP'
    
    strong_positive_vrp = (
        (market_data['vrp_rsi_14d'] > VRP_RSI_OVERBOUGHT) & 
        (market_data['vrp_above_ma10']) &
        (market_data['vrp_ma_momentum_positive'])
    )
    market_data.loc[strong_positive_vrp, 'vrp_tech_signal'] = 'STRONG_POSITIVE_VRP'
    
    # Moderate VRP signals (RSI or MA)
    moderate_negative_vrp = (
        (market_data['vrp_rsi_14d'] < VRP_RSI_OVERSOLD) | 
        ((~market_data['vrp_above_ma10']) & (~market_data['vrp_above_ma20']))
    ) & (market_data['vrp_tech_signal'] == 'NEUTRAL')
    market_data.loc[moderate_negative_vrp, 'vrp_tech_signal'] = 'MODERATE_NEGATIVE_VRP'
    
    moderate_positive_vrp = (
        (market_data['vrp_rsi_14d'] > VRP_RSI_OVERBOUGHT) | 
        ((market_data['vrp_above_ma10']) & (market_data['vrp_above_ma20']))
    ) & (market_data['vrp_tech_signal'] == 'NEUTRAL')
    market_data.loc[moderate_positive_vrp, 'vrp_tech_signal'] = 'MODERATE_POSITIVE_VRP'
    
    # VRP divergence signals (price vs RSI)
    market_data['vrp_rsi_divergence'] = 'NONE'
    
    # Bullish divergence: VRP making lower lows, RSI making higher lows
    vrp_lower = market_data['vrp_avg'] < market_data['vrp_avg'].shift(5)
    rsi_higher = market_data['vrp_rsi_14d'] > market_data['vrp_rsi_14d'].shift(5)
    bullish_div = vrp_lower & rsi_higher & (market_data['vrp_rsi_14d'] < 40)
    market_data.loc[bullish_div, 'vrp_rsi_divergence'] = 'BULLISH'
    
    # Bearish divergence: VRP making higher highs, RSI making lower highs
    vrp_higher = market_data['vrp_avg'] > market_data['vrp_avg'].shift(5)
    rsi_lower = market_data['vrp_rsi_14d'] < market_data['vrp_rsi_14d'].shift(5)
    bearish_div = vrp_higher & rsi_lower & (market_data['vrp_rsi_14d'] > 60)
    market_data.loc[bearish_div, 'vrp_rsi_divergence'] = 'BEARISH'
    
    return market_data

def enhance_signal_with_vrp_tech(signal_direction, vrp_tech_signal, vrp_rsi_divergence, confidence_score):
    """
    Enhance trading signal with VRP technical analysis
    
    Args:
        signal_direction (str): Original signal direction (BULLISH/BEARISH)
        vrp_tech_signal (str): VRP technical signal
        vrp_rsi_divergence (str): VRP RSI divergence signal
        confidence_score (float): Original confidence score
    
    Returns:
        tuple: (enhanced_confidence, tech_confirmation, tech_multiplier)
    """
    
    # VRP technical enhancement for CALL-only strategy
    if vrp_tech_signal == 'STRONG_NEGATIVE_VRP':
        # Strong negative VRP = High probability of volatility expansion
        tech_multiplier = 1.4
        tech_confirmation = True
        
    elif vrp_tech_signal == 'MODERATE_NEGATIVE_VRP':
        # Moderate negative VRP = Good probability of volatility expansion
        tech_multiplier = 1.25
        tech_confirmation = True
        
    elif vrp_tech_signal == 'STRONG_POSITIVE_VRP':
        # Strong positive VRP = High probability of volatility contraction
        tech_multiplier = 1.3
        tech_confirmation = True
        
    elif vrp_tech_signal == 'MODERATE_POSITIVE_VRP':
        # Moderate positive VRP = Good probability of volatility contraction
        tech_multiplier = 1.2
        tech_confirmation = True
        
    else:
        # Neutral VRP technical signal
        tech_multiplier = 1.0
        tech_confirmation = False
    
    # Additional enhancement from RSI divergence
    if vrp_rsi_divergence == 'BULLISH':
        tech_multiplier *= 1.15  # Boost for bullish divergence
    elif vrp_rsi_divergence == 'BEARISH':
        tech_multiplier *= 1.1   # Slight boost for bearish divergence
    
    # Enhanced confidence score (capped at 0.95)
    enhanced_confidence = min(0.95, confidence_score * tech_multiplier)
    
    return enhanced_confidence, tech_confirmation, tech_multiplier

def get_vrp_tech_trading_recommendation(vrp_tech_signal, vrp_rsi_14d, vrp_rsi_divergence):
    """
    Get trading recommendation based on VRP technical analysis
    
    Args:
        vrp_tech_signal (str): VRP technical signal category
        vrp_rsi_14d (float): Current VRP RSI value
        vrp_rsi_divergence (str): VRP RSI divergence signal
    
    Returns:
        str: Trading recommendation
    """
    
    base_recommendation = ""
    
    if vrp_tech_signal == 'STRONG_NEGATIVE_VRP':
        base_recommendation = f"🔥 STRONG VRP NEGATIVE - RSI: {vrp_rsi_14d:.1f} - High Vol Expansion Probability - BUY CALLS"
    elif vrp_tech_signal == 'MODERATE_NEGATIVE_VRP':
        base_recommendation = f"⚡ MODERATE VRP NEGATIVE - RSI: {vrp_rsi_14d:.1f} - Vol Expansion Expected - BUY CALLS"
    elif vrp_tech_signal == 'STRONG_POSITIVE_VRP':
        base_recommendation = f"🔥 STRONG VRP POSITIVE - RSI: {vrp_rsi_14d:.1f} - High Vol Contraction Probability - BUY CALLS"
    elif vrp_tech_signal == 'MODERATE_POSITIVE_VRP':
        base_recommendation = f"⚡ MODERATE VRP POSITIVE - RSI: {vrp_rsi_14d:.1f} - Vol Contraction Expected - BUY CALLS"
    else:
        base_recommendation = f"📊 VRP NEUTRAL - RSI: {vrp_rsi_14d:.1f} - No Strong Technical Signal"
    
    # Add divergence information
    if vrp_rsi_divergence == 'BULLISH':
        base_recommendation += " + BULLISH DIVERGENCE"
    elif vrp_rsi_divergence == 'BEARISH':
        base_recommendation += " + BEARISH DIVERGENCE"
    
    return base_recommendation

def analyze_vrp_tech_performance(market_data):
    """
    Analyze VRP technical signal distribution and effectiveness
    
    Args:
        market_data (pd.DataFrame): Market data with VRP technical signals
    
    Returns:
        dict: VRP technical analysis results
    """
    
    # Count VRP technical signals
    tech_counts = market_data['vrp_tech_signal'].value_counts()
    div_counts = market_data['vrp_rsi_divergence'].value_counts()
    
    # VRP RSI statistics
    vrp_rsi_stats = {
        'mean_rsi_14d': market_data['vrp_rsi_14d'].mean(),
        'median_rsi_14d': market_data['vrp_rsi_14d'].median(),
        'min_rsi_14d': market_data['vrp_rsi_14d'].min(),
        'max_rsi_14d': market_data['vrp_rsi_14d'].max(),
        'std_rsi_14d': market_data['vrp_rsi_14d'].std()
    }
    
    # Signal distribution
    signal_distribution = {
        'strong_negative': tech_counts.get('STRONG_NEGATIVE_VRP', 0),
        'moderate_negative': tech_counts.get('MODERATE_NEGATIVE_VRP', 0),
        'neutral': tech_counts.get('NEUTRAL', 0),
        'moderate_positive': tech_counts.get('MODERATE_POSITIVE_VRP', 0),
        'strong_positive': tech_counts.get('STRONG_POSITIVE_VRP', 0)
    }
    
    # Divergence distribution
    divergence_distribution = {
        'bullish_divergence': div_counts.get('BULLISH', 0),
        'bearish_divergence': div_counts.get('BEARISH', 0),
        'no_divergence': div_counts.get('NONE', 0)
    }
    
    return {
        'vrp_rsi_stats': vrp_rsi_stats,
        'signal_distribution': signal_distribution,
        'divergence_distribution': divergence_distribution,
        'total_signals': len(market_data),
        'tech_coverage': (len(market_data) - tech_counts.get('NEUTRAL', 0)) / len(market_data) * 100
    }

if __name__ == "__main__":
    print("VRP Technical Analysis Enhancement Module")
    print("RSI and Moving Average analysis for VRP (Volatility Risk Premium)")
    print("Features:")
    print("- VRP RSI (14-day and 7-day)")
    print("- VRP Moving Averages (5, 10, 20-day)")
    print("- VRP RSI Divergence Detection")
    print("- Technical signal classification")
    print("- Enhanced confidence scoring")
